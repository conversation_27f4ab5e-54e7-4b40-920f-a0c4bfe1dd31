# RMS-Refresh Design System Documentation

## Overview
This document defines the design system for the RMS-Refresh application, establishing patterns for a modern, performant, and consistent enterprise SaaS interface.

## Design Principles

### Core Values
1. **Clarity First** - Information hierarchy drives design decisions
2. **Performance Focused** - Every interaction should feel instant
3. **Consistently Professional** - Enterprise-grade without being boring
4. **Accessibility Built-in** - WCAG AA compliant by default
5. **Data Dense Friendly** - Support complex information elegantly

## Design Tokens

### Color System

#### Semantic Colors
```css
/* Base semantic tokens */
--color-info: 217 91% 60%;        /* Information blue */
--color-success: 142 71% 45%;     /* Success green */
--color-warning: 38 92% 50%;      /* Warning amber */
--color-error: 0 84% 60%;         /* Error red */

/* Surface elevation colors */
--surface-0: var(--background);
--surface-1: /* 2% lighter/darker based on theme */
--surface-2: /* 4% lighter/darker based on theme */
--surface-3: /* 6% lighter/darker based on theme */
--surface-4: /* 8% lighter/darker based on theme */
```

### Spacing Scale
Based on 4px baseline grid:
- `space-0`: 0px
- `space-1`: 4px
- `space-2`: 8px
- `space-3`: 12px
- `space-4`: 16px
- `space-5`: 20px
- `space-6`: 24px
- `space-8`: 32px
- `space-10`: 40px
- `space-12`: 48px
- `space-16`: 64px

### Typography Scale
```css
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
```

### Border Radius
```css
--radius-xs: 0.125rem;  /* 2px */
--radius-sm: 0.25rem;   /* 4px */
--radius-md: 0.375rem;  /* 6px */
--radius-lg: 0.5rem;    /* 8px */
--radius-xl: 0.75rem;   /* 12px */
--radius-2xl: 1rem;     /* 16px */
--radius-full: 9999px;
```

### Motion Tokens
```css
/* Easings */
--ease-standard: cubic-bezier(0.4, 0, 0.2, 1);
--ease-spring: cubic-bezier(0.22, 1, 0.36, 1);
--ease-snap: cubic-bezier(0.34, 1.56, 0.64, 1);

/* Durations */
--duration-instant: 120ms;
--duration-fast: 200ms;
--duration-moderate: 320ms;
--duration-slow: 420ms;
```

### Elevation (Shadows)
```css
--shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
```

## Component Architecture

### Directory Structure
```
src/design-system/
├── tokens/
│   ├── colors.ts
│   ├── spacing.ts
│   ├── typography.ts
│   └── motion.ts
├── primitives/
│   ├── StatCard.tsx
│   ├── Panel.tsx
│   ├── DataCard.tsx
│   ├── MetricDelta.tsx
│   └── BadgePill.tsx
├── patterns/
│   ├── Page.tsx
│   ├── PageHeader.tsx
│   ├── Section.tsx
│   ├── SplitPane.tsx
│   └── KPIGroup.tsx
├── feedback/
│   ├── SkeletonCard.tsx
│   ├── SkeletonRow.tsx
│   ├── SkeletonKPI.tsx
│   ├── EmptyState.tsx
│   ├── ErrorState.tsx
│   └── LoadingOverlay.tsx
├── controls/
│   ├── FilterBar.tsx
│   ├── SegmentedControl.tsx
│   ├── Toolbar.tsx
│   └── SearchInput.tsx
└── charts/
    ├── ChartContainer.tsx
    ├── AreaChart.tsx
    ├── BarChart.tsx
    ├── DonutChart.tsx
    └── Sparkline.tsx
```

## Component Patterns

### Page Layout
```tsx
<Page>
  <PageHeader
    title="Dashboard"
    description="Overview of your recruitment pipeline"
    actions={<Button>Add Candidate</Button>}
  />
  <KPIGroup>
    <StatCard ... />
    <StatCard ... />
  </KPIGroup>
  <Section>
    <Panel>Content</Panel>
  </Section>
</Page>
```

### StatCard Pattern
```tsx
interface StatCardProps {
  title: string;
  value: string | number;
  delta?: {
    value: number;
    trend: 'up' | 'down' | 'neutral';
  };
  icon?: React.ComponentType;
  loading?: boolean;
  href?: string;
}
```

### Panel Pattern
```tsx
interface PanelProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  elevation?: 0 | 1 | 2 | 3 | 4;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}
```

### Empty State Pattern
```tsx
interface EmptyStateProps {
  icon?: React.ComponentType;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
}
```

## Motion Guidelines

### Micro-interactions
1. **Hover States**: Use transform: translateY(-2px) with 200ms spring easing
2. **Focus States**: Clear focus ring with 2px offset
3. **Press States**: Scale(0.98) with 120ms duration
4. **Page Transitions**: Fade-in with 320ms duration

### Performance Rules
- Use transform and opacity for animations (GPU accelerated)
- Avoid animating width, height, or layout properties
- Limit backdrop-filter blur to 12px maximum
- Use will-change sparingly and remove after animation

### Accessibility
- Respect prefers-reduced-motion
- All animations should be optional
- Provide motion-safe and motion-reduce utilities

## Responsive Design

### Breakpoints
```css
--screen-sm: 640px;
--screen-md: 768px;
--screen-lg: 1024px;
--screen-xl: 1280px;
--screen-2xl: 1536px;
```

### Grid System
- 12 column grid on desktop
- 4 column grid on tablet
- 2 column grid on mobile
- Consistent gap spacing using tokens

### Mobile Patterns
- Minimum touch target: 44x44px
- Stack complex layouts vertically
- Use bottom sheets for mobile modals
- Horizontal scroll for overflowing content

## Data Visualization

### Chart Colors
```css
--chart-primary: hsl(217, 91%, 60%);
--chart-secondary: hsl(142, 71%, 45%);
--chart-tertiary: hsl(38, 92%, 50%);
--chart-quaternary: hsl(280, 65%, 60%);
--chart-quinary: hsl(0, 84%, 60%);
```

### Chart Guidelines
- Consistent 16px padding
- Subtle gridlines (#E5E7EB)
- Interactive tooltips with portal rendering
- Responsive sizing with aspect ratios
- Loading skeletons matching chart dimensions

## Dark Mode

### Elevation in Dark Mode
- Surface elevation through lightening, not shadows
- Each elevation level adds 2% lightness
- Subtle borders become more important
- Focus rings should be more prominent

### Contrast Requirements
- Normal text: 4.5:1 minimum
- Large text: 3:1 minimum
- Interactive elements: 3:1 minimum
- Disabled states: 2.5:1 minimum

## Implementation Phases

### Phase 1: Foundation (Current)
1. Design tokens implementation
2. Core primitives (StatCard, Panel, DataCard)
3. Loading and empty states
4. Dashboard modernization

### Phase 2: Rollout
1. Standardize all pages
2. Implement advanced patterns
3. Add virtualization for long lists
4. Command palette integration

### Phase 3: Polish
1. Advanced animations
2. Keyboard shortcuts
3. Accessibility audit
4. Performance optimization

## Usage Examples

### StatCard
```tsx
<StatCard
  title="Total Candidates"
  value={1234}
  delta={{ value: 12, trend: 'up' }}
  icon={Users}
  loading={false}
/>
```

### Panel with Actions
```tsx
<Panel
  title="Recent Activity"
  description="Last 7 days"
  actions={
    <Button size="sm" variant="outline">
      View All
    </Button>
  }
  elevation={1}
>
  {/* Content */}
</Panel>
```

### Empty State
```tsx
<EmptyState
  icon={Inbox}
  title="No candidates yet"
  description="Start building your talent pipeline"
  action={{
    label: "Add First Candidate",
    onClick: handleAddCandidate
  }}
/>
```

## Best Practices

### Performance
- Lazy load heavy components
- Use React.memo for expensive renders
- Implement virtual scrolling for long lists
- Optimize bundle splitting

### Accessibility
- Semantic HTML elements
- ARIA labels where needed
- Keyboard navigation support
- Screen reader announcements

### Consistency
- Use design tokens exclusively
- Follow component patterns
- Maintain visual hierarchy
- Consistent spacing and sizing

## Migration Checklist

For each page migration:
- [ ] Replace ad-hoc styles with design tokens
- [ ] Implement proper loading states
- [ ] Add empty states where applicable
- [ ] Ensure keyboard navigation
- [ ] Test responsive behavior
- [ ] Verify dark mode appearance
- [ ] Add error boundaries
- [ ] Implement lazy loading
- [ ] Update to use new components
- [ ] Performance audit
