# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- **AI Agent System**: Comprehensive AI agent infrastructure with multi-provider LLM support
  - AutonomousAgent with tool calling capabilities
  - Specialized agents: ScreeningAgent, CommunicationAgent, WorkflowAutomationAgent
  - IntelligentDecisionEngine for autonomous decision making
  - Multi-provider LLM integration (Gemini, OpenAI, Groq)
- **Enhanced Tab System**: Modern EnhancedTabs component with lazy loading and improved UX
- **Real-time Architecture**: Migration from polling to WebSocket-based real-time updates
- **Task Management**: Full task management system with CRUD operations and real-time updates
- **Notification System**: Comprehensive notification center with real-time updates
- **Workflow Automation**: Advanced workflow templates with autonomous capabilities
- GitHub Actions CI/CD pipeline for automated testing and deployment
- Automated version bumping and changelog generation workflow
- Database seed script with sample workflows and data
- Comprehensive CI/CD documentation
- Support for Supabase Edge Functions deployment
- Database migration automation via `supabase db push`
- E2E testing with Cypress integration
- Code coverage reporting with Codecov integration

### Changed

- **Architecture Migration**: Converted from polling-based to real-time reactive architecture
- **UI/UX Improvements**: Enhanced tab system with glass morphism effects and improved navigation
- **Workflow Engine**: Upgraded workflow execution with AI agent integration
- **Database Performance**: Optimized queries and indexing for real-time operations
- Updated @sentry/react to v9.36.0 for React 19 compatibility
- Enhanced package.json with database management scripts
- Synchronized local and remote Supabase migration history (2025-07-19)
  - Aligned migration timestamp for recruiter info sync trigger
  - Ensured consistency between local and remote migration versions

### Fixed

- Resolved React 19 dependency conflicts

## [0.0.0] - 2025-07-10

### Added

- Initial project setup with Vite, React, TypeScript, and Tailwind CSS
- Supabase integration for backend services
- Workflow automation system foundation
- Email and calendar functionality
- Message templates system
- Authentication with Supabase Auth
- Real-time updates capability
- Responsive UI with shadcn/ui components
