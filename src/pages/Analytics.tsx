import { AnalyticsDashboard } from "@/components/analytics/AnalyticsDashboard";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw, Loader2, BarChart3 } from "lucide-react";
import { useGenerateAnalyticsData } from "@/hooks/useAnalyticsDataGeneration";
import { PageWrapper } from "@/components/layout/PageWrapper";

const Analytics = () => {
  const generateAnalytics = useGenerateAnalyticsData();

  const handleRefreshAnalytics = async () => {
    try {
      await generateAnalytics.mutateAsync();
    } catch (error) {
      console.error("Failed to refresh analytics:", error);
    }
  };

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
          <div className="flex items-center gap-3 min-w-0">
            <div className="p-2.5 rounded-lg bg-gradient-to-r from-emerald-100 to-emerald-200 dark:from-emerald-900/30 dark:to-emerald-800/30 ring-1 ring-emerald-500/20 text-emerald-900 dark:text-emerald-100 hover:from-emerald-200 hover:to-emerald-300 dark:hover:from-emerald-800/40 dark:hover:to-emerald-700/40 transition-all duration-300 hover:scale-105 hover:rotate-3">
              <BarChart3 className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
            </div>
            <div className="min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
                Analytics
              </h1>
              <p className="text-sm sm:text-base text-muted-foreground truncate">
                Track performance metrics and insights
              </p>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={handleRefreshAnalytics}
              disabled={generateAnalytics.isPending}
              className="w-full sm:w-auto hover-lift transition-all duration-300 border-white/10 hover:bg-white/5 group"
            >
              {generateAnalytics.isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4 group-hover:rotate-180 transition-transform duration-500" />
              )}
              {generateAnalytics.isPending ? "Refreshing..." : "Refresh Analytics"}
            </Button>
          </div>
        </div>
      </div>
      
      {/* Analytics Dashboard Container with Enhanced Glass Effect */}
      <div className="motion-safe:animate-fade-in [animation-delay:150ms]">
        <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
          <AnalyticsDashboard />
        </div>
      </div>
      </div>
    </PageWrapper>
  );
};

export default Analytics;
