import { useState, useEffect, lazy, Suspense } from "react";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { Bot, Wand2, FolderOpen, FileCode, BarChart3, Plug, Sparkles, TestTube, Search, Mail, Zap, GitBranch, CheckCircle, FileSearch, Shield, Activity, Loader2 } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import { useWorkflowConfigurations } from "@/hooks/useWorkflowConfigurations";
import { LazyTabPanel } from "@/components/ui/lazy-tab-panel";
import { PageWrapper } from "@/components/layout/PageWrapper";

// Import only the main canvas component directly (most used tab)
import { AIWorkflowCanvas } from "@/components/ai/AIWorkflowCanvas";

// Lazy load all other heavy components - handle named exports
const WorkflowManager = lazy(() => 
  import("@/components/ai/workflow/WorkflowManager").then(module => ({ default: module.WorkflowManager }))
);
const WorkflowTemplates = lazy(() => 
  import("@/components/ai/workflow/WorkflowTemplates").then(module => ({ default: module.WorkflowTemplates }))
);
const WorkflowAnalytics = lazy(() => 
  import("@/components/ai/workflow/WorkflowAnalytics").then(module => ({ default: module.WorkflowAnalytics }))
);
const WorkflowIntegrations = lazy(() => 
  import("@/components/ai/workflow/WorkflowIntegrations").then(module => ({ default: module.WorkflowIntegrations }))
);
const WorkflowSuggestions = lazy(() => 
  import("@/components/ai/workflow/WorkflowSuggestions").then(module => ({ default: module.WorkflowSuggestions }))
);
const AgentTestPanel = lazy(() => 
  import("@/components/ai/agents/AgentTestPanel").then(module => ({ default: module.AgentTestPanel }))
);
const EnhancedScreeningPanel = lazy(() => 
  import("@/components/ai/agents/EnhancedScreeningPanel").then(module => ({ default: module.EnhancedScreeningPanel }))
);
const CommunicationPanel = lazy(() => 
  import("@/components/ai/agents/CommunicationPanel").then(module => ({ default: module.CommunicationPanel }))
);
const WorkflowAutomationPanel = lazy(() => 
  import("@/components/ai/agents/WorkflowAutomationPanel").then(module => ({ default: module.WorkflowAutomationPanel }))
);
const AutonomousWorkflowManager = lazy(() => 
  import("@/components/ai/workflow/AutonomousWorkflowManager").then(module => ({ default: module.AutonomousWorkflowManager }))
);
const ApprovalDashboard = lazy(() => 
  import("@/components/ai/workflow/ApprovalDashboard").then(module => ({ default: module.ApprovalDashboard }))
);
const AuditDashboard = lazy(() => 
  import("@/components/ai/workflow/AuditDashboard").then(module => ({ default: module.AuditDashboard }))
);
const RiskDashboard = lazy(() => 
  import("@/components/ai/workflow/RiskDashboard").then(module => ({ default: module.RiskDashboard }))
);
const ProductionMonitoringDashboard = lazy(() => 
  import("@/components/ai/workflow/ProductionMonitoringDashboard").then(module => ({ default: module.ProductionMonitoringDashboard }))
);

// Loading state component for tabs
const TabLoadingState = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="flex flex-col items-center gap-3">
      <Loader2 className="h-8 w-8 animate-spin text-primary/60" />
      <p className="text-sm text-muted-foreground">Loading workflow component...</p>
    </div>
  </div>
);

// Define all valid tab keys
type TabKey = "canvas" | "manage" | "templates" | "analytics" | "integrations" | "suggestions" | "agent-test" | "enhanced-screening" | "communication" | "workflow-automation" | "autonomous-workflows" | "approvals" | "audit" | "risk" | "monitoring";
const TAB_VALUES: TabKey[] = ["canvas", "manage", "templates", "analytics", "integrations", "suggestions", "agent-test", "enhanced-screening", "communication", "workflow-automation", "autonomous-workflows", "approvals", "audit", "risk", "monitoring"];

const AIWorkflows = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { data: workflows } = useWorkflowConfigurations();

  // Get edit parameter from URL
  const queryParams = new URLSearchParams(location.search);
  const editParam = queryParams.get("edit");

  const [editWorkflowId, setEditWorkflowId] = useState<string | null>(
    editParam,
  );

  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/ai-workflows",
    defaultTab: "canvas",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "ai-workflows-tab",
  });

  // Update URL with edit parameter when it changes
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    if (editWorkflowId) {
      params.set("edit", editWorkflowId);
    } else {
      params.delete("edit");
    }
    const newSearch = params.toString();
    if (newSearch !== location.search.substring(1)) {
      navigate(`${location.pathname}${newSearch ? `?${newSearch}` : ""}`, { replace: true });
    }
  }, [editWorkflowId, navigate, location.pathname, location.search]);

  // Handle edit workflow parameter
  useEffect(() => {
    if (editParam) {
      handleTabChange("canvas");
      setEditWorkflowId(editParam);
    }
  }, [editParam]);

  const onTabChange = (value: string) => {
    handleTabChange(value);
    if (value === "manage") {
      // Clear edit parameter when switching to manage tab
      setEditWorkflowId(null);
    }
  };

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex items-center gap-3 min-w-0">
          <div className="p-3 rounded-lg bg-gradient-to-r from-rose-100 to-rose-200 dark:from-rose-900/30 dark:to-rose-800/30 ring-1 ring-rose-500/20 text-rose-900 dark:text-rose-100 hover:from-rose-200 hover:to-rose-300 dark:hover:from-rose-800/40 dark:hover:to-rose-700/40 transition-all duration-300 hover:scale-105 hover:rotate-6 group">
            <Bot className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0 transition-colors duration-300" />
          </div>
          <div className="min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              AI Workflows
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground truncate">
              Automate processes with intelligent workflows
            </p>
          </div>
        </div>
      </div>

      {/* Define tabs configuration with proper structure */}
      {(() => {
        const tabs = [
          { value: "canvas", label: "Editor", icon: Wand2 },
          { value: "manage", label: "Manage", icon: FolderOpen },
          { value: "templates", label: "Templates", icon: FileCode },
          { value: "analytics", label: "Analytics", icon: BarChart3 },
          { value: "integrations", label: "Integrate", icon: Plug },
          { value: "suggestions", label: "Suggest", icon: Sparkles },
          { value: "agent-test", label: "Test", icon: TestTube },
          { value: "enhanced-screening", label: "Screen", icon: Search },
          { value: "communication", label: "Comm", icon: Mail },
          { value: "workflow-automation", label: "Automate", icon: Zap },
          { value: "autonomous-workflows", label: "Auto", icon: GitBranch },
          { value: "approvals", label: "Approve", icon: CheckCircle },
          { value: "audit", label: "Audit", icon: FileSearch },
          { value: "risk", label: "Risk", icon: Shield },
          { value: "monitoring", label: "Monitor", icon: Activity },
        ];

        return (
          <EnhancedTabs 
            tabs={tabs} 
            value={activeTab} 
            onValueChange={onTabChange} 
            variant="navigation"
            className="space-y-6"
          >

            <div className="relative min-h-[400px]">
              <TabPanel value="canvas">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <AIWorkflowCanvas editWorkflowId={editWorkflowId} />
                </div>
              </TabPanel>

              <TabPanel value="manage">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <WorkflowManager />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="templates">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <WorkflowTemplates />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="analytics">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <WorkflowAnalytics />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="integrations">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <WorkflowIntegrations />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="suggestions">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <WorkflowSuggestions />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="agent-test">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <AgentTestPanel />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="enhanced-screening">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <EnhancedScreeningPanel />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="communication">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <CommunicationPanel />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="workflow-automation">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <WorkflowAutomationPanel />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="autonomous-workflows">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <AutonomousWorkflowManager />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="approvals">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <ApprovalDashboard />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="audit">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <AuditDashboard />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="risk">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <RiskDashboard />
                  </Suspense>
                </div>
              </TabPanel>

              <TabPanel value="monitoring">
                <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
                  <Suspense fallback={<TabLoadingState />}>
                    <ProductionMonitoringDashboard />
                  </Suspense>
                </div>
              </TabPanel>
            </div>
          </EnhancedTabs>
        );
      })()}
      </div>
    </PageWrapper>
  );
};

export default AIWorkflows;
