import { useEffect } from "react";
import { SearchInterface } from "@/components/search/SearchInterface";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { initializeDatabase } from "@/integrations/supabase/client";
import { Search as SearchIcon, History, TrendingUp, Sparkles } from "lucide-react";
import { PageWrapper } from "@/components/layout/PageWrapper";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// Type definitions for tabs
type TabKey = "universal" | "recent" | "trending" | "ai-powered";
const TAB_VALUES: TabKey[] = ["universal", "recent", "trending", "ai-powered"];

const Search = () => {
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/search",
    defaultTab: "universal",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "search-tab",
  });

  useEffect(() => {
    // Initialize database and assign orphaned candidates
    initializeDatabase();
  }, []);

  const tabs = [
    { value: "universal", label: "Search", icon: SearchIcon },
    { value: "recent", label: "Recent", icon: History },
    { value: "trending", label: "Trending", icon: TrendingUp },
    { value: "ai-powered", label: "AI", icon: Sparkles },
  ];

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
          <div className="flex items-center gap-3 min-w-0">
            <div className="p-2.5 rounded-lg bg-gradient-to-r from-indigo-100 to-indigo-200 dark:from-indigo-900/30 dark:to-indigo-800/30 ring-1 ring-indigo-500/20 text-indigo-900 dark:text-indigo-100 hover:from-indigo-200 hover:to-indigo-300 dark:hover:from-indigo-800/40 dark:hover:to-indigo-700/40 transition-all duration-300 hover:scale-105">
              <SearchIcon className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
            </div>
            <div className="min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
                Search
              </h1>
              <p className="text-sm sm:text-base text-muted-foreground truncate">
                Universal search across candidates, jobs, and content
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Enhanced Tabs for different search modes */}
      <EnhancedTabs tabs={tabs} value={activeTab} onValueChange={handleTabChange} variant="navigation">
        <TabPanel value="universal" className="space-y-4 sm:space-y-6">
          <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
            <div className="relative">
              {/* Subtle glow effect behind search interface */}
              <div
                aria-hidden="true"
                className="pointer-events-none absolute inset-0 -z-10 rounded-xl bg-gradient-to-b from-primary/5 to-transparent blur-xl"
              />
              <SearchInterface />
            </div>
          </div>
        </TabPanel>

        <TabPanel value="recent" className="space-y-4 sm:space-y-6">
          <Card className="glass-card ring-subtle soft-shadow rounded-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Recent Search History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-muted-foreground">
                  Your recent searches will appear here. Start searching to build your history.
                </p>
                {/* Placeholder for recent searches */}
                <div className="grid gap-2">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="p-3 rounded-lg bg-muted/20 animate-pulse">
                      <div className="h-4 bg-muted/30 rounded w-3/4" />
                      <div className="h-3 bg-muted/20 rounded w-1/2 mt-2" />
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="trending" className="space-y-4 sm:space-y-6">
          <Card className="glass-card ring-subtle soft-shadow rounded-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Trending Searches
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Popular searches across your organization.
                </p>
                {/* Sample trending searches */}
                <div className="space-y-3">
                  {[
                    { query: "React developers", count: 42, trend: "up" },
                    { query: "Remote positions", count: 38, trend: "up" },
                    { query: "Senior engineers", count: 31, trend: "stable" },
                    { query: "Product managers", count: 27, trend: "down" },
                  ].map((item, i) => (
                    <div key={i} className="flex items-center justify-between p-3 rounded-lg bg-muted/10 hover:bg-muted/20 transition-colors cursor-pointer">
                      <div className="flex items-center gap-3">
                        <span className="text-lg font-medium">#{i + 1}</span>
                        <span>{item.query}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{item.count} searches</Badge>
                        {item.trend === "up" && <TrendingUp className="h-4 w-4 text-green-500" />}
                        {item.trend === "down" && <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="ai-powered" className="space-y-4 sm:space-y-6">
          <Card className="glass-card ring-subtle soft-shadow rounded-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                AI-Powered Search Assistant
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Let AI help you find the perfect candidates with intelligent search suggestions.
                </p>
                <div className="p-4 rounded-lg bg-gradient-to-br from-violet-500/10 to-purple-500/10 border border-violet-500/20">
                  <div className="flex items-center gap-2 mb-3">
                    <Sparkles className="h-5 w-5 text-violet-500" />
                    <span className="font-medium">Coming Soon</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    AI-powered semantic search, natural language queries, and intelligent candidate matching based on your requirements.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabPanel>
      </EnhancedTabs>
      </div>
    </PageWrapper>
  );
};

export default Search;
