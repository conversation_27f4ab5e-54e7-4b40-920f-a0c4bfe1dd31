import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { CandidateHeader } from "@/components/candidate/CandidateHeader";
import { CandidateFormDialog } from "@/components/candidate/CandidateFormDialog";
import { CandidateProfile } from "@/components/candidate/CandidateProfile";
import { useToast } from "@/hooks/use-toast";
import { useCandidate } from "@/hooks/useCandidate";
import { useAuth } from "@/contexts/AuthContext";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { EmailComposer } from "@/components/communication/EmailComposer";
import { JobMatchingModal } from "@/components/job/JobMatchingModal";
import { useJobs } from "@/hooks/useJobs";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useCreateMessage } from "@/hooks/useCreateMessage";
import { useCreatePipelineCandidate } from "@/hooks/usePipeline";
import { useQueryClient } from "@tanstack/react-query";
import { PipelineService } from "@/services/PipelineService";
import { useCandidateJobs } from "@/hooks/useCandidateJobs";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2 } from "lucide-react";
import { useCreateActivityEntry } from "@/hooks/useCreateActivityEntry";

const CandidateDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user, loading: authLoading } = useAuth();
  const { candidate, isLoading, error } = useCandidate(id || "");
  const { data: jobs = [] } = useJobs();
  const createMessage = useCreateMessage();
  const createPipelineCandidate = useCreatePipelineCandidate();
  const createActivityEntry = useCreateActivityEntry();
  const { data: candidateJobs = [] } = useCandidateJobs(candidate?.id || "");

  const [isMessageDialogOpen, setIsMessageDialogOpen] = useState(false);
  const [isAddToJobDialogOpen, setIsAddToJobDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedJobId, setSelectedJobId] = useState<string>("");

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">Loading...</p>
      </div>
    );
  }

  // Show authentication required message
  if (!user) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">
          Please sign in to view candidate details
        </p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-28" />
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">
          Error loading candidate details
        </p>
      </div>
    );
  }

  if (!candidate) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">Candidate not found</p>
      </div>
    );
  }

  const handleMessageClick = () => {
    setIsMessageDialogOpen(true);
  };

  const handleAddToJobClick = () => {
    setIsAddToJobDialogOpen(true);
  };

  const handleEditClick = () => {
    setIsEditDialogOpen(true);
  };

  const handleAddToJob = async () => {
    if (!selectedJobId || !user || !candidate) return;

    try {
      const selectedJob = jobs.find((job) => job.id === selectedJobId);
      if (!selectedJob) {
        toast({
          title: "Error",
          description: "Selected job not found.",
          variant: "destructive",
        });
        return;
      }

      // Create a pipeline entry for the candidate
      await createPipelineCandidate.mutateAsync({
        user_id: user.id, // Use authenticated user's ID
        job_id: selectedJobId,
        candidate_id: candidate.id,
        stage: "Applied", // Default stage
        rating: 0, // Default rating
        last_activity: new Date().toISOString(),
        source: "Manual", // Default source
        applied_at: new Date().toISOString(),
      });

      // Create an activity entry for this action
      try {
        await createActivityEntry.mutateAsync({
          candidate_id: candidate.id,
          activity_type: "pipeline_move",
          title: "Added to Job Pipeline",
          description: `Candidate ${candidate.name} has been added to job "${selectedJob.title}" pipeline.`,
          metadata: {
            job_id: selectedJobId,
            job_title: selectedJob.title,
            stage: "Applied",
            source: "Manual"
          }
        });
      } catch (activityError) {
        // Log the error but don't show a toast - the main operation succeeded
        console.warn("Failed to create activity entry for pipeline update:", activityError);
      }

      toast({
        title: "Candidate Added to Job",
        description: `${candidate.name} has been added to the ${selectedJob.title} pipeline.`,
      });

      setIsAddToJobDialogOpen(false);
      setSelectedJobId("");
    } catch (error) {
      console.error("Error adding candidate to job:", error);
      
      // Provide more specific error messages
      let errorMessage = "Failed to add candidate to job. Please try again.";
      
      if (error instanceof Error) {
        if (error.message.includes("duplicate key") || error.message.includes("unique_candidate_job_user")) {
          errorMessage = "This candidate is already in the selected job pipeline.";
        } else if (error.message.includes("foreign key")) {
          errorMessage = "Invalid job or candidate selected.";
        } else if (error.message.includes("permission")) {
          errorMessage = "You don't have permission to add candidates to this job.";
        }
      }
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleMessageSuccess = () => {
    setIsMessageDialogOpen(false);
    toast({
      title: "Message Sent",
      description: `Message sent to ${candidate?.name} successfully.`,
    });
  };

  return (
    <div className="space-y-6">
      <CandidateHeader
        candidate={candidate}
        onMessageClick={handleMessageClick}
        onAddToJobClick={handleAddToJobClick}
        onEditClick={handleEditClick}
      />
      <CandidateProfile candidate={candidate} />

      {/* Message Dialog */}
      <Dialog open={isMessageDialogOpen} onOpenChange={setIsMessageDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Send Message to {candidate?.name}</DialogTitle>
          </DialogHeader>
          <EmailComposer
            candidate={candidate}
            onSuccess={handleMessageSuccess}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Candidate Dialog */}
      <CandidateFormDialog
        mode="edit"
        candidate={candidate}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSuccess={() => {
          // Close dialog
          setIsEditDialogOpen(false);
          // Invalidate queries to refresh UI
          queryClient.invalidateQueries({ queryKey: ["candidate", id] });
          // Success toast intentionally omitted here; EditCandidateForm already shows success to avoid duplicates
        }}
      />

      {/* Add to Job Dialog */}
      <Dialog
        open={isAddToJobDialogOpen}
        onOpenChange={setIsAddToJobDialogOpen}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Add {candidate?.name} to Job</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="job-select">Select Job Position</Label>
              <Select value={selectedJobId} onValueChange={setSelectedJobId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a job position" />
                </SelectTrigger>
                <SelectContent>
                  {jobs.length === 0 ? (
                    <SelectItem value="no-jobs" disabled>
                      No active jobs available
                    </SelectItem>
                  ) : (
                    jobs.map((job) => {
                      const isAlreadyAdded = candidateJobs.some(
                        (cj) => cj.job_id === job.id
                      );
                      const existingApplication = candidateJobs.find(
                        (cj) => cj.job_id === job.id
                      );
                      
                      return (
                        <SelectItem 
                          key={job.id} 
                          value={job.id}
                          disabled={isAlreadyAdded}
                        >
                          <div className="flex items-center justify-between w-full">
                            <span className={isAlreadyAdded ? "text-muted-foreground" : ""}>
                              {job.title} - {job.department}
                            </span>
                            {isAlreadyAdded && (
                              <div className="flex items-center gap-2 ml-2">
                                <CheckCircle2 className="h-4 w-4 text-green-500" />
                                <Badge variant="secondary" className="text-xs">
                                  {existingApplication?.stage || "Applied"}
                                </Badge>
                              </div>
                            )}
                          </div>
                        </SelectItem>
                      );
                    })
                  )}
                </SelectContent>
              </Select>
            </div>
            {candidateJobs.length > 0 && (
              <div className="text-sm text-muted-foreground">
                <p className="flex items-center gap-1">
                  <CheckCircle2 className="h-3 w-3" />
                  {candidate?.name} is already in {candidateJobs.length} job pipeline{candidateJobs.length > 1 ? 's' : ''}.
                </p>
                <div className="mt-2 space-y-1">
                  {candidateJobs.map((cj) => (
                    <Button
                      key={cj.id}
                      variant="link"
                      size="sm"
                      className="h-auto p-0 text-xs"
                      onClick={() => {
                        setIsAddToJobDialogOpen(false);
                        navigate(`/jobs/${cj.job_id}/applicants`);
                      }}
                    >
                      View in {cj.job_title || 'Job'} pipeline →
                    </Button>
                  ))}
                </div>
              </div>
            )}
            <div className="flex gap-2">
              <Button
                onClick={handleAddToJob}
                disabled={
                  !selectedJobId ||
                  createPipelineCandidate.isPending ||
                  createActivityEntry.isPending ||
                  jobs.length === 0
                }
                className="flex-1"
              >
                {createPipelineCandidate.isPending ? "Adding..." : "Add to Job"}
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsAddToJobDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CandidateDetails;
