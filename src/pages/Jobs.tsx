import { Enhanced<PERSON>ab<PERSON>, Tab<PERSON>anelWithCard } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { PageWrapper } from "@/components/layout/PageWrapper";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus, GitBranch, BarChart3, Plug } from "lucide-react";
import { JobFormDialog } from "@/components/job/JobFormDialog";
import { Search, Filter, Briefcase, FileText } from "lucide-react";
import { JobList } from "@/components/job/JobList";
import { Button } from "@/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { JobFilters } from "@/components/job/JobFilters";
import { JobAnalytics } from "@/components/job/JobAnalytics";
import { JobPipeline } from "@/components/job/JobPipeline";
import { JobIntegrations } from "@/components/job/JobIntegrations";
import {
  JobFilters as JobFiltersType,
  DEFAULT_FILTERS,
} from "@/types/jobFilters";
import { useDebounce } from "@/hooks/useDebounce";

// Type definitions for tabs
type TabKey = "active" | "pipeline" | "analytics" | "integrations";
const TAB_VALUES: TabKey[] = ["active", "pipeline", "analytics", "integrations"];

const Jobs = () => {
  const [isPostDialogOpen, setIsPostDialogOpen] = useState(false);
  const [filters, setFilters] = useState<JobFiltersType>(DEFAULT_FILTERS);
  const [searchQuery, setSearchQuery] = useState("");
  
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/jobs",
    defaultTab: "active",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "jobs-tab",
  });

  // TODO: Get actual counts from data
  const activeJobCount = 0; // Will be populated from useJobs hook
  
  const tabs = [
    { value: "active", label: "Active", icon: Briefcase, badge: activeJobCount > 0 ? activeJobCount : undefined },
    { value: "pipeline", label: "Pipeline", icon: GitBranch },
    { value: "analytics", label: "Analytics", icon: BarChart3 },
    { value: "integrate", label: "Integrate", icon: Plug },
  ];

  // Debounce search query for better performance
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Update filters when debounced search query changes
  useEffect(() => {
    setFilters((prev) => ({ ...prev, search: debouncedSearchQuery }));
  }, [debouncedSearchQuery]);

  // Handle filter changes
  const handleFiltersChange = (newFilters: JobFiltersType) => {
    setFilters(newFilters);
    // Update search query if it changed via filters
    if (newFilters.search !== searchQuery) {
      setSearchQuery(newFilters.search || "");
    }
  };

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  };
  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
          <div className="flex items-center gap-3 min-w-0">
            <div className="p-2 rounded-lg bg-gradient-to-r from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/30 ring-1 ring-green-500/20 text-green-900 dark:text-green-100 hover:from-green-200 hover:to-green-300 dark:hover:from-green-800/40 dark:hover:to-green-700/40 transition-all duration-300">
              <Briefcase className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
            </div>
            <div className="min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">Jobs</h1>
              <p className="text-sm sm:text-base text-muted-foreground truncate">
                Manage job postings and requirements
              </p>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" className="w-full sm:w-auto">
                  <Filter className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">Filters</span>
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Job Filters</SheetTitle>
                </SheetHeader>
                <JobFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                />
              </SheetContent>
            </Sheet>
            <Dialog open={isPostDialogOpen} onOpenChange={setIsPostDialogOpen}>
              <DialogTrigger asChild>
                <Button className="w-full sm:w-auto hover-lift transition-all duration-300 bg-primary/90 hover:bg-primary shadow-soft">
                  <Plus className="w-4 h-4 mr-2" />
                  Post Job
                </Button>
              </DialogTrigger>
              <JobFormDialog 
                isOpen={isPostDialogOpen}
                onClose={() => setIsPostDialogOpen(false)}
                onSuccess={() => setIsPostDialogOpen(false)}
                mode="create"
              />
            </Dialog>
          </div>
        </div>
      </div>

      <EnhancedTabs tabs={tabs} value={activeTab} onValueChange={handleTabChange} variant="navigation">
        <div className="relative min-h-[400px]">
          <TabPanelWithCard 
            value="active" 
            title="Active Jobs"
            elevation={1}
          >
            <div className="space-y-3 sm:space-y-2">
              <div className="flex items-center gap-2 mb-3">
                <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
                <span className="text-sm text-muted-foreground">Live</span>
              </div>
              <div className="relative group">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground transition-colors group-focus-within:text-primary" />
                <Input
                  placeholder="Search jobs..."
                  className="pl-10 w-full"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>
              <JobList filters={filters} />
            </div>
          </TabPanelWithCard>

          <TabPanelWithCard value="pipeline" elevation={1}>
            <JobPipeline />
          </TabPanelWithCard>

          <TabPanelWithCard value="analytics" elevation={1}>
            <JobAnalytics />
          </TabPanelWithCard>

          <TabPanelWithCard value="integrations" elevation={1}>
            <JobIntegrations />
          </TabPanelWithCard>
        </div>
      </EnhancedTabs>
      </div>
    </PageWrapper>
  );
};

export default Jobs;
