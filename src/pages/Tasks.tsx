import { TaskManager } from "@/components/tasks/TaskManager";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { PageWrapper } from "@/components/layout/PageWrapper";
import { CheckSquare, Calendar, BarChart3, Users } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useLocation } from "react-router-dom";

// Type definitions for tabs
type TabKey = "tasks" | "calendar" | "analytics" | "team";
const TAB_VALUES: TabKey[] = ["tasks", "calendar", "analytics", "team"];

export default function Tasks() {
  const location = useLocation();
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/tasks",
    defaultTab: "tasks",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "tasks-main-tab",
  });

  // Check if we're on a TaskManager status route and coerce the tab to "tasks"
  const seg = location.pathname.split("/")[2] || "";
  const statusRoutes = new Set(["all", "pending", "in-progress", "completed"]);
  const controlledActiveTab = statusRoutes.has(seg) ? "tasks" : activeTab;

  const tabs = [
    { value: "tasks", label: "Task Manager", icon: CheckSquare },
    { value: "calendar", label: "Calendar View", icon: Calendar },
    { value: "analytics", label: "Analytics", icon: BarChart3 },
    { value: "team", label: "Team Overview", icon: Users },
  ];

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">
        {/* Header Section with Glass Effect */}
        <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
          <div className="flex items-center gap-3 min-w-0">
            <div className="p-2 rounded-lg bg-gradient-to-r from-teal-100 to-teal-200 dark:from-teal-900/30 dark:to-teal-800/30 ring-1 ring-teal-500/20 text-teal-900 dark:text-teal-100 hover:from-teal-200 hover:to-teal-300 dark:hover:from-teal-800/40 dark:hover:to-teal-700/40 transition-all duration-300">
              <CheckSquare className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
            </div>
            <div className="min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">Task Management</h1>
              <p className="text-sm sm:text-base text-muted-foreground truncate">
                Organize, track, and complete your tasks efficiently
              </p>
            </div>
          </div>
        </div>

        <EnhancedTabs tabs={tabs} value={controlledActiveTab} onValueChange={handleTabChange} variant="navigation">
          <TabPanel value="tasks" className="space-y-4 sm:space-y-6">
            <TaskManager />
          </TabPanel>

          <TabPanel value="calendar" className="space-y-4 sm:space-y-6">
            <Card className="glass-card ring-subtle soft-shadow rounded-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Calendar View
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    View your tasks in a calendar layout to better plan your schedule.
                  </p>
                  <div className="p-4 rounded-lg bg-muted/10 border border-muted/20">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Coming Soon</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Calendar view with drag-and-drop task scheduling, recurring tasks, and timeline visualization.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabPanel>

          <TabPanel value="analytics" className="space-y-4 sm:space-y-6">
            <Card className="glass-card ring-subtle soft-shadow rounded-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Task Analytics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 rounded-lg bg-muted/10">
                      <div className="text-2xl font-bold">24</div>
                      <div className="text-sm text-muted-foreground">Tasks Completed This Week</div>
                    </div>
                    <div className="p-4 rounded-lg bg-muted/10">
                      <div className="text-2xl font-bold">87%</div>
                      <div className="text-sm text-muted-foreground">Completion Rate</div>
                    </div>
                    <div className="p-4 rounded-lg bg-muted/10">
                      <div className="text-2xl font-bold">3.2 days</div>
                      <div className="text-sm text-muted-foreground">Average Task Duration</div>
                    </div>
                  </div>
                  <div className="p-4 rounded-lg bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20">
                    <p className="text-sm text-muted-foreground">
                      Detailed analytics and insights about your task productivity patterns will be available here.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabPanel>

          <TabPanel value="team" className="space-y-4 sm:space-y-6">
            <Card className="glass-card ring-subtle soft-shadow rounded-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Team Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    Monitor team task distribution and workload balance.
                  </p>
                  <div className="space-y-3">
                    {[
                      { name: "Sarah Chen", tasks: 8, status: "on-track" },
                      { name: "Mike Johnson", tasks: 12, status: "busy" },
                      { name: "Emily Davis", tasks: 6, status: "available" },
                      { name: "Alex Kim", tasks: 10, status: "on-track" },
                    ].map((member, i) => (
                      <div key={i} className="flex items-center justify-between p-3 rounded-lg bg-muted/10 hover:bg-muted/20 transition-colors">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-sm font-medium">
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </div>
                          <span>{member.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{member.tasks} tasks</span>
                          <Badge 
                            variant={member.status === "busy" ? "destructive" : member.status === "available" ? "success" : "secondary"}
                            className="text-xs"
                          >
                            {member.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabPanel>
        </EnhancedTabs>
      </div>
    </PageWrapper>
  );
}
