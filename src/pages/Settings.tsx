import { EnhancedTabs, TabPanelWithCard } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { Settings as SettingsIcon, User, Shield, Bell, Smartphone, Languages, Activity, Brain, FileCode } from "lucide-react";
import { PageWrapper } from "@/components/layout/PageWrapper";
import { SettingsNotifications } from "@/components/settings/SettingsNotifications";
import { SettingsAI } from "@/components/settings/SettingsAI";
import { SettingsProfile } from "@/components/settings/SettingsProfile";
import { MessageTemplateManager } from "@/components/message/MessageTemplateManager";
import { SettingsSecurity } from "@/components/settings/SettingsSecurity";
import { SettingsMobile } from "@/components/settings/SettingsMobile";
import { SettingsInternationalization } from "@/components/settings/SettingsInternationalization";
import { SettingsPerformance } from "@/components/settings/SettingsPerformance";
import { PerformanceMonitor } from "@/components/performance/PerformanceMonitor";
import { SystemHealth } from "@/components/dashboard/SystemHealth";

// Type definitions for tabs
type SettingsTab = "profile" | "security" | "notifications" | "mobile" | "i18n" | "performance" | "ai" | "templates";
const SETTINGS_TABS: SettingsTab[] = ["profile", "security", "notifications", "mobile", "i18n", "performance", "ai", "templates"];

// Helper function retained for reference but replaced by standardized hook
const getActiveTabFromPath = (pathname: string): SettingsTab => {
  const segments = pathname.split("/");
  const lastSegment = segments[segments.length - 1];
  return SETTINGS_TABS.includes(lastSegment as SettingsTab) ? (lastSegment as SettingsTab) : "profile";
};

const Settings = () => {
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/settings",
    defaultTab: "profile",
    validTabs: SETTINGS_TABS,
    persistToLocalStorage: true,
    storageKey: "settings-tab",
  });

  const tabs = [
    { value: "profile", label: "Profile", icon: User },
    { value: "security", label: "Security", icon: Shield },
    { value: "notifications", label: "Notifications", icon: Bell },
    { value: "mobile", label: "Mobile", icon: Smartphone },
    { value: "i18n", label: "Language", icon: Languages },
    { value: "performance", label: "Performance", icon: Activity },
    { value: "ai", label: "AI Settings", icon: Brain },
    { value: "templates", label: "Templates", icon: FileCode },
  ];

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">
        {/* Glass morphism header */}
        <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
          <div className="flex items-center gap-3 min-w-0">
            <div className="p-2 rounded-lg bg-gradient-to-r from-pink-100 to-pink-200 dark:from-pink-900/30 dark:to-pink-800/30 ring-1 ring-pink-500/20 text-pink-900 dark:text-pink-100 hover:from-pink-200 hover:to-pink-300 dark:hover:from-pink-800/40 dark:hover:to-pink-700/40 transition-all duration-300">
              <SettingsIcon className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
            </div>
            <div className="min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">Settings</h1>
              <p className="text-sm sm:text-base text-muted-foreground truncate">
                Manage your account settings and preferences
              </p>
            </div>
          </div>
        </div>

        {/* Tabs with consistent navigation variant styling */}
        <EnhancedTabs tabs={tabs} value={activeTab} onValueChange={handleTabChange} variant="navigation">
          <div className="relative min-h-[400px]">
            <TabPanelWithCard 
              value="profile" 
              title="Profile Settings"
              elevation={1}
            >
              <SettingsProfile />
            </TabPanelWithCard>

            <TabPanelWithCard 
              value="security" 
              title="Security Settings"
              elevation={1}
            >
              <SettingsSecurity />
            </TabPanelWithCard>

            <TabPanelWithCard 
              value="notifications" 
              title="Notification Preferences"
              elevation={1}
            >
              <SettingsNotifications />
            </TabPanelWithCard>

            <TabPanelWithCard 
              value="mobile" 
              title="Mobile Settings"
              elevation={1}
            >
              <SettingsMobile />
            </TabPanelWithCard>

            <TabPanelWithCard 
              value="i18n" 
              title="Internationalization"
              elevation={1}
            >
              <SettingsInternationalization />
            </TabPanelWithCard>

            <TabPanelWithCard 
              value="performance" 
              title="Performance Settings"
              elevation={1}
            >
              <div className="grid gap-6 lg:grid-cols-12">
                <div className="lg:col-span-8">
                  <SettingsPerformance />
                </div>
                <div className="lg:col-span-4 space-y-6">
                  <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift">
                    <PerformanceMonitor />
                  </div>
                  <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift">
                    <SystemHealth />
                  </div>
                </div>
              </div>
            </TabPanelWithCard>

            <TabPanelWithCard 
              value="ai" 
              title="AI Configuration"
              elevation={1}
            >
              <SettingsAI />
            </TabPanelWithCard>

            <TabPanelWithCard 
              value="templates" 
              title="Message Templates"
              elevation={1}
            >
              <MessageTemplateManager />
            </TabPanelWithCard>
          </div>
        </EnhancedTabs>
      </div>
    </PageWrapper>
  );
};

export default Settings;
