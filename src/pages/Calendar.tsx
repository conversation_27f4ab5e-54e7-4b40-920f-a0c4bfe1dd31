import React, { useContext, useState, useEffect } from "react";
import { CalendarView } from "@/components/calendar/CalendarView";
import { UpcomingEvents } from "@/components/calendar/UpcomingEvents";
import { TaskCalendarWidget } from "@/components/tasks/TaskCalendarWidget";
import { LocalEventsContext } from "@/components/layout/AppLayout";
import { PageWrapper } from "@/components/layout/PageWrapper";
import { Menu, X, ChevronRight, ChevronLeft, Calendar as CalendarIcon } from "lucide-react";

const Calendar = () => {
  const { localEvents, setLocalEvents } = useContext(LocalEventsContext);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date(),
  );
  // Initialize drawer state: default to closed unless explicitly opened before
  const [isDrawerOpen, setIsDrawerOpen] = useState(() => {
    // Check localStorage first - if user has explicitly set a preference, respect it
    const savedState = localStorage.getItem("isDrawerOpen");
    if (savedState !== null) {
      return savedState === "true";
    }
    // Default to closed
    return false;
  });

  // Function to toggle drawer state
  const toggleDrawer = () => {
    setIsDrawerOpen((prev) => {
      const newState = !prev;
      localStorage.setItem("isDrawerOpen", String(newState));
      return newState;
    });
  };

  // Handle escape key to close drawer on mobile
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isDrawerOpen && window.innerWidth < 1024) {
        toggleDrawer();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isDrawerOpen]);

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
          <div className="flex items-center gap-3 min-w-0">
            <div className="p-2.5 rounded-lg bg-gradient-to-r from-cyan-100 to-cyan-200 dark:from-cyan-900/30 dark:to-cyan-800/30 ring-1 ring-cyan-500/20 text-cyan-900 dark:text-cyan-100 hover:from-cyan-200 hover:to-cyan-300 dark:hover:from-cyan-800/40 dark:hover:to-cyan-700/40 transition-all duration-300 hover:scale-105">
              <CalendarIcon className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
            </div>
            <div className="min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">Calendar</h1>
              <p className="text-sm sm:text-base text-muted-foreground truncate">
                Schedule and manage appointments
              </p>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            {/* Desktop toggle button - enhanced with glass effect */}
            <button
              className="hidden lg:flex items-center gap-2 px-4 py-2.5 glass-card ring-subtle soft-shadow rounded-lg hover-lift transition-all duration-300 z-10 w-full sm:w-auto"
              onClick={toggleDrawer}
              aria-label={
                isDrawerOpen ? "Hide tasks sidebar" : "Show tasks sidebar"
              }
            >
              {isDrawerOpen ? (
                <>
                  <span className="text-sm font-medium">Hide Tasks</span>
                  <ChevronRight className="h-4 w-4" />
                </>
              ) : (
                <>
                  <ChevronLeft className="h-4 w-4" />
                  <span className="text-sm font-medium">Show Tasks</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-6 sm:gap-8 motion-safe:animate-fade-in [animation-delay:120ms]">
        {/* Main calendar area - full width on large screens */}
        <div className="flex-1 min-w-0 relative glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift" id="calendar-container">
          <CalendarView
            onEventsChange={setLocalEvents}
            onDateSelect={(date) => setSelectedDate(date || undefined)}
          />


          {/* Toggle button for drawer - enhanced with glass effect */}
          <button
            className="fixed bottom-4 right-16 lg:hidden bg-gradient-to-br from-primary to-primary/80 p-3 rounded-full shadow-elevated z-40 hover:from-primary/90 hover:to-primary/70 transition-all duration-300 hover:scale-105"
            onClick={() => {
              toggleDrawer();
              // Restore focus to calendar for accessibility
              if (isDrawerOpen) {
                // When closing the drawer, return focus to main content
                const calendarContainer =
                  document.getElementById("calendar-container");
                if (calendarContainer) {
                  calendarContainer.setAttribute("tabIndex", "-1");
                  calendarContainer.focus();
                  // Remove tabIndex after focus
                  setTimeout(
                    () => calendarContainer.removeAttribute("tabIndex"),
                    100,
                  );
                }
              }
            }}
          >
            {isDrawerOpen ? (
              <X className="h-5 w-5 text-white" />
            ) : (
              <Menu className="h-5 w-5 text-white" />
            )}
          </button>
        </div>

        {/* Mobile overlay with backdrop blur */}
        {isDrawerOpen && (
          <div
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-20 lg:hidden transition-opacity duration-300"
            onClick={toggleDrawer}
            aria-hidden="true"
          />
        )}

        {/* Task Drawer container - enhanced with glass effect on desktop */}
        <aside
          className={`
            fixed lg:relative top-0 right-0 h-full lg:h-auto
            w-full sm:w-96 lg:w-80 xl:w-96
            bg-white lg:bg-transparent
            shadow-xl lg:shadow-none
            transform transition-transform duration-300 ease-in-out
            ${isDrawerOpen ? "translate-x-0" : "translate-x-full"}
            lg:transform-none lg:transition-none
            ${!isDrawerOpen && "lg:hidden"}
            overflow-y-auto z-30
            lg:glass-card lg:ring-subtle lg:soft-shadow lg:rounded-xl lg:p-3 lg:hover-lift
          `}
          aria-label="Task sidebar"
          aria-hidden={!isDrawerOpen}
        >
          {/* Mobile close button - enhanced styling */}
          <div className="lg:hidden sticky top-0 bg-gradient-to-b from-white to-gray-50/90 backdrop-blur-sm border-b p-4 flex justify-between items-center">
            <h2 className="text-lg font-semibold tracking-tight">Tasks</h2>
            <button
              onClick={toggleDrawer}
              className="p-2 hover:bg-gray-100/80 rounded-lg transition-all duration-300 hover:scale-105"
              aria-label="Close tasks sidebar"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="lg:bg-transparent">
            <TaskCalendarWidget
              selectedDate={selectedDate}
              showOverdue={true}
              showDueToday={true}
              showSelectedDate={true}
            />
          </div>
        </aside>
      </div>
      </div>
    </PageWrapper>
  );
};

export default Calendar;
