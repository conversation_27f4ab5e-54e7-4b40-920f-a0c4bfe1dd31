import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { DashboardStats } from "@/components/dashboard/DashboardStats";
import { RecentCandidates } from "@/components/dashboard/RecentCandidates";
import { RecentJobs } from "@/components/dashboard/RecentJobs";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { HiringPipeline } from "@/components/analytics/HiringPipeline";
import { UpcomingEvents } from "@/components/calendar/UpcomingEvents";
import { TaskDashboardWidget } from "@/components/tasks/TaskDashboardWidget";
import {
  DashboardFiltersProvider,
  useDashboardFilters,
} from "@/contexts/DashboardFiltersContext";
import React, { useContext } from "react";
import { LocalEventsContext } from "@/components/layout/AppLayout";
import { PageWrapper } from "@/components/layout/PageWrapper";

function DashboardContent() {
  const { componentVisibility } = useDashboardFilters();
  const { localEvents, setLocalEvents } = useContext(LocalEventsContext);

  // Calculate if we have any main content to show
  const hasMainContent =
    componentVisibility.recentCandidates ||
    componentVisibility.recentJobs ||
    componentVisibility.hiringPipeline;

  // Calculate if we have any sidebar content to show
  const hasSidebarContent =
    componentVisibility.quickActions ||
    componentVisibility.upcomingEvents ||
    componentVisibility.taskWidget;

  return (
    <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">

      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up tracking-tight">
        <DashboardHeader />
      </div>

      {componentVisibility.dashboardStats && (
        <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 sm:p-3 hover-lift motion-safe:animate-fade-in [animation-delay:100ms]">
          <DashboardStats />
        </div>
      )}

      {(hasMainContent || hasSidebarContent) && (
        <div
          className={`grid gap-6 sm:gap-8 ${hasMainContent && hasSidebarContent ? "lg:grid-cols-12" : ""} fade-up`}
        >
          {/* Main content area - takes up 8 columns on large screens when sidebar is present */}
          {hasMainContent && (
            <div
              className={`${hasSidebarContent ? "lg:col-span-8" : ""} space-y-6 sm:space-y-8 min-w-0 rounded-2xl ring-subtle soft-shadow hover-lift p-2 sm:p-3 bg-background/60`}
            >
              {/* Recent Candidates and Jobs - stack on mobile, side by side on larger screens */}
              {(componentVisibility.recentCandidates ||
                componentVisibility.recentJobs) && (
                <div
                  className={`grid gap-6 sm:gap-8 ${
                    componentVisibility.recentCandidates &&
                    componentVisibility.recentJobs
                      ? "sm:grid-cols-2"
                      : ""
                  } motion-safe:[&>*]:animate-fade-in motion-safe:[&>*:nth-child(1)]:[animation-delay:120ms] motion-safe:[&>*:nth-child(2)]:[animation-delay:200ms]`}
                >
                  {componentVisibility.recentCandidates && (
                    <div className="min-w-0">
                      <RecentCandidates />
                    </div>
                  )}
                  {componentVisibility.recentJobs && (
                    <div className="min-w-0">
                      <RecentJobs />
                    </div>
                  )}
                </div>
              )}

              {/* Hiring Pipeline gets full width */}
              {componentVisibility.hiringPipeline && (
                <div className="min-w-0 hover-lift motion-safe:animate-fade-in [animation-delay:140ms]">
                  <HiringPipeline />
                </div>
              )}
            </div>
          )}

          {/* Sidebar - takes up 4 columns on large screens, full width on mobile */}
          {hasSidebarContent && (
            <div
              className={`${hasMainContent ? "lg:col-span-4" : ""} space-y-6 sm:space-y-8 min-w-0 rounded-2xl ring-subtle soft-shadow hover-lift p-2 sm:p-3 bg-background/60`}
            >
              {componentVisibility.quickActions && (
                <div className="min-w-0 hover-lift motion-safe:animate-fade-in [animation-delay:160ms]">
                  <QuickActions />
                </div>
              )}
              {componentVisibility.upcomingEvents && (
                <div className="min-w-0 hover-lift motion-safe:animate-fade-in [animation-delay:180ms]">
                  <UpcomingEvents
                    events={localEvents}
                    onDeleteEvent={(eventId) =>
                      setLocalEvents((prev) =>
                        prev.filter((e) => e.id !== eventId),
                      )
                    }
                  />
                </div>
              )}
              {componentVisibility.taskWidget && (
                <div className="min-w-0 hover-lift motion-safe:animate-fade-in [animation-delay:200ms]">
                  <TaskDashboardWidget />
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default function Dashboard() {
  // Removed workflow execution polling - this should be handled by proper background scheduler (Supabase Edge Functions with cron)

  return (
    <PageWrapper>
      <DashboardFiltersProvider>
        <DashboardContent />
      </DashboardFiltersProvider>
    </PageWrapper>
  );
}