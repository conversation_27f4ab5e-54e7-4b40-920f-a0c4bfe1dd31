import { useState, useEffect } from "react";
import { Enhanced<PERSON>abs, TabPanelWithCard } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { PageWrapper } from "@/components/layout/PageWrapper";
import { Button } from "@/components/ui/button";
import { Search, Plus, Filter, Users, List, BarChart3, LineChart, Wand2 } from "lucide-react";
import { initializeDatabase } from "@/integrations/supabase/client";
import { CandidateList } from "@/components/candidate/CandidateList";
import { CandidateFilters } from "@/components/candidate/CandidateFilters";
import { CandidateFormDialog } from "@/components/candidate/CandidateFormDialog";
import { CandidateMetrics } from "@/components/candidate/CandidateMetrics";
import { CandidateAnalytics } from "@/components/candidate/CandidateAnalytics";
import { CandidateTools } from "@/components/candidate/CandidateTools";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, She<PERSON>Header, SheetTitle, SheetTrigger } from "@/components/ui/sheet";

// Type definitions for tabs
type TabKey = "list" | "metrics" | "analytics" | "tools";
const TAB_VALUES: TabKey[] = ["list", "metrics", "analytics", "tools"];

const Candidates = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({});
  
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/candidates",
    defaultTab: "list",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "candidates-tab",
  });

  // TODO: Get actual counts from data
  const candidateCount = 0; // Will be populated from useCandidates hook
  
  const tabs = [
    { value: "list", label: "List", icon: List, badge: candidateCount > 0 ? candidateCount : undefined },
    { value: "metrics", label: "Metrics", icon: BarChart3 },
    { value: "analytics", label: "Analytics", icon: LineChart },
    { value: "tools", label: "Tools", icon: Wand2 },
  ];

  useEffect(() => {
    // Initialize database and assign orphaned candidates
    initializeDatabase();
  }, []);

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
          <div className="flex items-center gap-3 min-w-0">
            <div className="p-2 rounded-lg bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-800/30 dark:to-slate-700/30 ring-1 ring-slate-500/20 text-slate-900 dark:text-slate-100">
              <Users className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
            </div>
            <div className="min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">
                Candidates
              </h1>
              <p className="text-sm sm:text-base text-muted-foreground truncate">
                View and manage candidate profiles
              </p>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" className="w-full sm:w-auto">
                  <Filter className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">Filters</span>
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Candidate Filters</SheetTitle>
                </SheetHeader>
                <CandidateFilters
                  filters={filters}
                  onFiltersChange={setFilters}
                />
              </SheetContent>
            </Sheet>
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="w-full sm:w-auto hover-lift transition-all duration-300 bg-primary/90 hover:bg-primary"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Candidate
            </Button>
          </div>
        </div>
      </div>

      <EnhancedTabs tabs={tabs} value={activeTab} onValueChange={handleTabChange} variant="navigation">
        <div className="relative min-h-[400px]">
          <TabPanelWithCard 
            value="list" 
            title="All Candidates"
            elevation={1}
          >
            <div className="space-y-3 sm:space-y-2">
              <div className="relative group">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground transition-colors group-focus-within:text-primary" />
                <Input
                  placeholder="Search candidates..."
                  className="pl-10 w-full"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <CandidateList searchQuery={searchQuery} filters={filters} />
            </div>
          </TabPanelWithCard>

          <TabPanelWithCard value="metrics" elevation={1}>
            <CandidateMetrics />
          </TabPanelWithCard>

          <TabPanelWithCard value="analytics" elevation={1}>
            <CandidateAnalytics />
          </TabPanelWithCard>

          <TabPanelWithCard value="tools" elevation={1}>
            <CandidateTools />
          </TabPanelWithCard>
        </div>
      </EnhancedTabs>

      <CandidateFormDialog
        mode="add"
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
      />
      </div>
    </PageWrapper>
  );
};

export default Candidates;
