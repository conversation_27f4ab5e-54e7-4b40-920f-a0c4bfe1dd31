import { ReportGenerator } from "@/components/reporting/ReportGenerator";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { PageWrapper } from "@/components/layout/PageWrapper";
import { FileBarChart2, FileText, Clock, Archive, TrendingUp, Calendar, RefreshCw } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

// Type definitions for tabs
type TabKey = "generate" | "templates" | "scheduled" | "history" | "analytics";
const TAB_VALUES: TabKey[] = ["generate", "templates", "scheduled", "history", "analytics"];

export default function Reports() {
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/reports",
    defaultTab: "generate",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "reports-tab",
  });

  const tabs = [
    { value: "generate", label: "Generate Report", icon: FileBarChart2 },
    { value: "templates", label: "Templates", icon: FileText },
    { value: "scheduled", label: "Scheduled", icon: Clock },
    { value: "history", label: "History", icon: Archive },
    { value: "analytics", label: "Analytics", icon: TrendingUp },
  ];

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">
        {/* Header Section with Glass Effect */}
        <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
            <div className="flex items-center gap-3 min-w-0">
              <div className="p-2.5 rounded-lg bg-gradient-to-r from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 ring-1 ring-orange-500/20 text-orange-900 dark:text-orange-100 hover:from-orange-200 hover:to-orange-300 dark:hover:from-orange-800/40 dark:hover:to-orange-700/40 transition-all duration-300">
                <FileBarChart2 className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
              </div>
              <div className="min-w-0">
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">Reports</h1>
                <p className="text-sm sm:text-base text-muted-foreground truncate">
                  Generate and manage recruitment reports and analytics
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
              <Button className="w-full sm:w-auto hover-lift transition-all duration-300 bg-primary/90 hover:bg-primary shadow-soft">
                <RefreshCw className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Refresh Data</span>
                <span className="sm:hidden">Refresh</span>
              </Button>
            </div>
          </div>
        </div>

        <EnhancedTabs tabs={tabs} value={activeTab} onValueChange={handleTabChange} variant="navigation">
          <TabPanel value="generate" className="space-y-4 sm:space-y-6">
            <ReportGenerator />
          </TabPanel>

          <TabPanel value="templates" className="space-y-4 sm:space-y-6">
            <Card className="glass-card ring-subtle soft-shadow rounded-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Report Templates
                </CardTitle>
                <CardDescription>
                  Pre-configured report templates for common use cases
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  {[
                    { name: "Monthly Hiring Summary", description: "Overview of hiring activities and metrics", uses: 145 },
                    { name: "Candidate Pipeline Report", description: "Detailed candidate progression analysis", uses: 89 },
                    { name: "Time-to-Hire Analysis", description: "Track recruitment efficiency metrics", uses: 67 },
                    { name: "Source Effectiveness", description: "Analyze recruitment channel performance", uses: 52 },
                    { name: "Diversity & Inclusion", description: "DEI metrics and compliance reporting", uses: 41 },
                  ].map((template, i) => (
                    <div key={i} className="p-4 rounded-lg bg-muted/10 hover:bg-muted/20 transition-colors cursor-pointer">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="font-medium">{template.name}</div>
                          <div className="text-sm text-muted-foreground">{template.description}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {template.uses} uses
                          </Badge>
                          <Button size="sm" variant="outline">Use</Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabPanel>

          <TabPanel value="scheduled" className="space-y-4 sm:space-y-6">
            <Card className="glass-card ring-subtle soft-shadow rounded-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Scheduled Reports
                </CardTitle>
                <CardDescription>
                  Automated reports that run on a schedule
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: "Weekly Team Update", schedule: "Every Monday at 9:00 AM", status: "active", nextRun: "2 days" },
                    { name: "Monthly Executive Summary", schedule: "First day of month", status: "active", nextRun: "1 week" },
                    { name: "Quarterly Performance Review", schedule: "Quarterly", status: "paused", nextRun: "—" },
                  ].map((report, i) => (
                    <div key={i} className="flex items-center justify-between p-4 rounded-lg bg-muted/10">
                      <div className="space-y-1">
                        <div className="font-medium">{report.name}</div>
                        <div className="text-sm text-muted-foreground flex items-center gap-2">
                          <Calendar className="h-3 w-3" />
                          {report.schedule}
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="text-sm text-muted-foreground">
                          Next: {report.nextRun}
                        </div>
                        <Badge 
                          variant={report.status === "active" ? "success" : "secondary"}
                          className="text-xs"
                        >
                          {report.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  <Button className="w-full" variant="outline">
                    <Clock className="h-4 w-4 mr-2" />
                    Schedule New Report
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabPanel>

          <TabPanel value="history" className="space-y-4 sm:space-y-6">
            <Card className="glass-card ring-subtle soft-shadow rounded-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Archive className="h-5 w-5" />
                  Report History
                </CardTitle>
                <CardDescription>
                  Previously generated reports
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { name: "Q4 2023 Hiring Report", date: "Dec 31, 2023", size: "2.4 MB", type: "PDF" },
                    { name: "November Pipeline Analysis", date: "Nov 30, 2023", size: "1.8 MB", type: "Excel" },
                    { name: "Candidate Source Report", date: "Nov 15, 2023", size: "956 KB", type: "PDF" },
                    { name: "Time-to-Hire Metrics", date: "Nov 1, 2023", size: "1.2 MB", type: "PDF" },
                    { name: "October Summary", date: "Oct 31, 2023", size: "3.1 MB", type: "PDF" },
                  ].map((report, i) => (
                    <div key={i} className="flex items-center justify-between p-3 rounded-lg bg-muted/10 hover:bg-muted/20 transition-colors">
                      <div className="flex items-center gap-3">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium text-sm">{report.name}</div>
                          <div className="text-xs text-muted-foreground">{report.date} • {report.size}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">{report.type}</Badge>
                        <Button size="sm" variant="ghost">Download</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabPanel>

          <TabPanel value="analytics" className="space-y-4 sm:space-y-6">
            <Card className="glass-card ring-subtle soft-shadow rounded-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Report Analytics
                </CardTitle>
                <CardDescription>
                  Insights about your reporting patterns and usage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 rounded-lg bg-muted/10">
                      <div className="text-2xl font-bold">247</div>
                      <div className="text-sm text-muted-foreground">Reports Generated</div>
                      <div className="text-xs text-green-500 mt-1">+12% this month</div>
                    </div>
                    <div className="p-4 rounded-lg bg-muted/10">
                      <div className="text-2xl font-bold">18</div>
                      <div className="text-sm text-muted-foreground">Active Templates</div>
                      <div className="text-xs text-blue-500 mt-1">3 new this week</div>
                    </div>
                    <div className="p-4 rounded-lg bg-muted/10">
                      <div className="text-2xl font-bold">94%</div>
                      <div className="text-sm text-muted-foreground">Schedule Success Rate</div>
                      <div className="text-xs text-muted-foreground mt-1">Last 30 days</div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium">Most Used Reports</h4>
                    <div className="space-y-2">
                      {[
                        { name: "Monthly Hiring Summary", usage: 85 },
                        { name: "Candidate Pipeline", usage: 72 },
                        { name: "Time-to-Hire Analysis", usage: 64 },
                        { name: "Source Effectiveness", usage: 51 },
                      ].map((item, i) => (
                        <div key={i} className="flex items-center gap-3">
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm">{item.name}</span>
                              <span className="text-xs text-muted-foreground">{item.usage}%</span>
                            </div>
                            <div className="h-2 bg-muted/20 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transition-all duration-500"
                                style={{ width: `${item.usage}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabPanel>
        </EnhancedTabs>
      </div>
    </PageWrapper>
  );
}
