import { useState, useMemo, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PageWrapper } from "@/components/layout/PageWrapper";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Mail, Send, Users, Inbox, FileText } from "lucide-react";
import { MessageList } from "@/components/message/MessageList";
import { MessageComposer } from "@/components/message/MessageComposer";
import { EmailComposer } from "@/components/communication/EmailComposer";
import { EmailTemplateManager } from "@/components/communication/EmailTemplateManager";
import { ContactManagement } from "@/components/communication/ContactManagement";
import {
  useUpdateMessage,
  type Message,
} from "@/hooks/useMessages";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

// Type definitions for tabs
type TabKey = "inbox" | "compose" | "templates" | "contacts";
const TAB_VALUES: TabKey[] = ["inbox", "compose", "templates", "contacts"];

const Communications = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const { user } = useAuth();
  const { data: templates = [] } = useMessageTemplates();
  const updateMessage = useUpdateMessage();
  
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/communications",
    defaultTab: "inbox",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "communications-tab",
  });

  // Fetch messages with realtime updates
  const fetchMessages = async () => {
    if (!user) return [];

    const { data, error } = await supabase
      .from("messages")
      .select("*")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const { records: messages = [], isLoading } = useRealtimeCollection<Message>(
    "messages",
    fetchMessages,
    "public",
    user ? `user_id=eq.${user.id}` : undefined,
  );

  // Deduplicate messages before displaying
  const uniqueMessages = useMemo(() => {
    const map = new Map<string, Message>();
    for (const m of messages) {
      // Composite key avoids showing identical seeded messages twice
      const key = `${m.sender_email}|${m.content?.trim() || ""}`;
      const existing = map.get(key);
      if (!existing || new Date(m.created_at) > new Date(existing.created_at)) {
        map.set(key, m);
      }
    }
    return Array.from(map.values());
  }, [messages]);

  // Use unique messages for display
  const displayMessages = uniqueMessages;

  const handleSelectMessage = (message: Message) => {
    setSelectedMessage(message);
    
    // Update URL with message ID
    setSearchParams({ message: message.id });

    // Mark message as read if it's unread
    if (message.status === "unread") {
      updateMessage.mutate({
        id: message.id,
        status: "read",
      });
    }
  };

  const handleMessageUpdate = (updatedMessage: Message) => {
    setSelectedMessage(updatedMessage);
  };

  // Sync selectedMessage with the latest version from messages when realtime updates arrive
  useEffect(() => {
    if (!selectedMessage) return;
    
    const latestMessage = messages.find((m) => m.id === selectedMessage.id);
    if (!latestMessage) return;

    // Check if any relevant fields have changed
    const hasChanged =
      latestMessage.is_starred !== selectedMessage.is_starred ||
      latestMessage.status !== selectedMessage.status ||
      latestMessage.follow_up !== selectedMessage.follow_up ||
      latestMessage.reminder !== selectedMessage.reminder ||
      latestMessage.content !== selectedMessage.content ||
      latestMessage.updated_at !== selectedMessage.updated_at;

    if (hasChanged) {
      setSelectedMessage(latestMessage);
    }
  }, [messages]); // Intentionally exclude selectedMessage to avoid infinite loop

  // Handle deep linking - auto-select message from URL
  useEffect(() => {
    const messageId = searchParams.get('message');
    
    if (messageId && messages.length > 0) {
      const targetMessage = messages.find(m => m.id === messageId);
      if (targetMessage && (!selectedMessage || selectedMessage.id !== messageId)) {
        handleSelectMessage(targetMessage);
        // Navigate to inbox tab if not already there to show the message
        if (activeTab !== 'inbox') {
          navigate({ 
            pathname: '/communications/inbox',
            search: location.search
          });
        }
      }
    }
  }, [searchParams, messages]); // eslint-disable-line react-hooks/exhaustive-deps

  const unreadCount = messages.filter((m) => m.status === "unread").length;
  const templateCount = templates.length;
  
  const tabs = [
    { value: "inbox", label: "Inbox", icon: Inbox, badge: unreadCount > 0 ? unreadCount : undefined },
    { value: "compose", label: "Compose", icon: Send },
    { value: "templates", label: "Templates", icon: FileText, badge: templateCount > 0 ? templateCount : undefined },
    { value: "contacts", label: "Contacts", icon: Users },
  ];

  if (isLoading) {
    return (
      <PageWrapper>
        <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">
          <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6">
            <h1 className="text-3xl font-bold">Communications</h1>
            <div className="text-center p-8">Loading messages...</div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <div className="px-4 sm:px-0 space-y-6 sm:space-y-8">

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
            <div className="flex items-center gap-3 min-w-0">
              <div className="p-2 rounded-lg bg-gradient-to-r from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30 ring-1 ring-purple-500/20 text-purple-900 dark:text-purple-100 hover:from-purple-200 hover:to-purple-300 dark:hover:from-purple-800/40 dark:hover:to-purple-700/40 transition-all duration-300">
                <MessageSquare className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0" />
              </div>
              <div className="min-w-0">
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">Communications</h1>
                <p className="text-sm sm:text-base text-muted-foreground truncate">
                  Manage messages and communication templates
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 min-w-0 bg-background/40 px-3 py-1.5 rounded-lg">
              <MessageSquare className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="text-xs sm:text-sm text-muted-foreground truncate">
                {messages.length} total messages
              </span>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="flex-shrink-0 animate-pulse">
                  {unreadCount} unread
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      <EnhancedTabs tabs={tabs} value={activeTab} onValueChange={handleTabChange} variant="navigation">
        <TabPanel value="inbox" className="space-y-4 sm:space-y-6">
          {/* Mobile: Show either list or conversation, not both */}
          <div className="lg:hidden">
            {!selectedMessage ? (
              <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 sm:p-3 hover-lift">
              <Card className="min-w-0 border-0 bg-transparent shadow-none">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">
                    Conversations ({displayMessages.length})
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <MessageList
                    messages={displayMessages.map((msg) => ({
                      id: msg.id,
                      sender: {
                        name: msg.sender_name,
                        avatar: msg.sender_avatar || "/placeholder.svg",
                        role: msg.sender_role || "Unknown",
                      },
                      content: msg.content,
                      time: new Date(msg.created_at).toLocaleString(),
                      status: msg.status as "read" | "unread",
                      isStarred: msg.is_starred,
                      lastActivity: new Date(msg.updated_at).toLocaleDateString(),
                      followUp: msg.follow_up,
                      reminder: msg.reminder,
                    }))}
                    onSelectMessage={(mockMessage) => {
                      const realMessage = displayMessages.find(
                        (m) => m.id === mockMessage.id,
                      );
                      if (realMessage) handleSelectMessage(realMessage);
                    }}
                    selectedMessageId={selectedMessage?.id}
                  />
                </CardContent>
              </Card>
              </div>
            ) : (
              <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 sm:p-3 hover-lift">
              <Card className="min-w-0 border-0 bg-transparent shadow-none">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => {
                        setSelectedMessage(null);
                        setSearchParams({});
                      }}
                      className="p-1 hover:bg-accent rounded"
                    >
                      ←
                    </button>
                    <CardTitle className="text-lg truncate">
                      {selectedMessage.sender_name}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="flex flex-col h-[calc(100vh-200px)] p-0">
                  <MessageComposer
                    selectedMessage={{
                      id: selectedMessage.id,
                      sender: {
                        name: selectedMessage.sender_name,
                        avatar: selectedMessage.sender_avatar || "/placeholder.svg",
                        role: selectedMessage.sender_role || "Unknown",
                      },
                      candidateId: selectedMessage.candidate_id ?? undefined,
                      content: selectedMessage.content,
                      time: new Date(selectedMessage.created_at).toLocaleString(),
                      status: selectedMessage.status as "read" | "unread",
                      isStarred: selectedMessage.is_starred,
                      lastActivity: new Date(
                        selectedMessage.updated_at,
                      ).toLocaleDateString(),
                      followUp: selectedMessage.follow_up,
                      reminder: selectedMessage.reminder,
                    }}
                    onMessageUpdate={(mockMessage) => {
                      const realMessage = displayMessages.find(
                        (m) => m.id === mockMessage.id,
                      );
                      if (realMessage) {
                        handleMessageUpdate({
                          ...realMessage,
                          // Map UI fields to DB fields for immediate feedback
                          is_starred: mockMessage.isStarred,
                          status: mockMessage.status,
                          follow_up: mockMessage.followUp,
                          reminder: mockMessage.reminder,
                          updated_at: new Date().toISOString(),
                        });
                      }
                    }}
                  />
                </CardContent>
              </Card>
              </div>
            )}
          </div>

          {/* Desktop: Show both side by side */}
          <div className="hidden lg:grid grid-cols-3 gap-6 h-[calc(100vh-200px)]">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 hover-lift col-span-1">
            <Card className="min-w-0 border-0 bg-transparent shadow-none">
              <CardHeader>
                <CardTitle>Conversations ({displayMessages.length})</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <MessageList
                  messages={displayMessages.map((msg) => ({
                    id: msg.id,
                    sender: {
                      name: msg.sender_name,
                      avatar: msg.sender_avatar || "/placeholder.svg",
                      role: msg.sender_role || "Unknown",
                    },
                    candidateId: msg.candidate_id ?? undefined,
                    candidateName: msg.candidates?.name ?? msg.sender_name,
                    content: msg.content,
                    time: new Date(msg.created_at).toLocaleString(),
                    status: msg.status as "read" | "unread",
                    isStarred: msg.is_starred,
                    lastActivity: new Date(msg.updated_at).toLocaleDateString(),
                    followUp: msg.follow_up,
                    reminder: msg.reminder,
                  }))}
                  onSelectMessage={(mockMessage) => {
                    const realMessage = displayMessages.find(
                      (m) => m.id === mockMessage.id,
                    );
                    if (realMessage) handleSelectMessage(realMessage);
                  }}
                  selectedMessageId={selectedMessage?.id}
                />
              </CardContent>
            </Card>
            </div>

            <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 hover-lift col-span-2">
            <Card className="min-w-0 border-0 bg-transparent shadow-none">
              <CardHeader>
                <CardTitle>
                  {selectedMessage ? "Conversation" : "Select a conversation"}
                </CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col h-full p-0">
                <MessageComposer
                  selectedMessage={
                    selectedMessage
                      ? {
                          id: selectedMessage.id,
                          sender: {
                            name: selectedMessage.sender_name,
                            avatar:
                              selectedMessage.sender_avatar || "/placeholder.svg",
                            role: selectedMessage.sender_role || "Unknown",
                          },
                          candidateId: selectedMessage.candidate_id ?? undefined,
                          content: selectedMessage.content,
                          time: new Date(
                            selectedMessage.created_at,
                          ).toLocaleString(),
                          status: selectedMessage.status as "read" | "unread",
                          isStarred: selectedMessage.is_starred,
                          lastActivity: new Date(
                            selectedMessage.updated_at,
                          ).toLocaleDateString(),
                          followUp: selectedMessage.follow_up,
                          reminder: selectedMessage.reminder,
                        }
                      : undefined
                  }
                  onMessageUpdate={(mockMessage) => {
                    const realMessage = displayMessages.find(
                      (m) => m.id === mockMessage.id,
                    );
                    if (realMessage) {
                      handleMessageUpdate({
                        ...realMessage,
                        // Map UI fields to DB fields for immediate feedback
                        is_starred: mockMessage.isStarred,
                        status: mockMessage.status,
                        follow_up: mockMessage.followUp,
                        reminder: mockMessage.reminder,
                        updated_at: new Date().toISOString(),
                      });
                    }
                  }}
                />
              </CardContent>
            </Card>
            </div>
          </div>
        </TabPanel>

        <TabPanel value="compose" className="space-y-4 sm:space-y-6">
          <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 sm:p-3 hover-lift">
            <EmailComposer />
          </div>
        </TabPanel>

        <TabPanel value="templates" className="space-y-4 sm:space-y-6">
          <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 sm:p-3 hover-lift">
            <EmailTemplateManager />
          </div>
        </TabPanel>

        <TabPanel value="contacts" className="space-y-4 sm:space-y-6">
          <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 sm:p-3 hover-lift">
          <Card className="border-0 bg-transparent shadow-none">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <Users className="h-4 w-4 sm:h-5 sm:w-5" />
                Contact Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ContactManagement />
            </CardContent>
          </Card>
          </div>
        </TabPanel>
      </EnhancedTabs>
      </div>
    </PageWrapper>
  );
};

export default Communications;
