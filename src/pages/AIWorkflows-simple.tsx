import { useState, useEffect } from "react";
import { Enhanced<PERSON>abs, TabPanel } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { Bot, Wand2, FolderOpen, FileCode, BarChart3, Plug, Sparkles, TestTube, Search, Mail, Zap, GitBranch, CheckCircle, FileSearch, Shield, Activity } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import { useWorkflowConfigurations } from "@/hooks/useWorkflowConfigurations";

// Import all components directly (no lazy loading for now)
import { AIWorkflowCanvas } from "@/components/ai/AIWorkflowCanvas";
import { WorkflowManager } from "@/components/ai/workflow/WorkflowManager";
import { WorkflowTemplates } from "@/components/ai/workflow/WorkflowTemplates";
import { WorkflowAnalytics } from "@/components/ai/workflow/WorkflowAnalytics";
import { WorkflowIntegrations } from "@/components/ai/workflow/WorkflowIntegrations";
import { WorkflowSuggestions } from "@/components/ai/workflow/WorkflowSuggestions";
import { AgentTestPanel } from "@/components/ai/agents/AgentTestPanel";
import { EnhancedScreeningPanel } from "@/components/ai/agents/EnhancedScreeningPanel";
import { CommunicationPanel } from "@/components/ai/agents/CommunicationPanel";
import { WorkflowAutomationPanel } from "@/components/ai/agents/WorkflowAutomationPanel";
import { AutonomousWorkflowManager } from "@/components/ai/workflow/AutonomousWorkflowManager";
import { ApprovalDashboard } from "@/components/ai/workflow/ApprovalDashboard";
import { AuditDashboard } from "@/components/ai/workflow/AuditDashboard";
import { RiskDashboard } from "@/components/ai/workflow/RiskDashboard";
import { ProductionMonitoringDashboard } from "@/components/ai/workflow/ProductionMonitoringDashboard";

// Define all valid tab keys
type TabKey = "canvas" | "manage" | "templates" | "analytics" | "integrations" | "suggestions" | "agent-test" | "enhanced-screening" | "communication" | "workflow-automation" | "autonomous-workflows" | "approvals" | "audit" | "risk" | "monitoring";
const TAB_VALUES: TabKey[] = ["canvas", "manage", "templates", "analytics", "integrations", "suggestions", "agent-test", "enhanced-screening", "communication", "workflow-automation", "autonomous-workflows", "approvals", "audit", "risk", "monitoring"];

const AIWorkflowsSimple = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { data: workflows } = useWorkflowConfigurations();

  // Get edit parameter from URL
  const queryParams = new URLSearchParams(location.search);
  const editParam = queryParams.get("edit");

  const [editWorkflowId, setEditWorkflowId] = useState<string | null>(
    editParam,
  );

  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/ai-workflows",
    defaultTab: "canvas",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "ai-workflows-tab",
  });

  // Update URL with edit parameter when it changes
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    if (editWorkflowId) {
      params.set("edit", editWorkflowId);
    } else {
      params.delete("edit");
    }
    const newSearch = params.toString();
    if (newSearch !== location.search.substring(1)) {
      navigate(`${location.pathname}${newSearch ? `?${newSearch}` : ""}`, { replace: true });
    }
  }, [editWorkflowId, navigate, location.pathname, location.search]);

  // Handle edit workflow parameter
  useEffect(() => {
    if (editParam) {
      handleTabChange("canvas");
      setEditWorkflowId(editParam);
    }
  }, [editParam]);

  const onTabChange = (value: string) => {
    handleTabChange(value);
    if (value === "manage") {
      // Clear edit parameter when switching to manage tab
      setEditWorkflowId(null);
    }
  };

  const tabs = [
    { value: "canvas", label: "Editor", icon: Wand2 },
    { value: "manage", label: "Manage", icon: FolderOpen },
    { value: "templates", label: "Templates", icon: FileCode },
    { value: "analytics", label: "Analytics", icon: BarChart3 },
    { value: "integrations", label: "Integrations", icon: Plug },
    { value: "suggestions", label: "AI Suggestions", icon: Sparkles },
    { value: "agent-test", label: "Agent Test", icon: TestTube },
    { value: "enhanced-screening", label: "AI Screening", icon: Search },
    { value: "communication", label: "AI Comm", icon: Mail },
    { value: "workflow-automation", label: "Automation", icon: Zap },
    { value: "autonomous-workflows", label: "Autonomous", icon: GitBranch },
    { value: "approvals", label: "Approvals", icon: CheckCircle },
    { value: "audit", label: "Audit", icon: FileSearch },
    { value: "risk", label: "Risk", icon: Shield },
    { value: "monitoring", label: "Monitor", icon: Activity },
  ];

  return (
    <div className="relative isolate min-h-[calc(100vh-4rem)] px-4 sm:px-0 space-y-6 sm:space-y-8">
      {/* Spotlight gradient overhead */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute inset-0 -z-10 bg-spotlight opacity-80"
      />
      {/* Ultra-subtle grid for depth */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute inset-0 -z-10 bg-grid opacity-[0.025]"
      />
      {/* Soft blurred blobs for AI theme - futuristic purple and pink */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute top-0 left-1/4 size-96 rounded-full bg-purple-600/10 blur-3xl"
      />
      <div
        aria-hidden="true"
        className="pointer-events-none absolute bottom-0 right-1/4 size-96 rounded-full bg-pink-600/10 blur-3xl"
      />
      {/* AI-focused animated center glow */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 size-[600px] rounded-full bg-violet-500/8 blur-3xl animate-pulse"
      />
      {/* Additional AI neural network effect */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute top-1/3 right-1/3 size-64 rounded-full bg-blue-500/6 blur-2xl"
      />

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex items-center gap-3 min-w-0">
          <div className="p-3 rounded-lg bg-gradient-to-br from-violet-600/20 to-pink-600/15 ring-1 ring-violet-500/25 hover:from-violet-600/25 hover:to-pink-600/20 transition-all duration-300 hover:scale-105 hover:rotate-6 group">
            <Bot className="w-6 h-6 sm:w-8 sm:h-8 text-primary flex-shrink-0 group-hover:text-violet-400 transition-colors duration-300" />
          </div>
          <div className="min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              AI Workflows
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground truncate">
              Automate processes with intelligent workflows
            </p>
          </div>
        </div>
      </div>

      <EnhancedTabs 
        tabs={tabs} 
        value={activeTab} 
        onValueChange={onTabChange} 
        variant="navigation"
        className="space-y-6"
      >
        <div className="relative min-h-[400px]">
          <TabPanel value="canvas">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <AIWorkflowCanvas editWorkflowId={editWorkflowId} />
            </div>
          </TabPanel>

          <TabPanel value="manage">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <WorkflowManager />
            </div>
          </TabPanel>

          <TabPanel value="templates">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <WorkflowTemplates />
            </div>
          </TabPanel>

          <TabPanel value="analytics">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <WorkflowAnalytics />
            </div>
          </TabPanel>

          <TabPanel value="integrations">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <WorkflowIntegrations />
            </div>
          </TabPanel>

          <TabPanel value="suggestions">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <WorkflowSuggestions />
            </div>
          </TabPanel>

          <TabPanel value="agent-test">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <AgentTestPanel />
            </div>
          </TabPanel>

          <TabPanel value="enhanced-screening">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <EnhancedScreeningPanel />
            </div>
          </TabPanel>

          <TabPanel value="communication">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <CommunicationPanel />
            </div>
          </TabPanel>

          <TabPanel value="workflow-automation">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <WorkflowAutomationPanel />
            </div>
          </TabPanel>

          <TabPanel value="autonomous-workflows">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <AutonomousWorkflowManager />
            </div>
          </TabPanel>

          <TabPanel value="approvals">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <ApprovalDashboard />
            </div>
          </TabPanel>

          <TabPanel value="audit">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <AuditDashboard />
            </div>
          </TabPanel>

          <TabPanel value="risk">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <RiskDashboard />
            </div>
          </TabPanel>

          <TabPanel value="monitoring">
            <div className="glass-card ring-subtle soft-shadow rounded-xl p-3 sm:p-4 hover-lift">
              <ProductionMonitoringDashboard />
            </div>
          </TabPanel>
        </div>
      </EnhancedTabs>
    </div>
  );
};

export default AIWorkflowsSimple;
