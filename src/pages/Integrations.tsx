import { IntegrationHub } from "@/components/integrations/IntegrationHub";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { useTabNavigation } from "@/hooks/useTabNavigation";
import { Plug, Link2, Settings, Activity, Shield, TrendingUp } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

// Type definitions for tabs
type TabKey = "connected" | "available" | "settings" | "logs" | "security";
const TAB_VALUES: TabKey[] = ["connected", "available", "settings", "logs", "security"];

export default function Integrations() {
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/integrations",
    defaultTab: "connected",
    validTabs: TAB_VALUES,
    persistToLocalStorage: true,
    storageKey: "integrations-tab",
  });

  const tabs = [
    { value: "connected", label: "Connected", icon: Link2, badge: 8 },
    { value: "available", label: "Available", icon: Plug, badge: 24 },
    { value: "settings", label: "Settings", icon: Settings },
    { value: "logs", label: "Activity Logs", icon: Activity },
    { value: "security", label: "Security", icon: Shield },
  ];
  return (
    <div className="relative isolate min-h-[calc(100vh-4rem)] -mx-4 sm:-mx-6 lg:-mx-8 -my-4 sm:-my-6 lg:-my-8 overflow-x-hidden">
      {/* Background effects - these fill the entire viewport */}
      {/* Spotlight gradient overhead */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute inset-0 -z-10 bg-spotlight opacity-75"
      />
      {/* Ultra-subtle grid for depth */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute inset-0 -z-10 bg-grid opacity-[0.018]"
      />
      {/* Soft blurred blobs for integrations theme - tech blue and teal */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute top-1/3 left-0 -translate-x-1/2 size-80 rounded-full bg-blue-500/10 blur-3xl"
      />
      <div
        aria-hidden="true"
        className="pointer-events-none absolute bottom-1/3 right-0 translate-x-1/2 size-80 rounded-full bg-teal-500/10 blur-3xl"
      />
      {/* Integration network effect - connected nodes */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 size-96 rounded-full bg-cyan-500/6 blur-3xl animate-pulse"
      />
      <div
        aria-hidden="true"
        className="pointer-events-none absolute top-20 right-1/3 size-64 rounded-full bg-indigo-500/5 blur-2xl"
      />
      <div
        aria-hidden="true"
        className="pointer-events-none absolute bottom-20 left-1/3 size-64 rounded-full bg-green-500/5 blur-2xl"
      />
      
      {/* Content container with padding that matches the negative margins */}
      <div className="px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8 space-y-6 sm:space-y-8">

      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex items-center gap-3 min-w-0">
          <div className="p-2.5 rounded-lg bg-gradient-to-r from-amber-100 to-amber-200 dark:from-amber-900/30 dark:to-amber-800/30 ring-1 ring-amber-500/20 text-amber-900 dark:text-amber-100 hover:from-amber-200 hover:to-amber-300 dark:hover:from-amber-800/40 dark:hover:to-amber-700/40 transition-all duration-300 hover:scale-105 hover:-rotate-6 group">
            <Plug className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0 transition-colors duration-300" />
          </div>
          <div className="min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate bg-gradient-to-r from-blue-600 via-cyan-600 to-teal-600 bg-clip-text text-transparent">
              Integrations
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground truncate">
              Connect your favorite tools and services
            </p>
          </div>
        </div>
      </div>

      {/* Enhanced Tabs for different integration views */}
      <EnhancedTabs tabs={tabs} value={activeTab} onValueChange={handleTabChange} variant="navigation">
        <TabPanel value="connected" className="space-y-4 sm:space-y-6">
          <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift">
            <IntegrationHub />
          </div>
        </TabPanel>

        <TabPanel value="available" className="space-y-4 sm:space-y-6">
          <Card className="glass-card ring-subtle soft-shadow rounded-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plug className="h-5 w-5" />
                Available Integrations
              </CardTitle>
              <CardDescription>
                Discover and connect new integrations to enhance your workflow
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[
                  { name: "Slack", category: "Communication", description: "Team messaging and notifications" },
                  { name: "Google Calendar", category: "Calendar", description: "Schedule interviews and meetings" },
                  { name: "LinkedIn", category: "Social", description: "Profile enrichment and sourcing" },
                  { name: "Indeed", category: "Job Boards", description: "Post jobs and receive applications" },
                  { name: "Greenhouse", category: "ATS", description: "Sync candidate data bi-directionally" },
                  { name: "Zoom", category: "Video", description: "Video interview scheduling" },
                  { name: "DocuSign", category: "Documents", description: "Digital offer letters and contracts" },
                  { name: "Checkr", category: "Background", description: "Background check automation" },
                  { name: "Calendly", category: "Scheduling", description: "Self-service interview scheduling" },
                ].map((integration, i) => (
                  <div key={i} className="p-4 rounded-lg bg-muted/10 hover:bg-muted/20 transition-colors">
                    <div className="space-y-3">
                      <div>
                        <div className="font-medium">{integration.name}</div>
                        <Badge variant="secondary" className="text-xs mt-1">
                          {integration.category}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {integration.description}
                      </p>
                      <Button size="sm" className="w-full">
                        Connect
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="settings" className="space-y-4 sm:space-y-6">
          <Card className="glass-card ring-subtle soft-shadow rounded-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Integration Settings
              </CardTitle>
              <CardDescription>
                Configure global integration preferences and behaviors
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Sync Preferences</h3>
                  <div className="space-y-3">
                    {[
                      { label: "Auto-sync candidate data", description: "Automatically sync new candidates from connected ATS", enabled: true },
                      { label: "Two-way sync", description: "Push updates back to integrated systems", enabled: true },
                      { label: "Real-time updates", description: "Receive instant notifications for changes", enabled: false },
                      { label: "Daily summary emails", description: "Get daily reports of integration activity", enabled: true },
                    ].map((setting, i) => (
                      <div key={i} className="flex items-center justify-between p-3 rounded-lg bg-muted/10">
                        <div className="space-y-0.5">
                          <div className="text-sm font-medium">{setting.label}</div>
                          <div className="text-xs text-muted-foreground">{setting.description}</div>
                        </div>
                        <Switch defaultChecked={setting.enabled} />
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Data Handling</h3>
                  <div className="space-y-3">
                    <div className="p-3 rounded-lg bg-muted/10">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Data retention</span>
                        <Badge variant="outline">90 days</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Synced data is retained for 90 days after disconnection
                      </p>
                    </div>
                    <div className="p-3 rounded-lg bg-muted/10">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Duplicate handling</span>
                        <Badge variant="outline">Merge</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Automatically merge duplicate candidates based on email
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="logs" className="space-y-4 sm:space-y-6">
          <Card className="glass-card ring-subtle soft-shadow rounded-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Activity Logs
              </CardTitle>
              <CardDescription>
                Recent integration activities and sync history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { action: "Candidate synced", integration: "LinkedIn", time: "2 minutes ago", status: "success", details: "John Doe profile imported" },
                  { action: "Job posted", integration: "Indeed", time: "15 minutes ago", status: "success", details: "Senior Developer position" },
                  { action: "Calendar event created", integration: "Google Calendar", time: "1 hour ago", status: "success", details: "Interview with Sarah Chen" },
                  { action: "Background check initiated", integration: "Checkr", time: "2 hours ago", status: "pending", details: "Awaiting candidate consent" },
                  { action: "Sync failed", integration: "Greenhouse", time: "3 hours ago", status: "error", details: "API rate limit exceeded" },
                  { action: "Document sent", integration: "DocuSign", time: "5 hours ago", status: "success", details: "Offer letter to Mike Johnson" },
                  { action: "Meeting scheduled", integration: "Zoom", time: "Yesterday", status: "success", details: "Technical interview room created" },
                  { action: "Bulk import completed", integration: "LinkedIn", time: "Yesterday", status: "success", details: "47 candidates imported" },
                ].map((log, i) => (
                  <div key={i} className="flex items-start justify-between p-3 rounded-lg bg-muted/10 hover:bg-muted/20 transition-colors">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{log.action}</span>
                        <Badge variant="outline" className="text-xs">
                          {log.integration}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">{log.details}</p>
                      <p className="text-xs text-muted-foreground">{log.time}</p>
                    </div>
                    <Badge 
                      variant={log.status === "success" ? "success" : log.status === "error" ? "destructive" : "secondary"}
                      className="text-xs"
                    >
                      {log.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="security" className="space-y-4 sm:space-y-6">
          <Card className="glass-card ring-subtle soft-shadow rounded-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security & Compliance
              </CardTitle>
              <CardDescription>
                Manage API keys, permissions, and compliance settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">API Keys</h3>
                  <div className="space-y-3">
                    {[
                      { name: "Production API Key", created: "Jan 15, 2024", lastUsed: "2 minutes ago", status: "active" },
                      { name: "Development API Key", created: "Dec 1, 2023", lastUsed: "3 days ago", status: "active" },
                      { name: "Legacy API Key", created: "Oct 10, 2023", lastUsed: "30 days ago", status: "inactive" },
                    ].map((key, i) => (
                      <div key={i} className="p-3 rounded-lg bg-muted/10">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <div className="font-medium text-sm">{key.name}</div>
                            <div className="text-xs text-muted-foreground">
                              Created: {key.created} • Last used: {key.lastUsed}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={key.status === "active" ? "success" : "secondary"}
                              className="text-xs"
                            >
                              {key.status}
                            </Badge>
                            <Button size="sm" variant="ghost">Revoke</Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button variant="outline" className="w-full">
                    <Shield className="h-4 w-4 mr-2" />
                    Generate New API Key
                  </Button>
                </div>

                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Compliance</h3>
                  <div className="grid gap-3">
                    <div className="p-3 rounded-lg bg-green-500/10 border border-green-500/20">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500" />
                        <span className="text-sm font-medium">GDPR Compliant</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        All data handling meets GDPR requirements
                      </p>
                    </div>
                    <div className="p-3 rounded-lg bg-green-500/10 border border-green-500/20">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500" />
                        <span className="text-sm font-medium">SOC 2 Type II</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Security controls audited and certified
                      </p>
                    </div>
                    <div className="p-3 rounded-lg bg-green-500/10 border border-green-500/20">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500" />
                        <span className="text-sm font-medium">CCPA Compliant</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        California privacy requirements satisfied
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabPanel>
      </EnhancedTabs>
      </div>
    </div>
  );
}
