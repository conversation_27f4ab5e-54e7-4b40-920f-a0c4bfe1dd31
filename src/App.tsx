import { Toaster } from "@/components/ui/sonner";
import { Toaster as RadixToaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AppLayout } from "@/components/layout/AppLayout";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { ScrollToTop } from "@/components/layout/ScrollToTop";
import Dashboard from "@/pages/Dashboard";
import Jobs from "@/pages/Jobs";
import Search from "@/pages/Search";

import Calendar from "@/pages/Calendar";
import Analytics from "@/pages/Analytics";
import Settings from "@/pages/Settings";
import AIWorkflows from "@/pages/AIWorkflows";
import Auth from "@/pages/Auth";
import Index from "@/pages/Index";
import Candidates from "@/pages/Candidates";
import CandidateDetails from "@/pages/CandidateDetails";
import CandidateDocuments from "@/pages/CandidateDocuments";
import JobDetails from "@/pages/JobDetails";
import JobApplicants from "@/pages/JobApplicants";
import Communications from "@/pages/Communications";
import Integrations from "@/pages/Integrations";
import Tasks from "@/pages/Tasks";
import Reports from "@/pages/Reports";
import { useEffect } from "react";
import { initializeDatabase } from "@/integrations/supabase/client";
import { WorkflowRunnerPage } from "@/components/ai/workflow/WorkflowRunnerPage";
// Stagewise integration removed - no longer supported

// Minimal QueryClient for mutations only (no query configuration needed)
const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      retry: 1, // Retry failed mutations once
    },
  },
});

const App = () => {
  // Initialize database when app loads
  useEffect(() => {
    initializeDatabase();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <TooltipProvider>
          <BrowserRouter>
            <ScrollToTop />
            <AuthProvider>
              <Toaster />
              <RadixToaster />
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/auth" element={<Auth />} />

              {/* All protected routes wrapped in AppLayout */}
              <Route
                path="/*"
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <Routes>
                        <Route path="/dashboard" element={<Dashboard />} />
                        <Route path="/candidates" element={<Candidates />}>
                          {/* Default /candidates -> /candidates/list */}
                          <Route index element={<Navigate to="list" replace />} />
                          {/* Static children for each tab */}
                          <Route path="list" element={null} />
                          <Route path="metrics" element={null} />
                          <Route path="analytics" element={null} />
                          <Route path="tools" element={null} />
                        </Route>
                        <Route path="/candidates/:id" element={<CandidateDetails />}>
                          {/* Default /candidates/:id -> /candidates/:id/profile */}
                          <Route index element={<Navigate to="profile" replace />} />
                          {/* Static children for each tab */}
                          <Route path="profile" element={null} />
                          <Route path="screening" element={null} />
                          <Route path="jobs" element={null} />
                          <Route path="activity" element={null}>
                            {/* Default /candidates/:id/activity -> /candidates/:id/activity/all */}
                            <Route index element={<Navigate to="all" replace />} />
                            {/* Sub-tabs for activity */}
                            <Route path="all" element={null} />
                            <Route path="communications" element={null} />
                            <Route path="meetings" element={null} />
                            <Route path="documents" element={null} />
                          </Route>
                          <Route path="documents" element={null} />
                          <Route path="interviews" element={null} />
                          <Route path="notes" element={null} />
                          <Route path="compare" element={null} />
                        </Route>
                        {/* <Route
                          path="/candidates/:id/profile"
                          element={<CandidateDetails />}
                        />
                        <Route
                          path="/candidates/:id/documents"
                          element={<CandidateDocuments />}
                        /> */}
                        <Route path="/jobs" element={<Jobs />}>
                          {/* Default /jobs -> /jobs/active */}
                          <Route index element={<Navigate to="active" replace />} />
                          {/* Static children for each tab */}
                          <Route path="active" element={null} />
                          <Route path="pipeline" element={null} />
                          <Route path="analytics" element={null} />
                          <Route path="integrations" element={null} />
                        </Route>
                        <Route path="/jobs/:jobId" element={<JobDetails />} />
                        <Route path="/jobs/:jobId/applicants" element={<JobApplicants />} />
                        <Route path="/search" element={<Search />}>
                          {/* Default /search -> /search/universal */}
                          <Route index element={<Navigate to="universal" replace />} />
                          {/* Static children for each tab */}
                          <Route path="universal" element={null} />
                          <Route path="recent" element={null} />
                          <Route path="trending" element={null} />
                          <Route path="ai-powered" element={null} />
                        </Route>
                        <Route path="/communications" element={<Communications />}>
                          {/* Default /communications -> /communications/inbox */}
                          <Route index element={<Navigate to="inbox" replace />} />
                          {/* Static children for each tab */}
                          <Route path="inbox" element={null} />
                          <Route path="compose" element={null} />
                          <Route path="templates" element={null} />
                          <Route path="contacts" element={null} />
                        </Route>
                        {/* Legacy routes redirect to new structure */}
                        <Route path="/messages" element={<Navigate to="/communications/inbox" replace />} />
                        <Route path="/communication" element={<Navigate to="/communications/inbox" replace />} />
                        <Route path="/calendar" element={<Calendar />} />
                        <Route path="/analytics" element={<Analytics />} />
                        <Route path="/ai-workflows" element={<AIWorkflows />}>
                          {/* Default /ai-workflows -> /ai-workflows/canvas */}
                          <Route index element={<Navigate to="canvas" replace />} />
                          {/* Static children for each tab */}
                          <Route path="canvas" element={null} />
                          <Route path="manage" element={null} />
                          <Route path="templates" element={null} />
                          <Route path="analytics" element={null} />
                          <Route path="integrations" element={null} />
                          <Route path="suggestions" element={null} />
                          <Route path="agent-test" element={null} />
                          <Route path="enhanced-screening" element={null} />
                          <Route path="communication" element={null} />
                          <Route path="workflow-automation" element={null} />
                          <Route path="autonomous-workflows" element={null} />
                          <Route path="approvals" element={null} />
                          <Route path="audit" element={null} />
                          <Route path="risk" element={null} />
                          <Route path="monitoring" element={null} />
                        </Route>
                        <Route
                          path="/ai-workflows/run/:id"
                          element={<WorkflowRunnerPage />}
                        />
                        <Route path="/integrations" element={<Integrations />}>
                          {/* Default /integrations -> /integrations/connected */}
                          <Route index element={<Navigate to="connected" replace />} />
                          {/* Static children for each tab */}
                          <Route path="connected" element={null} />
                          <Route path="available" element={null} />
                          <Route path="settings" element={null} />
                          <Route path="logs" element={null} />
                          <Route path="security" element={null} />
                        </Route>
                        <Route path="/tasks" element={<Tasks />}>
                          {/* Default /tasks -> /tasks/tasks */}
                          <Route index element={<Navigate to="tasks" replace />} />
                          {/* Static children for each tab */}
                          <Route path="tasks" element={null} />
                          <Route path="calendar" element={null} />
                          <Route path="analytics" element={null} />
                          <Route path="team" element={null} />
                        </Route>
                        {/* TaskManager status routes (to support deep links from TaskManager tabs) */}
                        <Route path="/tasks/all" element={<Tasks />} />
                        <Route path="/tasks/pending" element={<Tasks />} />
                        <Route path="/tasks/in-progress" element={<Tasks />} />
                        <Route path="/tasks/completed" element={<Tasks />} />
                        <Route path="/reports" element={<Reports />}>
                          {/* Default /reports -> /reports/generate */}
                          <Route index element={<Navigate to="generate" replace />} />
                          {/* Static children for each tab */}
                          <Route path="generate" element={null} />
                          <Route path="templates" element={null} />
                          <Route path="scheduled" element={null} />
                          <Route path="history" element={null} />
                          <Route path="analytics" element={null} />
                        </Route>
                        <Route path="/settings" element={<Settings />}>
                          {/* Default /settings -> /settings/profile */}
                          <Route index element={<Navigate to="profile" replace />} />
                          {/* Static children for each settings tab */}
                          <Route path="profile" element={null} />
                          <Route path="security" element={null} />
                          <Route path="notifications" element={null} />
                          <Route path="mobile" element={null} />
                          <Route path="i18n" element={null} />
                          <Route path="performance" element={null} />
                          <Route path="ai" element={null} />
                          <Route path="templates" element={null} />
                        </Route>
                      </Routes>
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
            </Routes>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
