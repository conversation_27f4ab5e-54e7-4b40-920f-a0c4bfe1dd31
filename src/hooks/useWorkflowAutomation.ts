/**
 * React hook for workflow automation agent functionality
 */

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  WorkflowAutomationAgent, 
  WorkflowContext, 
  AutomationResult,
  BottleneckAnalysis,
  WorkflowOptimization 
} from '@/services/agents/WorkflowAutomationAgent';

interface UseWorkflowAutomationOptions {
  workflowId?: string;
  candidateId?: string;
  jobId?: string;
  automationLevel?: 'manual' | 'assisted' | 'autonomous';
  autoMonitor?: boolean;
  monitoringInterval?: number; // in milliseconds
}

interface UseWorkflowAutomationReturn {
  analyzeWorkflowHealth: (workflowId?: string) => Promise<AutomationResult>;
  optimizeWorkflow: (workflowId?: string, optimizations?: WorkflowOptimization[]) => Promise<AutomationResult>;
  monitorWorkflow: (workflowId: string) => Promise<AutomationResult>;
  isAnalyzing: boolean;
  isOptimizing: boolean;
  isMonitoring: boolean;
  lastAnalysis: AutomationResult | null;
  lastOptimization: AutomationResult | null;
  lastMonitoring: AutomationResult | null;
  bottlenecks: BottleneckAnalysis[];
  optimizations: WorkflowOptimization[];
  error: string | null;
  clearError: () => void;
  startMonitoring: (workflowId: string) => void;
  stopMonitoring: () => void;
}

export function useWorkflowAutomation(options: UseWorkflowAutomationOptions = {}): UseWorkflowAutomationReturn {
  const { user } = useAuth();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [lastAnalysis, setLastAnalysis] = useState<AutomationResult | null>(null);
  const [lastOptimization, setLastOptimization] = useState<AutomationResult | null>(null);
  const [lastMonitoring, setLastMonitoring] = useState<AutomationResult | null>(null);
  const [bottlenecks, setBottlenecks] = useState<BottleneckAnalysis[]>([]);
  const [optimizations, setOptimizations] = useState<WorkflowOptimization[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [monitoringInterval, setMonitoringInterval] = useState<NodeJS.Timeout | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const createContext = useCallback((): WorkflowContext => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    return {
      userId: user.id,
      workflowId: options.workflowId,
      candidateId: options.candidateId,
      jobId: options.jobId,
      priority: 'medium',
      automationLevel: options.automationLevel || 'assisted',
    };
  }, [user, options]);

  const analyzeWorkflowHealth = useCallback(async (workflowId?: string): Promise<AutomationResult> => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const context = createContext();
      const agent = new WorkflowAutomationAgent(context);
      
      const result = await agent.analyzeWorkflowHealth(workflowId);
      setLastAnalysis(result);
      
      if (result.bottlenecksDetected) {
        setBottlenecks(result.bottlenecksDetected);
      }
      
      if (result.optimizations) {
        setOptimizations(result.optimizations);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Workflow analysis failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsAnalyzing(false);
    }
  }, [createContext]);

  const optimizeWorkflow = useCallback(async (
    workflowId?: string, 
    targetOptimizations?: WorkflowOptimization[]
  ): Promise<AutomationResult> => {
    setIsOptimizing(true);
    setError(null);

    try {
      const context = createContext();
      const agent = new WorkflowAutomationAgent(context);
      
      const result = await agent.optimizeWorkflow(workflowId, targetOptimizations);
      setLastOptimization(result);
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Workflow optimization failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsOptimizing(false);
    }
  }, [createContext]);

  const monitorWorkflow = useCallback(async (workflowId: string): Promise<AutomationResult> => {
    setIsMonitoring(true);
    setError(null);

    try {
      const context = createContext();
      const agent = new WorkflowAutomationAgent(context);
      
      const result = await agent.monitorWorkflowExecution(workflowId);
      setLastMonitoring(result);
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Workflow monitoring failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsMonitoring(false);
    }
  }, [createContext]);

  const startMonitoring = useCallback((workflowId: string) => {
    if (monitoringInterval) {
      clearInterval(monitoringInterval);
    }

    const interval = setInterval(async () => {
      try {
        await monitorWorkflow(workflowId);
      } catch (err) {
        console.error('Monitoring error:', err);
      }
    }, options.monitoringInterval || 30000); // Default 30 seconds

    setMonitoringInterval(interval);
  }, [monitorWorkflow, monitoringInterval, options.monitoringInterval]);

  const stopMonitoring = useCallback(() => {
    if (monitoringInterval) {
      clearInterval(monitoringInterval);
      setMonitoringInterval(null);
    }
  }, [monitoringInterval]);

  // Auto-start monitoring if enabled
  useEffect(() => {
    if (options.autoMonitor && options.workflowId) {
      startMonitoring(options.workflowId);
    }

    return () => {
      if (monitoringInterval) {
        clearInterval(monitoringInterval);
      }
    };
  }, [options.autoMonitor, options.workflowId, startMonitoring, monitoringInterval]);

  return {
    analyzeWorkflowHealth,
    optimizeWorkflow,
    monitorWorkflow,
    isAnalyzing,
    isOptimizing,
    isMonitoring,
    lastAnalysis,
    lastOptimization,
    lastMonitoring,
    bottlenecks,
    optimizations,
    error,
    clearError,
    startMonitoring,
    stopMonitoring,
  };
}

// Helper hook for bottleneck analysis utilities
export function useBottleneckAnalysis() {
  const getSeverityColor = useCallback((severity: string): string => {
    switch (severity) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  }, []);

  const getSeverityBadgeVariant = useCallback((severity: string): 'default' | 'secondary' | 'destructive' => {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      default:
        return 'default';
    }
  }, []);

  const getBottleneckIcon = useCallback((type: string): string => {
    switch (type) {
      case 'time_delay': return '⏰';
      case 'resource_constraint': return '👥';
      case 'process_inefficiency': return '⚙️';
      case 'communication_gap': return '💬';
      default: return '⚠️';
    }
  }, []);

  const formatImpact = useCallback((impact: BottleneckAnalysis['impact']) => {
    return {
      candidates: `${impact.candidatesAffected} candidates affected`,
      delay: `${Math.round(impact.averageDelay / 1000 / 60)} min average delay`,
      cost: `$${impact.costImpact.toLocaleString()} estimated cost impact`,
    };
  }, []);

  const prioritizeBottlenecks = useCallback((bottlenecks: BottleneckAnalysis[]) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    
    return [...bottlenecks].sort((a, b) => {
      const severityDiff = (severityOrder[b.severity as keyof typeof severityOrder] || 0) - 
                          (severityOrder[a.severity as keyof typeof severityOrder] || 0);
      
      if (severityDiff !== 0) return severityDiff;
      
      // Secondary sort by impact
      return b.impact.candidatesAffected - a.impact.candidatesAffected;
    });
  }, []);

  return {
    getSeverityColor,
    getSeverityBadgeVariant,
    getBottleneckIcon,
    formatImpact,
    prioritizeBottlenecks,
  };
}

// Helper hook for optimization analysis utilities
export function useOptimizationAnalysis() {
  const getOptimizationIcon = useCallback((type: string): string => {
    switch (type) {
      case 'process_reorder': return '🔄';
      case 'parallel_execution': return '⚡';
      case 'automation_opportunity': return '🤖';
      case 'resource_reallocation': return '📊';
      default: return '💡';
    }
  }, []);

  const calculateROI = useCallback((optimization: WorkflowOptimization) => {
    const { timeReduction, costSavings } = optimization.expectedBenefits;
    const estimatedImplementationCost = 1000; // Base implementation cost
    
    return {
      roi: ((costSavings - estimatedImplementationCost) / estimatedImplementationCost) * 100,
      paybackPeriod: estimatedImplementationCost / (costSavings / 12), // months
      timeReductionPercent: timeReduction,
    };
  }, []);

  const prioritizeOptimizations = useCallback((optimizations: WorkflowOptimization[]) => {
    return [...optimizations].sort((a, b) => {
      const roiA = calculateROI(a).roi;
      const roiB = calculateROI(b).roi;
      
      if (roiB !== roiA) return roiB - roiA;
      
      // Secondary sort by time reduction
      return b.expectedBenefits.timeReduction - a.expectedBenefits.timeReduction;
    });
  }, [calculateROI]);

  const getImplementationComplexity = useCallback((optimization: WorkflowOptimization): {
    level: 'low' | 'medium' | 'high';
    description: string;
    estimatedTime: string;
  } => {
    const stepCount = optimization.implementationSteps.length;
    const riskCount = optimization.risks.length;
    const requirementCount = optimization.requirements.length;
    
    const complexity = stepCount + riskCount + requirementCount;
    
    if (complexity <= 5) {
      return {
        level: 'low',
        description: 'Simple implementation with minimal risks',
        estimatedTime: '1-2 days',
      };
    } else if (complexity <= 10) {
      return {
        level: 'medium',
        description: 'Moderate implementation with some complexity',
        estimatedTime: '3-5 days',
      };
    } else {
      return {
        level: 'high',
        description: 'Complex implementation requiring careful planning',
        estimatedTime: '1-2 weeks',
      };
    }
  }, []);

  return {
    getOptimizationIcon,
    calculateROI,
    prioritizeOptimizations,
    getImplementationComplexity,
  };
}

// Helper hook for workflow monitoring utilities
export function useWorkflowMonitoring() {
  const getHealthScore = useCallback((
    bottlenecks: BottleneckAnalysis[],
    optimizations: WorkflowOptimization[]
  ): {
    score: number;
    status: 'excellent' | 'good' | 'fair' | 'poor';
    factors: string[];
  } => {
    let score = 100;
    const factors: string[] = [];

    // Deduct points for bottlenecks
    bottlenecks.forEach(bottleneck => {
      switch (bottleneck.severity) {
        case 'critical':
          score -= 25;
          factors.push(`Critical bottleneck: ${bottleneck.location}`);
          break;
        case 'high':
          score -= 15;
          factors.push(`High severity bottleneck: ${bottleneck.location}`);
          break;
        case 'medium':
          score -= 8;
          factors.push(`Medium bottleneck: ${bottleneck.location}`);
          break;
        case 'low':
          score -= 3;
          break;
      }
    });

    // Add points for optimization opportunities (indicates room for improvement)
    const highImpactOptimizations = optimizations.filter(o => o.expectedBenefits.timeReduction > 20);
    if (highImpactOptimizations.length > 0) {
      factors.push(`${highImpactOptimizations.length} high-impact optimization opportunities`);
    }

    const finalScore = Math.max(0, Math.min(100, score));
    
    let status: 'excellent' | 'good' | 'fair' | 'poor';
    if (finalScore >= 90) status = 'excellent';
    else if (finalScore >= 75) status = 'good';
    else if (finalScore >= 50) status = 'fair';
    else status = 'poor';

    return { score: finalScore, status, factors };
  }, []);

  const getRecommendedActions = useCallback((
    bottlenecks: BottleneckAnalysis[],
    optimizations: WorkflowOptimization[]
  ): string[] => {
    const actions: string[] = [];

    // Critical bottlenecks first
    const criticalBottlenecks = bottlenecks.filter(b => b.severity === 'critical');
    criticalBottlenecks.forEach(bottleneck => {
      actions.push(`URGENT: Address ${bottleneck.description}`);
    });

    // High-impact optimizations
    const highImpactOptimizations = optimizations
      .filter(o => o.expectedBenefits.timeReduction > 30)
      .slice(0, 3);
    
    highImpactOptimizations.forEach(optimization => {
      actions.push(`Implement: ${optimization.proposedState}`);
    });

    // General recommendations
    if (bottlenecks.length > 5) {
      actions.push('Consider comprehensive workflow redesign');
    }

    return actions.slice(0, 8); // Limit to top 8 actions
  }, []);

  return {
    getHealthScore,
    getRecommendedActions,
  };
}
