import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import {
  TasksService,
  Task,
  CreateTaskData,
  UpdateTaskData,
  TaskFilters,
} from "@/services/TasksService";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useMemo } from "react";

export const useTasks = (filters?: TaskFilters) => {
  const { user } = useAuth();

  // Real-time tasks subscription
  const { records: allTasks = [], isLoading } = useRealtimeCollection(
    "tasks",
    async () => {
      if (!user) return [];

      try {
        return await TasksService.getTasks(user.id, filters);
      } catch (error) {
        console.error("Error in useTasks:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  // Apply filters to tasks (since real-time gives us all records)
  const filteredTasks = useMemo(() => {
    if (!filters) return allTasks;

    return allTasks.filter((task) => {
      if (filters.status && task.status !== filters.status) return false;
      if (filters.priority && task.priority !== filters.priority) return false;
      if (filters.category && task.category !== filters.category) return false;
      if (filters.assignee && task.assignee !== filters.assignee) return false;
      return true;
    });
  }, [allTasks, filters]);

  return { data: filteredTasks, isLoading, error: null };
};

export const useTask = (taskId: string) => {
  const { user } = useAuth();

  // Real-time individual task subscription
  const { records: tasks, isLoading } = useRealtimeCollection(
    "tasks",
    async () => {
      if (!taskId || !user) return [];

      try {
        const task = await TasksService.getTask(taskId);
        return task ? [task] : []; // Wrap in array for useRealtimeCollection
      } catch (error) {
        console.error("Error in useTask:", error);
        return [];
      }
    },
    "public",
    `id=eq.${taskId}`,
  );

  return { data: tasks[0] || null, isLoading, error: null };
};

export const useCreateTask = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (taskData: CreateTaskData) => {
      return await TasksService.createTask(taskData);
    },
    // Success toast intentionally omitted (UI shows context-specific success)
    onError: (error) => {
      console.error("Error creating task:", error);
      toast({
        title: "Error",
        description: "Failed to create task. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateTask = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      taskId,
      updateData,
    }: {
      taskId: string;
      updateData: UpdateTaskData;
    }) => {
      return await TasksService.updateTask(taskId, updateData);
    },
    // Success toast intentionally omitted (UI shows context-specific success)
    onError: (error) => {
      console.error("Error updating task:", error);
      toast({
        title: "Error",
        description: "Failed to update task. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteTask = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (taskId: string) => {
      return await TasksService.deleteTask(taskId);
    },
    // Success toast intentionally omitted (UI shows context-specific success)
    onError: (error) => {
      console.error("Error deleting task:", error);
      toast({
        title: "Error",
        description: "Failed to delete task. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useCompleteTask = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (taskId: string) => {
      return await TasksService.completeTask(taskId);
    },
    // Success toast intentionally omitted (UI shows context-specific success)
    onError: (error) => {
      console.error("Error completing task:", error);
      toast({
        title: "Error",
        description: "Failed to complete task. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useAssignTask = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      taskId,
      assignee,
    }: {
      taskId: string;
      assignee: string;
    }) => {
      return await TasksService.assignTask(taskId, assignee);
    },
    // Success toast intentionally omitted (UI shows context-specific success)
    onError: (error) => {
      console.error("Error assigning task:", error);
      toast({
        title: "Error",
        description: "Failed to assign task. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useOverdueTasks = () => {
  const { user } = useAuth();

  // Real-time tasks subscription for overdue tasks
  const { records: allTasks = [], isLoading } = useRealtimeCollection(
    "tasks",
    async () => {
      if (!user) return [];

      try {
        return await TasksService.getOverdueTasks(user.id);
      } catch (error) {
        console.error("Error in useOverdueTasks:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  // Filter for overdue tasks
  const overdueTasks = useMemo(() => {
    const now = new Date();
    return allTasks.filter(
      (task) =>
        task.due_date &&
        new Date(task.due_date) < now &&
        task.status !== "completed",
    );
  }, [allTasks]);

  return { data: overdueTasks, isLoading, error: null };
};

export const useTasksDueToday = () => {
  const { user } = useAuth();

  // Real-time tasks subscription for tasks due today
  const { records: allTasks = [], isLoading } = useRealtimeCollection(
    "tasks",
    async () => {
      if (!user) return [];

      try {
        return await TasksService.getTasksDueToday(user.id);
      } catch (error) {
        console.error("Error in useTasksDueToday:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  // Filter for tasks due today
  const tasksDueToday = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return allTasks.filter((task) => {
      if (!task.due_date) return false;
      const dueDate = new Date(task.due_date);
      dueDate.setHours(0, 0, 0, 0);
      return dueDate.getTime() === today.getTime();
    });
  }, [allTasks]);

  return { data: tasksDueToday, isLoading, error: null };
};

export const useTaskStats = () => {
  const { user } = useAuth();

  // Real-time task stats subscription
  const { records: taskStatsData = [], isLoading } = useRealtimeCollection(
    "tasks",
    async () => {
      if (!user) return [];

      try {
        const stats = await TasksService.getTaskStats(user.id);
        return stats ? [stats] : []; // Wrap in array for useRealtimeCollection
      } catch (error) {
        console.error("Error in useTaskStats:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: taskStatsData[0] || null, isLoading, error: null };
};

export const useCreateTaskFromTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      userId,
      templateName,
      candidateName,
      jobTitle,
    }: {
      userId: string;
      templateName: string;
      candidateName?: string;
      jobTitle?: string;
    }) => {
      return await TasksService.createTaskFromTemplate(
        userId,
        templateName,
        candidateName,
        jobTitle,
      );
    },
    // Success toast intentionally omitted (UI shows context-specific success)
    onError: (error) => {
      console.error("Error creating task from template:", error);
      toast({
        title: "Error",
        description: "Failed to create task from template. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useBulkUpdateTaskStatus = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      taskIds,
      status,
    }: {
      taskIds: string[];
      status: "pending" | "in-progress" | "completed";
    }) => {
      return await TasksService.bulkUpdateTaskStatus(taskIds, status);
    },
    onSuccess: (_, variables) => {
      toast({
        title: "Tasks Updated",
        description: `Updated status for ${variables.taskIds.length} task${variables.taskIds.length > 1 ? 's' : ''}.`,
      });
    },
    onError: (error) => {
      console.error("Error bulk updating tasks:", error);
      toast({
        title: "Error",
        description: "Failed to update tasks. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useBulkUpdateTaskPriority = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      taskIds,
      priority,
    }: {
      taskIds: string[];
      priority: "low" | "medium" | "high";
    }) => {
      return await TasksService.bulkUpdatePriority(taskIds, priority);
    },
    onSuccess: (_, variables) => {
      toast({
        title: "Priority Updated",
        description: `Updated priority for ${variables.taskIds.length} task${variables.taskIds.length > 1 ? 's' : ''}.`,
      });
    },
    onError: (error) => {
      console.error("Error bulk updating task priority:", error);
      toast({
        title: "Error",
        description: "Failed to update task priority. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useBulkAssignTasks = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      taskIds,
      assignee,
    }: {
      taskIds: string[];
      assignee: string | null;
    }) => {
      return await TasksService.bulkAssign(taskIds, assignee);
    },
    onSuccess: (_, variables) => {
      const message = variables.assignee
        ? `Assigned ${variables.taskIds.length} task${variables.taskIds.length > 1 ? 's' : ''} to ${variables.assignee}.`
        : `Unassigned ${variables.taskIds.length} task${variables.taskIds.length > 1 ? 's' : ''}.`;
      toast({
        title: "Tasks Updated",
        description: message,
      });
    },
    onError: (error) => {
      console.error("Error bulk assigning tasks:", error);
      toast({
        title: "Error",
        description: "Failed to assign tasks. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useBulkUpdateTaskCategory = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      taskIds,
      category,
    }: {
      taskIds: string[];
      category: "recruitment" | "screening" | "interview" | "onboarding" | "general";
    }) => {
      return await TasksService.bulkUpdateCategory(taskIds, category);
    },
    onSuccess: (_, variables) => {
      toast({
        title: "Category Updated",
        description: `Updated category for ${variables.taskIds.length} task${variables.taskIds.length > 1 ? 's' : ''}.`,
      });
    },
    onError: (error) => {
      console.error("Error bulk updating task category:", error);
      toast({
        title: "Error",
        description: "Failed to update task category. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useBulkDeleteTasks = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (taskIds: string[]) => {
      return await TasksService.bulkDelete(taskIds);
    },
    onSuccess: (_, taskIds) => {
      toast({
        title: "Tasks Deleted",
        description: `Deleted ${taskIds.length} task${taskIds.length > 1 ? 's' : ''}.`,
      });
    },
    onError: (error) => {
      console.error("Error bulk deleting tasks:", error);
      toast({
        title: "Error",
        description: "Failed to delete tasks. Please try again.",
        variant: "destructive",
      });
    },
  });
};
