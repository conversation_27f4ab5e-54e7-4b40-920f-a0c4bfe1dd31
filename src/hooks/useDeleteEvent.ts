import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export const useDeleteEvent = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventId: string) => {
      if (!eventId) {
        throw new Error("Event ID is required");
      }

      const { error } = await supabase
        .from("events")
        .delete()
        .eq("id", eventId);

      if (error) throw error;
      return eventId;
    },
    // Success toast intentionally omitted (UI shows context-specific success)
    onError: (error) => {
      console.error("Error deleting event:", error);
      toast({
        title: "Error",
        description: "Failed to delete event. Please try again.",
        variant: "destructive",
      });
    },
  });
};
