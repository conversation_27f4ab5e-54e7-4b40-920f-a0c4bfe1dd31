/**
 * React hook for LLM integration
 */

import { useState, useCallback, useEffect } from 'react';
import { llmManager } from '@/services/llm/LLMManager';
import { 
  LLMRequest, 
  LLMResponse, 
  LLMToolRequest, 
  LLMToolResponse,
  LLMUsageStats,
  LLMError
} from '@/services/llm/types';

interface UseLLMOptions {
  preferredProvider?: string;
  autoInitialize?: boolean;
}

interface UseLLMReturn {
  generateResponse: (request: LLMRequest) => Promise<LLMResponse>;
  generateWithTools: (request: LLMToolRequest) => Promise<LLMToolResponse>;
  isLoading: boolean;
  error: LLMError | null;
  usageStats: LLMUsageStats[];
  providerStatus: Array<{ id: string; name: string; available: boolean; stats: LLMUsageStats }>;
  refreshProviders: () => Promise<void>;
  clearError: () => void;
}

export function useLLM(options: UseLLMOptions = {}): UseLLMReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<LLMError | null>(null);
  const [usageStats, setUsageStats] = useState<LLMUsageStats[]>([]);
  const [providerStatus, setProviderStatus] = useState<Array<{ id: string; name: string; available: boolean; stats: LLMUsageStats }>>([]);

  const { preferredProvider, autoInitialize = true } = options;

  // Initialize LLM manager on mount
  useEffect(() => {
    if (autoInitialize) {
      llmManager.initialize().then(() => {
        updateStats();
      });
    }
  }, [autoInitialize]);

  const updateStats = useCallback(() => {
    setUsageStats(llmManager.getUsageStats());
    setProviderStatus(llmManager.getProviderStatus());
  }, []);

  const generateResponse = useCallback(async (request: LLMRequest): Promise<LLMResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await llmManager.generateResponse(request, preferredProvider);
      updateStats();
      return response;
    } catch (err) {
      const llmError = err as LLMError;
      setError(llmError);
      throw llmError;
    } finally {
      setIsLoading(false);
    }
  }, [preferredProvider, updateStats]);

  const generateWithTools = useCallback(async (request: LLMToolRequest): Promise<LLMToolResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await llmManager.generateWithTools(request, preferredProvider);
      updateStats();
      return response;
    } catch (err) {
      const llmError = err as LLMError;
      setError(llmError);
      throw llmError;
    } finally {
      setIsLoading(false);
    }
  }, [preferredProvider, updateStats]);

  const refreshProviders = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await llmManager.refreshProviders();
      updateStats();
    } catch (err) {
      const llmError = err as LLMError;
      setError(llmError);
    } finally {
      setIsLoading(false);
    }
  }, [updateStats]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    generateResponse,
    generateWithTools,
    isLoading,
    error,
    usageStats,
    providerStatus,
    refreshProviders,
    clearError,
  };
}

// Utility hook for simple text generation
export function useSimpleLLM(systemPrompt?: string, preferredProvider?: string) {
  const { generateResponse, isLoading, error, clearError } = useLLM({ preferredProvider });

  const generate = useCallback(async (userMessage: string): Promise<string> => {
    const messages = [];
    
    if (systemPrompt) {
      messages.push({ role: 'system' as const, content: systemPrompt });
    }
    
    messages.push({ role: 'user' as const, content: userMessage });

    const response = await generateResponse({ messages });
    return response.content;
  }, [generateResponse, systemPrompt]);

  return {
    generate,
    isLoading,
    error,
    clearError,
  };
}

// Hook for agent-specific LLM usage with predefined system prompts
export function useAgentLLM(agentType: 'screening' | 'scheduling' | 'communication' | 'analysis') {
  const systemPrompts = {
    screening: `You are an AI recruiting assistant specialized in candidate screening. 
Your role is to analyze candidate profiles, resumes, and job requirements to provide accurate matching scores and recommendations.
Always be objective, fair, and focus on relevant qualifications and experience.`,
    
    scheduling: `You are an AI scheduling assistant for recruitment processes.
Your role is to coordinate interviews, manage calendars, and optimize scheduling for both candidates and interviewers.
Always prioritize efficiency while being respectful of everyone's time and preferences.`,
    
    communication: `You are an AI communication assistant for recruitment.
Your role is to draft professional, personalized messages to candidates and stakeholders.
Always maintain a professional yet friendly tone, and ensure all communications are clear and helpful.`,
    
    analysis: `You are an AI analysis assistant for recruitment data.
Your role is to analyze hiring patterns, identify bottlenecks, and provide actionable insights.
Always base your analysis on data and provide specific, actionable recommendations.`
  };

  return useSimpleLLM(systemPrompts[agentType]);
}
