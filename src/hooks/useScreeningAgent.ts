/**
 * React hook for enhanced candidate screening using AI agents
 */

import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ScreeningAgent, ScreeningCriteria, ScreeningResult, ScreeningContext } from '@/services/agents/ScreeningAgent';
import { CandidateType } from '@/types/candidate';
import { supabase } from '@/integrations/supabase/client';

interface UseScreeningAgentOptions {
  workflowId?: string;
  jobId?: string;
  autoSave?: boolean;
  includeInterviewQuestions?: boolean;
  includeRiskAssessment?: boolean;
}

interface UseScreeningAgentReturn {
  screenCandidate: (candidate: CandidateType, criteria: ScreeningCriteria) => Promise<ScreeningResult>;
  isScreening: boolean;
  lastResult: ScreeningResult | null;
  error: string | null;
  clearError: () => void;
  saveScreeningResult: (candidateId: string, result: ScreeningResult) => Promise<void>;
}

export function useScreeningAgent(options: UseScreeningAgentOptions = {}): UseScreeningAgentReturn {
  const { user } = useAuth();
  const [isScreening, setIsScreening] = useState(false);
  const [lastResult, setLastResult] = useState<ScreeningResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const saveScreeningResult = useCallback(async (candidateId: string, result: ScreeningResult) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      // Update candidate with screening results
      const { error: updateError } = await supabase
        .from('candidates')
        .update({
          screening: {
            ...result,
            lastUpdated: new Date().toISOString(),
            status: 'completed',
          },
          updated_at: new Date().toISOString(),
        })
        .eq('id', candidateId);

      if (updateError) {
        throw updateError;
      }

      // Create timeline entry
      await supabase.from('candidate_timeline').insert({
        candidate_id: candidateId,
        user_id: user.id,
        event_type: 'screening',
        title: 'AI Screening Completed',
        description: `AI screening completed with overall score: ${result.overallScore}%. Confidence: ${Math.round(result.confidenceLevel * 100)}%`,
        event_date: new Date().toISOString(),
        status: 'completed',
        metadata: {
          overallScore: result.overallScore,
          confidenceLevel: result.confidenceLevel,
          criteriaScores: result.criteriaScores,
          executionTimeMs: result.executionTimeMs,
        },
      });

    } catch (err) {
      console.error('Error saving screening result:', err);
      throw new Error('Failed to save screening result');
    }
  }, [user]);

  const screenCandidate = useCallback(async (
    candidate: CandidateType,
    criteria: ScreeningCriteria
  ): Promise<ScreeningResult> => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setIsScreening(true);
    setError(null);

    try {
      // Create screening context
      const context: ScreeningContext = {
        userId: user.id,
        jobId: options.jobId,
        workflowId: options.workflowId,
        priority: 'medium',
        includeInterviewQuestions: options.includeInterviewQuestions ?? true,
        includeRiskAssessment: options.includeRiskAssessment ?? true,
      };

      // Create and execute screening agent
      const agent = new ScreeningAgent(user.id, context);
      const result = await agent.screenCandidate(candidate, criteria);

      setLastResult(result);

      // Auto-save if enabled
      if (options.autoSave) {
        await saveScreeningResult(candidate.id, result);
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Screening failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsScreening(false);
    }
  }, [user, options, saveScreeningResult]);

  return {
    screenCandidate,
    isScreening,
    lastResult,
    error,
    clearError,
    saveScreeningResult,
  };
}

// Helper hook for creating screening criteria from job data
export function useScreeningCriteria() {
  const createCriteriaFromJob = useCallback((job: any): ScreeningCriteria => {
    return {
      jobTitle: job.title || job.name || 'Position',
      requiredSkills: job.required_skills || job.skills || [],
      experienceLevel: job.experience_level || 'mid',
      department: job.department || 'General',
      location: job.location,
      remoteOk: job.remote_ok || false,
      salaryRange: job.salary_range ? {
        min: job.salary_range.min || 0,
        max: job.salary_range.max || 0,
      } : undefined,
      customCriteria: job.custom_criteria || {},
    };
  }, []);

  const createCriteriaFromForm = useCallback((formData: {
    jobTitle: string;
    requiredSkills: string;
    experienceLevel: string;
    department: string;
    location?: string;
    remoteOk?: boolean;
    salaryMin?: number;
    salaryMax?: number;
    customCriteria?: string;
  }): ScreeningCriteria => {
    const skills = formData.requiredSkills
      .split(',')
      .map(skill => skill.trim())
      .filter(skill => skill.length > 0);

    let customCriteria = {};
    if (formData.customCriteria) {
      try {
        customCriteria = JSON.parse(formData.customCriteria);
      } catch (error) {
        console.warn('Invalid custom criteria JSON:', error);
      }
    }

    return {
      jobTitle: formData.jobTitle,
      requiredSkills: skills,
      experienceLevel: formData.experienceLevel as any,
      department: formData.department,
      location: formData.location,
      remoteOk: formData.remoteOk,
      salaryRange: (formData.salaryMin && formData.salaryMax) ? {
        min: formData.salaryMin,
        max: formData.salaryMax,
      } : undefined,
      customCriteria,
    };
  }, []);

  return {
    createCriteriaFromJob,
    createCriteriaFromForm,
  };
}

// Helper hook for screening result analysis
export function useScreeningAnalysis() {
  const getScoreColor = useCallback((score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  }, []);

  const getScoreBadgeVariant = useCallback((score: number): 'default' | 'secondary' | 'destructive' => {
    if (score >= 70) return 'default';
    if (score >= 50) return 'secondary';
    return 'destructive';
  }, []);

  const getConfidenceLabel = useCallback((confidence: number): string => {
    if (confidence >= 0.8) return 'High Confidence';
    if (confidence >= 0.6) return 'Medium Confidence';
    if (confidence >= 0.4) return 'Low Confidence';
    return 'Very Low Confidence';
  }, []);

  const getRecommendation = useCallback((result: ScreeningResult): {
    action: 'proceed' | 'interview' | 'review' | 'reject';
    message: string;
    priority: 'high' | 'medium' | 'low';
  } => {
    const { overallScore, confidenceLevel } = result;

    if (overallScore >= 80 && confidenceLevel >= 0.7) {
      return {
        action: 'proceed',
        message: 'Strong candidate - proceed to next stage',
        priority: 'high',
      };
    }

    if (overallScore >= 60 && confidenceLevel >= 0.5) {
      return {
        action: 'interview',
        message: 'Good candidate - schedule interview',
        priority: 'medium',
      };
    }

    if (overallScore >= 40 || confidenceLevel < 0.5) {
      return {
        action: 'review',
        message: 'Requires manual review',
        priority: 'medium',
      };
    }

    return {
      action: 'reject',
      message: 'Not a good fit for this position',
      priority: 'low',
    };
  }, []);

  return {
    getScoreColor,
    getScoreBadgeVariant,
    getConfidenceLabel,
    getRecommendation,
  };
}
