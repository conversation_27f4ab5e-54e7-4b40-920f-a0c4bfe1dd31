import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";

interface CreateMessageTemplateData {
  name: string;
  subject: string;
  content: string;
  template_category?:
    | "general"
    | "interview"
    | "follow_up"
    | "rejection"
    | "offer";
}

export const useCreateMessageTemplate = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (templateData: CreateMessageTemplateData) => {
      if (!user) {
        throw new Error(
          "User must be authenticated to create a message template",
        );
      }

      // Validate template data
      if (!templateData.name.trim()) {
        throw new Error("Template name is required");
      }

      if (!templateData.subject.trim()) {
        throw new Error("Subject is required");
      }

      if (!templateData.content.trim()) {
        throw new Error("Content is required");
      }

      const { data, error } = await supabase
        .from("message_templates")
        .insert({
          ...templateData,
          template_category: templateData.template_category || "general",
          user_id: user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    // Success toast intentionally omitted (UI shows context-specific success)
    onError: (error) => {
      console.error("Error creating message template:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to create template. Please try again.",
        variant: "destructive",
      });
    },
  });
};
