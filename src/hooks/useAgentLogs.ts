/**
 * React hook for agent execution logging and analytics
 */

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { agentLogger } from '@/services/agents/AgentLogger';
import { AgentExecutionLog } from '@/services/agents/types';
import { LogQuery, LogAnalytics } from '@/services/agents/AgentLogger';

interface UseAgentLogsOptions {
  workflowId?: string;
  executionId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseAgentLogsReturn {
  logs: AgentExecutionLog[];
  analytics: LogAnalytics | null;
  isLoading: boolean;
  error: string | null;
  queryLogs: (query: LogQuery) => Promise<void>;
  refreshAnalytics: () => Promise<void>;
  clearError: () => void;
}

export function useAgentLogs(options: UseAgentLogsOptions = {}): UseAgentLogsReturn {
  const { user } = useAuth();
  const [logs, setLogs] = useState<AgentExecutionLog[]>([]);
  const [analytics, setAnalytics] = useState<LogAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    workflowId,
    executionId,
    autoRefresh = false,
    refreshInterval = 30000 // 30 seconds
  } = options;

  // Auto-refresh logs and analytics
  useEffect(() => {
    if (!autoRefresh || !user) return;

    const interval = setInterval(() => {
      queryLogs({ workflowId, executionId, userId: user.id });
      refreshAnalytics();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, user, workflowId, executionId]);

  // Initial load
  useEffect(() => {
    if (user) {
      queryLogs({ workflowId, executionId, userId: user.id });
      refreshAnalytics();
    }
  }, [user, workflowId, executionId]);

  const queryLogs = useCallback(async (query: LogQuery) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await agentLogger.queryLogs({
        ...query,
        limit: query.limit || 100,
      });
      setLogs(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to query logs';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshAnalytics = useCallback(async () => {
    if (!user) return;

    try {
      const analyticsData = await agentLogger.getAnalytics({
        workflowId,
        executionId,
        userId: user.id,
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      });
      setAnalytics(analyticsData);
    } catch (err) {
      console.error('Failed to refresh analytics:', err);
    }
  }, [user, workflowId, executionId]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    logs,
    analytics,
    isLoading,
    error,
    queryLogs,
    refreshAnalytics,
    clearError,
  };
}

// Hook for workflow-specific logs
export function useWorkflowLogs(workflowId: string, autoRefresh = true) {
  return useAgentLogs({
    workflowId,
    autoRefresh,
    refreshInterval: 10000, // 10 seconds for workflow logs
  });
}

// Hook for execution-specific logs
export function useExecutionLogs(executionId: string) {
  return useAgentLogs({
    executionId,
    autoRefresh: true,
    refreshInterval: 5000, // 5 seconds for active execution logs
  });
}

// Hook for user's recent logs
export function useRecentLogs(limit = 50) {
  const { user } = useAuth();
  const [recentLogs, setRecentLogs] = useState<AgentExecutionLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadRecentLogs = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const logs = await agentLogger.queryLogs({
        userId: user.id,
        limit,
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
      });
      setRecentLogs(logs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load recent logs');
    } finally {
      setIsLoading(false);
    }
  }, [user, limit]);

  useEffect(() => {
    loadRecentLogs();
  }, [loadRecentLogs]);

  return {
    recentLogs,
    isLoading,
    error,
    refresh: loadRecentLogs,
  };
}

// Hook for error logs analysis
export function useErrorLogs(days = 7) {
  const { user } = useAuth();
  const [errorLogs, setErrorLogs] = useState<AgentExecutionLog[]>([]);
  const [errorAnalytics, setErrorAnalytics] = useState<{
    totalErrors: number;
    errorRate: number;
    topErrors: Array<{ errorCode: string; count: number; message: string }>;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadErrorLogs = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);

    try {
      const [errors, analytics] = await Promise.all([
        agentLogger.queryLogs({
          userId: user.id,
          success: false,
          startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
          limit: 200,
        }),
        agentLogger.getAnalytics({
          userId: user.id,
          startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
        })
      ]);

      setErrorLogs(errors);
      setErrorAnalytics({
        totalErrors: errors.length,
        errorRate: 1 - analytics.successRate,
        topErrors: analytics.topErrors,
      });
    } catch (err) {
      console.error('Failed to load error logs:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user, days]);

  useEffect(() => {
    loadErrorLogs();
  }, [loadErrorLogs]);

  return {
    errorLogs,
    errorAnalytics,
    isLoading,
    refresh: loadErrorLogs,
  };
}

// Hook for performance monitoring
export function usePerformanceMetrics(days = 7) {
  const { user } = useAuth();
  const [metrics, setMetrics] = useState<{
    averageExecutionTime: number;
    p95ExecutionTime: number;
    totalTokensUsed: number;
    totalCostEstimate: number;
    executionTrends: Array<{ date: string; count: number; successRate: number }>;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadMetrics = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);

    try {
      const analytics = await agentLogger.getAnalytics({
        userId: user.id,
        startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
      });

      setMetrics({
        averageExecutionTime: analytics.averageExecutionTime,
        p95ExecutionTime: analytics.performanceMetrics.p95ExecutionTime,
        totalTokensUsed: analytics.totalTokensUsed,
        totalCostEstimate: analytics.totalCostEstimate,
        executionTrends: analytics.executionTrends,
      });
    } catch (err) {
      console.error('Failed to load performance metrics:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user, days]);

  useEffect(() => {
    loadMetrics();
  }, [loadMetrics]);

  return {
    metrics,
    isLoading,
    refresh: loadMetrics,
  };
}
