import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface UpdateMessageData {
  id: string;
  status?: "unread" | "read" | "archived";
  is_starred?: boolean;
  follow_up?: boolean;
  reminder?: boolean;
}

export const useUpdateMessage = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateMessageData) => {
      if (!id) {
        throw new Error("Message ID is required");
      }

      const { data, error } = await supabase
        .from("messages")
        .update(updateData)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    // Do not show a generic success toast here to avoid duplicate toasts.
    // Let callers (e.g., MessageComposer) present a single, contextual toast.
    onError: (error) => {
      console.error("Error updating message:", error);
      toast({
        title: "Error",
        description: "Failed to update message. Please try again.",
        variant: "destructive",
      });
    },
  });
};
