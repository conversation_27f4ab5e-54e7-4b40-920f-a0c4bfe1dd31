/**
 * React hook for agent tool execution
 */

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toolRegistry } from '@/services/agents/ToolRegistry';
import { toolExecutor } from '@/services/agents/ToolExecutor';
import { 
  AgentTool, 
  ToolCallRequest, 
  ToolCallResponse, 
  ToolExecutionContext,
  ToolApprovalRequest,
  RateLimitStatus
} from '@/services/agents/types';

interface UseAgentToolsOptions {
  workflowId?: string;
  executionId?: string;
  securityLevel?: 'safe' | 'restricted' | 'dangerous';
  permissions?: string[];
}

interface UseAgentToolsReturn {
  availableTools: AgentTool[];
  executeToolCall: (toolName: string, args: Record<string, any>) => Promise<ToolCallResponse>;
  isLoading: boolean;
  error: string | null;
  pendingApprovals: ToolApprovalRequest[];
  rateLimitStatus: RateLimitStatus[];
  refreshTools: () => Promise<void>;
  clearError: () => void;
}

export function useAgentTools(options: UseAgentToolsOptions = {}): UseAgentToolsReturn {
  const { user } = useAuth();
  const [availableTools, setAvailableTools] = useState<AgentTool[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pendingApprovals, setPendingApprovals] = useState<ToolApprovalRequest[]>([]);
  const [rateLimitStatus, setRateLimitStatus] = useState<RateLimitStatus[]>([]);

  const {
    workflowId,
    executionId,
    securityLevel = 'safe',
    permissions = ['read:candidates', 'read:jobs']
  } = options;

  // Initialize tools on mount
  useEffect(() => {
    initializeTools();
  }, [securityLevel]);

  // Update pending approvals and rate limits periodically
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      updateStatus();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [user]);

  const initializeTools = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await toolRegistry.initialize();
      const tools = toolRegistry.getAvailableTools(securityLevel);
      setAvailableTools(tools);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize tools');
    } finally {
      setIsLoading(false);
    }
  }, [securityLevel]);

  const updateStatus = useCallback(() => {
    if (!user) return;

    // Update pending approvals
    const approvals = toolExecutor.getPendingApprovals(user.id);
    setPendingApprovals(approvals);

    // Update rate limit status
    const rateLimits = toolExecutor.getRateLimitStatus(user.id);
    setRateLimitStatus(rateLimits);
  }, [user]);

  const executeToolCall = useCallback(async (
    toolName: string,
    args: Record<string, any>
  ): Promise<ToolCallResponse> => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setIsLoading(true);
    setError(null);

    try {
      const request: ToolCallRequest = {
        toolName,
        arguments: args,
        requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        workflowId,
        userId: user.id,
        context: { executionId },
      };

      const context: ToolExecutionContext = {
        userId: user.id,
        workflowId,
        executionId,
        permissions,
        rateLimits: new Map(),
        securityLevel,
      };

      const response = await toolExecutor.executeToolCall(request, context);
      
      // Update status after execution
      updateStatus();

      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Tool execution failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [user, workflowId, executionId, permissions, securityLevel, updateStatus]);

  const refreshTools = useCallback(async () => {
    await toolRegistry.refreshTools();
    await initializeTools();
    updateStatus();
  }, [initializeTools, updateStatus]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    availableTools,
    executeToolCall,
    isLoading,
    error,
    pendingApprovals,
    rateLimitStatus,
    refreshTools,
    clearError,
  };
}

// Utility hook for specific tool categories
export function useAgentToolsByCategory(category: string, options: UseAgentToolsOptions = {}) {
  const { availableTools, ...rest } = useAgentTools(options);
  
  const categoryTools = availableTools.filter(tool => tool.category === category);
  
  return {
    availableTools: categoryTools,
    ...rest,
  };
}

// Hook for database-related tools
export function useDatabaseTools(options: UseAgentToolsOptions = {}) {
  return useAgentToolsByCategory('database', {
    ...options,
    permissions: ['read:candidates', 'read:jobs', 'read:applications', ...(options.permissions || [])],
  });
}

// Hook for communication tools
export function useCommunicationTools(options: UseAgentToolsOptions = {}) {
  return useAgentToolsByCategory('communication', {
    ...options,
    securityLevel: 'restricted', // Communication tools require higher security
    permissions: ['send:emails', 'read:candidates', ...(options.permissions || [])],
  });
}

// Hook for analysis tools
export function useAnalysisTools(options: UseAgentToolsOptions = {}) {
  return useAgentToolsByCategory('analysis', {
    ...options,
    permissions: ['read:candidates', 'read:jobs', 'read:analytics', ...(options.permissions || [])],
  });
}
