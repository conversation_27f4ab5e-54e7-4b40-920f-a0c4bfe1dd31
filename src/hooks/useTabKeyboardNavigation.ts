import { useEffect, useCallback } from 'react';

interface UseTabKeyboardNavigationProps {
  /**
   * Array of tab values in order
   */
  tabs: string[];
  /**
   * Currently active tab
   */
  activeTab: string;
  /**
   * Callback to change the active tab
   */
  onTabChange: (tab: string) => void;
  /**
   * Whether keyboard navigation is enabled
   */
  enabled?: boolean;
}

/**
 * Hook to enable keyboard navigation for tabs
 * - Ctrl+Tab / Cmd+Tab: Next tab
 * - Ctrl+Shift+Tab / Cmd+Shift+Tab: Previous tab
 * - Alt+[1-9]: Jump to tab by index
 */
export function useTabKeyboardNavigation({
  tabs,
  activeTab,
  onTabChange,
  enabled = true,
}: UseTabKeyboardNavigationProps) {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      const isMac = navigator.platform.toLowerCase().includes('mac');
      const modifierKey = isMac ? event.metaKey : event.ctrlKey;

      // Get current tab index
      const currentIndex = tabs.indexOf(activeTab);
      if (currentIndex === -1) return;

      // Ctrl/Cmd + Tab: Next tab
      if (modifierKey && event.key === 'Tab' && !event.shiftKey) {
        event.preventDefault();
        const nextIndex = (currentIndex + 1) % tabs.length;
        onTabChange(tabs[nextIndex]);
        return;
      }

      // Ctrl/Cmd + Shift + Tab: Previous tab
      if (modifierKey && event.shiftKey && event.key === 'Tab') {
        event.preventDefault();
        const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        onTabChange(tabs[prevIndex]);
        return;
      }

      // Alt + [1-9]: Jump to tab by index
      if (event.altKey && !event.ctrlKey && !event.metaKey) {
        const num = parseInt(event.key);
        if (num >= 1 && num <= 9 && num <= tabs.length) {
          event.preventDefault();
          onTabChange(tabs[num - 1]);
          return;
        }
      }

      // Arrow keys when focus is on tab list (handled by ARIA)
      if (event.target instanceof HTMLElement) {
        const isTabTrigger = event.target.getAttribute('role') === 'tab';
        if (isTabTrigger) {
          switch (event.key) {
            case 'ArrowRight':
              event.preventDefault();
              const nextIndex = (currentIndex + 1) % tabs.length;
              onTabChange(tabs[nextIndex]);
              // Focus the next tab trigger
              setTimeout(() => {
                const nextTab = document.querySelector(`[role="tab"][data-state="active"]`);
                if (nextTab instanceof HTMLElement) {
                  nextTab.focus();
                }
              }, 0);
              break;
            case 'ArrowLeft':
              event.preventDefault();
              const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
              onTabChange(tabs[prevIndex]);
              // Focus the previous tab trigger
              setTimeout(() => {
                const prevTab = document.querySelector(`[role="tab"][data-state="active"]`);
                if (prevTab instanceof HTMLElement) {
                  prevTab.focus();
                }
              }, 0);
              break;
            case 'Home':
              event.preventDefault();
              onTabChange(tabs[0]);
              break;
            case 'End':
              event.preventDefault();
              onTabChange(tabs[tabs.length - 1]);
              break;
          }
        }
      }
    },
    [tabs, activeTab, onTabChange, enabled]
  );

  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, enabled]);

  // Return helper functions
  return {
    goToNextTab: useCallback(() => {
      const currentIndex = tabs.indexOf(activeTab);
      if (currentIndex !== -1) {
        const nextIndex = (currentIndex + 1) % tabs.length;
        onTabChange(tabs[nextIndex]);
      }
    }, [tabs, activeTab, onTabChange]),
    
    goToPreviousTab: useCallback(() => {
      const currentIndex = tabs.indexOf(activeTab);
      if (currentIndex !== -1) {
        const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        onTabChange(tabs[prevIndex]);
      }
    }, [tabs, activeTab, onTabChange]),
    
    goToTab: useCallback((index: number) => {
      if (index >= 0 && index < tabs.length) {
        onTabChange(tabs[index]);
      }
    }, [tabs, onTabChange]),
  };
}

/**
 * Keyboard shortcuts help text
 */
export const TabKeyboardShortcuts = {
  nextTab: {
    mac: '⌘ + Tab',
    windows: 'Ctrl + Tab',
    description: 'Go to next tab',
  },
  previousTab: {
    mac: '⌘ + Shift + Tab',
    windows: 'Ctrl + Shift + Tab',
    description: 'Go to previous tab',
  },
  jumpToTab: {
    mac: 'Alt + [1-9]',
    windows: 'Alt + [1-9]',
    description: 'Jump to specific tab',
  },
  arrowNavigation: {
    keys: '← →',
    description: 'Navigate tabs when focused',
  },
  homeEnd: {
    keys: 'Home / End',
    description: 'Jump to first / last tab',
  },
};
