/**
 * React hook for advanced communication agent functionality
 */

import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  CommunicationAgent, 
  CommunicationContext, 
  CommunicationRequest, 
  CommunicationResult,
  SentimentAnalysis 
} from '@/services/agents/CommunicationAgent';
import { CandidateType } from '@/types/candidate';

interface UseCommunicationAgentOptions {
  workflowId?: string;
  jobId?: string;
  candidateId?: string;
  autoSave?: boolean;
  defaultTone?: 'professional' | 'friendly' | 'formal' | 'casual';
  defaultPriority?: 'low' | 'medium' | 'high' | 'urgent';
}

interface UseCommunicationAgentReturn {
  generateEmail: (request: Omit<CommunicationRequest, 'type'>) => Promise<CommunicationResult>;
  analyzeSentiment: (content: string) => Promise<SentimentAnalysis | null>;
  suggestResponse: (request: Omit<CommunicationRequest, 'type'>) => Promise<CommunicationResult>;
  scheduleFollowup: (request: Omit<CommunicationRequest, 'type'>) => Promise<CommunicationResult>;
  isProcessing: boolean;
  lastResult: CommunicationResult | null;
  error: string | null;
  clearError: () => void;
}

export function useCommunicationAgent(options: UseCommunicationAgentOptions = {}): UseCommunicationAgentReturn {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastResult, setLastResult] = useState<CommunicationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const createContext = useCallback((communicationType: CommunicationContext['communicationType']): CommunicationContext => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    return {
      userId: user.id,
      candidateId: options.candidateId,
      jobId: options.jobId,
      workflowId: options.workflowId,
      communicationType,
      priority: options.defaultPriority || 'medium',
      tone: options.defaultTone || 'professional',
      includePersonalization: true,
      includeSentimentAnalysis: true,
    };
  }, [user, options]);

  const processRequest = useCallback(async (
    requestType: CommunicationRequest['type'],
    request: Omit<CommunicationRequest, 'type'>
  ): Promise<CommunicationResult> => {
    setIsProcessing(true);
    setError(null);

    try {
      const context = createContext('email'); // Default to email, can be overridden
      const agent = new CommunicationAgent(context);
      
      const fullRequest: CommunicationRequest = {
        ...request,
        type: requestType,
      };

      const result = await agent.processRequest(fullRequest);
      setLastResult(result);
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Communication processing failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  }, [createContext]);

  const generateEmail = useCallback(async (request: Omit<CommunicationRequest, 'type'>): Promise<CommunicationResult> => {
    return processRequest('generate_email', request);
  }, [processRequest]);

  const analyzeSentiment = useCallback(async (content: string): Promise<SentimentAnalysis | null> => {
    try {
      const result = await processRequest('analyze_sentiment', { content });
      return result.sentimentAnalysis || null;
    } catch (err) {
      console.error('Sentiment analysis failed:', err);
      return null;
    }
  }, [processRequest]);

  const suggestResponse = useCallback(async (request: Omit<CommunicationRequest, 'type'>): Promise<CommunicationResult> => {
    return processRequest('suggest_response', request);
  }, [processRequest]);

  const scheduleFollowup = useCallback(async (request: Omit<CommunicationRequest, 'type'>): Promise<CommunicationResult> => {
    return processRequest('schedule_followup', request);
  }, [processRequest]);

  return {
    generateEmail,
    analyzeSentiment,
    suggestResponse,
    scheduleFollowup,
    isProcessing,
    lastResult,
    error,
    clearError,
  };
}

// Helper hook for email templates and common patterns
export function useEmailTemplates() {
  const getTemplate = useCallback((templateType: string): { subject: string; template: string } => {
    const templates: Record<string, { subject: string; template: string }> = {
      initial_outreach: {
        subject: 'Exciting opportunity at [Company Name]',
        template: 'initial_outreach_template',
      },
      interview_invitation: {
        subject: 'Interview invitation for [Position] role',
        template: 'interview_invitation_template',
      },
      follow_up: {
        subject: 'Following up on our conversation',
        template: 'follow_up_template',
      },
      rejection: {
        subject: 'Update on your application',
        template: 'rejection_template',
      },
      offer: {
        subject: 'Job offer for [Position] role',
        template: 'offer_template',
      },
      onboarding: {
        subject: 'Welcome to [Company Name]!',
        template: 'onboarding_template',
      },
    };

    return templates[templateType] || {
      subject: 'Communication from [Company Name]',
      template: 'default_template',
    };
  }, []);

  const getPersonalizationVariables = useCallback((candidate: CandidateType) => {
    return {
      candidateName: candidate.name,
      candidateRole: candidate.role,
      candidateLocation: candidate.location,
      candidateExperience: candidate.experience,
      candidateSkills: candidate.skills?.map(s => typeof s === 'string' ? s : s.name).join(', '),
      candidateEmail: candidate.email,
    };
  }, []);

  return {
    getTemplate,
    getPersonalizationVariables,
  };
}

// Helper hook for sentiment analysis utilities
export function useSentimentAnalysis() {
  const getSentimentColor = useCallback((sentiment: string): string => {
    switch (sentiment) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      case 'neutral': return 'text-gray-600';
      case 'mixed': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  }, []);

  const getSentimentIcon = useCallback((sentiment: string): string => {
    switch (sentiment) {
      case 'positive': return '😊';
      case 'negative': return '😟';
      case 'neutral': return '😐';
      case 'mixed': return '🤔';
      default: return '❓';
    }
  }, []);

  const getEmotionIntensity = useCallback((emotion: number): 'low' | 'medium' | 'high' => {
    if (emotion >= 0.7) return 'high';
    if (emotion >= 0.4) return 'medium';
    return 'low';
  }, []);

  const getResponseUrgency = useCallback((analysis: SentimentAnalysis): {
    level: 'low' | 'medium' | 'high' | 'urgent';
    message: string;
    color: string;
  } => {
    if (analysis.recommendedResponse === 'immediate' || analysis.sentiment === 'negative') {
      return {
        level: 'urgent',
        message: 'Immediate response required',
        color: 'text-red-600',
      };
    }

    if (analysis.recommendedResponse === 'escalate') {
      return {
        level: 'high',
        message: 'Escalation recommended',
        color: 'text-orange-600',
      };
    }

    if (analysis.sentiment === 'positive') {
      return {
        level: 'medium',
        message: 'Timely response recommended',
        color: 'text-green-600',
      };
    }

    return {
      level: 'low',
      message: 'Standard response timing',
      color: 'text-gray-600',
    };
  }, []);

  const formatEmotionAnalysis = useCallback((emotions: SentimentAnalysis['emotions']) => {
    return Object.entries(emotions)
      .filter(([_, value]) => value > 0.3) // Only show significant emotions
      .sort(([_, a], [__, b]) => b - a) // Sort by intensity
      .map(([emotion, intensity]) => ({
        emotion: emotion.charAt(0).toUpperCase() + emotion.slice(1),
        intensity,
        level: getEmotionIntensity(intensity),
      }));
  }, [getEmotionIntensity]);

  return {
    getSentimentColor,
    getSentimentIcon,
    getEmotionIntensity,
    getResponseUrgency,
    formatEmotionAnalysis,
  };
}

// Helper hook for communication analytics
export function useCommunicationAnalytics() {
  const analyzeResponseTime = useCallback((messages: Array<{timestamp: string; sender: string}>) => {
    const recruiterMessages = messages.filter(m => m.sender === 'recruiter');
    const candidateMessages = messages.filter(m => m.sender === 'candidate');

    if (recruiterMessages.length === 0 || candidateMessages.length === 0) {
      return null;
    }

    // Calculate average response time
    const responseTimes: number[] = [];
    
    for (let i = 1; i < messages.length; i++) {
      const current = new Date(messages[i].timestamp);
      const previous = new Date(messages[i - 1].timestamp);
      
      if (messages[i].sender !== messages[i - 1].sender) {
        responseTimes.push(current.getTime() - previous.getTime());
      }
    }

    if (responseTimes.length === 0) return null;

    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const hours = Math.round(avgResponseTime / (1000 * 60 * 60));

    return {
      averageResponseTimeMs: avgResponseTime,
      averageResponseTimeHours: hours,
      totalExchanges: responseTimes.length,
      engagement: hours <= 24 ? 'high' : hours <= 72 ? 'medium' : 'low',
    };
  }, []);

  const getCommunicationHealth = useCallback((
    sentimentHistory: SentimentAnalysis[],
    responseTimeData: ReturnType<typeof analyzeResponseTime>
  ) => {
    if (sentimentHistory.length === 0) {
      return { score: 50, status: 'unknown', factors: [] };
    }

    let score = 50;
    const factors: string[] = [];

    // Sentiment trend analysis
    const recentSentiments = sentimentHistory.slice(-3);
    const positiveCount = recentSentiments.filter(s => s.sentiment === 'positive').length;
    const negativeCount = recentSentiments.filter(s => s.sentiment === 'negative').length;

    if (positiveCount > negativeCount) {
      score += 20;
      factors.push('Positive sentiment trend');
    } else if (negativeCount > positiveCount) {
      score -= 20;
      factors.push('Negative sentiment trend');
    }

    // Response time analysis
    if (responseTimeData) {
      if (responseTimeData.engagement === 'high') {
        score += 15;
        factors.push('Quick response times');
      } else if (responseTimeData.engagement === 'low') {
        score -= 15;
        factors.push('Slow response times');
      }
    }

    // Confidence analysis
    const avgConfidence = sentimentHistory.reduce((sum, s) => sum + s.confidence, 0) / sentimentHistory.length;
    if (avgConfidence > 0.8) {
      score += 10;
      factors.push('High analysis confidence');
    }

    const finalScore = Math.max(0, Math.min(100, score));
    
    return {
      score: finalScore,
      status: finalScore >= 70 ? 'healthy' : finalScore >= 40 ? 'moderate' : 'concerning',
      factors,
    };
  }, []);

  return {
    analyzeResponseTime,
    getCommunicationHealth,
  };
}
