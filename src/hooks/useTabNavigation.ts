import { useState, useCallback, useEffect, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

export interface UseTabNavigationOptions {
  basePath: string;
  defaultTab: string;
  validTabs: string[];
  preserveQuery?: boolean;
  persistToLocalStorage?: boolean;
  storageKey?: string;
}

/**
 * Custom hook for managing tab navigation with URL persistence
 * 
 * @param options Configuration options for tab navigation
 * @returns Current tab value and handler for tab changes
 */
export function useTabNavigation({
  basePath,
  defaultTab,
  validTabs,
  preserveQuery = true,
  persistToLocalStorage = false,
  storageKey,
}: UseTabNavigationOptions) {
  const navigate = useNavigate();
  const location = useLocation();

  // Get active tab from URL
  const activeTab = useMemo(() => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const baseSegments = basePath.split('/').filter(Boolean);
    
    // Find the segment after the base path
    if (pathSegments.length > baseSegments.length) {
      const tabSegment = pathSegments[baseSegments.length];
      if (validTabs.includes(tabSegment)) {
        return tabSegment;
      }
    }

    // Try to get from localStorage if enabled
    if (persistToLocalStorage && storageKey) {
      const stored = localStorage.getItem(storageKey);
      if (stored && validTabs.includes(stored)) {
        return stored;
      }
    }

    return defaultTab;
  }, [location.pathname, basePath, defaultTab, validTabs, persistToLocalStorage, storageKey]);

  // Handle tab change
  const handleTabChange = useCallback(
    (newTab: string) => {
      if (!validTabs.includes(newTab)) {
        console.warn(`Invalid tab value: ${newTab}`);
        return;
      }

      if (newTab === activeTab) return;

      // Save to localStorage if enabled
      if (persistToLocalStorage && storageKey) {
        localStorage.setItem(storageKey, newTab);
      }

      // Navigate to new tab
      const newPath = `${basePath}/${newTab}`;
      navigate({
        pathname: newPath,
        search: preserveQuery ? location.search : '',
        hash: location.hash,
      });
    },
    [activeTab, basePath, navigate, location.search, location.hash, preserveQuery, validTabs, persistToLocalStorage, storageKey]
  );

  // Sync localStorage across tabs
  useEffect(() => {
    if (!persistToLocalStorage || !storageKey) return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === storageKey && e.newValue && validTabs.includes(e.newValue)) {
        const newPath = `${basePath}/${e.newValue}`;
        navigate({
          pathname: newPath,
          search: preserveQuery ? location.search : '',
          hash: location.hash,
        });
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [basePath, navigate, location.search, location.hash, preserveQuery, validTabs, persistToLocalStorage, storageKey]);

  return {
    activeTab,
    handleTabChange,
  };
}

/**
 * Hook for simple state-based tabs (no URL routing)
 */
export function useStateTabs(defaultTab: string, storageKey?: string) {
  const stored = storageKey ? localStorage.getItem(storageKey) : null;
  const [activeTab, setActiveTab] = useState(stored || defaultTab);

  const handleTabChange = useCallback(
    (newTab: string) => {
      setActiveTab(newTab);
      if (storageKey) {
        localStorage.setItem(storageKey, newTab);
      }
    },
    [storageKey]
  );

  // Sync across browser tabs
  useEffect(() => {
    if (!storageKey) return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === storageKey && e.newValue) {
        setActiveTab(e.newValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [storageKey]);

  return {
    activeTab,
    handleTabChange,
  };
}
