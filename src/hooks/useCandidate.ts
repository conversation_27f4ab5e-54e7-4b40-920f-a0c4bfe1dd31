import { useRealtimeRecord } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { CandidateType } from "@/types/candidate";
import { useAuth } from "@/contexts/AuthContext";
import { useQuery } from "@tanstack/react-query";

export const useCandidate = (id: string) => {
  const { user } = useAuth();

  // Fetch candidate with normalized skills from the view
  const { data: candidateData, isLoading, error } = useQuery({
    queryKey: ["candidate", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("candidates_with_normalized_data")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id && !!user,
  });

  const transformedCandidate: CandidateType | null = candidateData
    ? {
        id: candidateData.id,
        name: candidateData.name,
        role: candidateData.role,
        email: candidateData.email,
        phone: candidateData.phone || "",
        location: candidateData.location || "",
        avatar: candidateData.avatar || "/placeholder.svg",
        recruiter: {
          id: candidateData.recruiter_id || "",
          name: candidateData.recruiter_name || "",
          avatar: candidateData.recruiter_avatar || "/placeholder.svg",
        },
        tags: candidateData.normalized_tags 
          ? candidateData.normalized_tags.map(tag => tag.name)
          : [],
        normalized_tags: Array.isArray(candidateData.normalized_tags)
          ? (candidateData.normalized_tags as {
              id: string;
              name: string;
              color?: string;
            }[])
          : [],
        socialLinks: {
          github: candidateData.github_url || "",
          linkedin: candidateData.linkedin_url || "",
          twitter: candidateData.twitter_url || "",
        },
        relationshipScore: candidateData.relationship_score || 0,
        experience: candidateData.experience || "",
        industry: candidateData.industry || "",
        remotePreference: candidateData.remote_preference || "",
        visaStatus: candidateData.visa_status || "",
        skills: Array.isArray(candidateData.normalized_skills)
          ? (candidateData.normalized_skills as {
              name: string;
              level: string;
              years: number;
              category?: string;
            }[])
          : [],
        aiSummary: candidateData.ai_summary || "",
        matchedJobs: [], // This would be populated by a separate query
        screening: candidateData.screening || undefined,
        createdAt: candidateData.created_at,
        updatedAt: candidateData.updated_at,
      }
    : null;

  return {
    candidate: transformedCandidate,
    isLoading,
    error,
  };
};
