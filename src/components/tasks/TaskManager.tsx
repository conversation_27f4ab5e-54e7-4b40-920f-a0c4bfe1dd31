import React, { useState, useEffect, useMemo } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { EnhancedTabs, Tab<PERSON>anel } from "@/design-system/controls/EnhancedTabs";
import {
  CalendarIcon,
  CheckCircle2,
  Clock,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Loader2,
  CheckSquare,
  ListTodo,
  AlertCircle,
  PlayCircle,
  CheckCircle,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import {
  useTasks,
  useCreateTask,
  useUpdateTask,
  useDeleteTask,
  useBulkUpdateTaskStatus,
  useBulkUpdateTaskPriority,
  useBulkAssignTasks,
  useBulkUpdateTaskCategory,
  useBulkDeleteTasks,
} from "@/hooks/useTasks";
import { CreateTaskData, TaskFilters, Task } from "@/services/TasksService";
import { useToast } from "@/hooks/use-toast";
import { TaskActionMenu } from "./TaskActionMenu";
import { TaskDetailModal } from "./TaskDetailModal";
import { BatchActionsToolbar } from "./BatchActionsToolbar";

// Type definitions for tabs
type TabKey = "all" | "pending" | "in-progress" | "completed";
const TAB_VALUES: TabKey[] = ["all", "pending", "in-progress", "completed"];

// Helper function to derive active tab from URL
const getActiveTabFromPath = (pathname: string): TabKey => {
  const base = "/tasks/";
  const idx = pathname.indexOf(base);
  const seg = idx >= 0 ? pathname.slice(idx + base.length).split("/")[0] : "";
  return (TAB_VALUES as readonly string[]).includes(seg) ? (seg as TabKey) : "all";
};

export function TaskManager() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();

  // Derive active tab from URL
  const activeTab = getActiveTabFromPath(location.pathname);
  
  // Handle tab change
  const handleTabChange = (value: string) => {
    if (value !== activeTab) {
      navigate({ 
        pathname: `/tasks/${value}`, 
        search: location.search,
        hash: location.hash 
      });
    }
  };

  // States for filters and UI
  const [selectedFilter, setSelectedFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false);
  
  // Selection state for batch operations
  const [selectedTaskIds, setSelectedTaskIds] = useState<Set<string>>(new Set());

  // States for task detail modal
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isTaskDetailOpen, setIsTaskDetailOpen] = useState(false);
  const [taskDetailMode, setTaskDetailMode] = useState<"view" | "edit">("view");

  // New task form state - using Date for UI, will convert to string for database
  const [newTask, setNewTask] = useState<{
    title: string;
    description: string;
    status: string;
    priority: string;
    assignee: string;
    due_date: Date | null;
    category: string;
  }>({
    title: "",
    description: "",
    status: "pending",
    priority: "medium",
    assignee: "",
    due_date: null,
    category: "general",
  });

  // Build filters for the query - only use selectedFilter for server-side filtering
  const taskFilters: TaskFilters = {};
  if (selectedFilter !== "all") {
    taskFilters.status = selectedFilter as TaskFilters["status"];
  }

  // Hooks for data fetching and mutations
  // Get all tasks for counting, then filter client-side based on activeTab
  const { data: allTasks = [], isLoading, error } = useTasks(taskFilters);
  const createTaskMutation = useCreateTask();
  const updateTaskMutation = useUpdateTask();
  const deleteTaskMutation = useDeleteTask();
  
  // Bulk operation hooks
  const bulkUpdateStatus = useBulkUpdateTaskStatus();
  const bulkUpdatePriority = useBulkUpdateTaskPriority();
  const bulkAssignTasks = useBulkAssignTasks();
  const bulkUpdateCategory = useBulkUpdateTaskCategory();
  const bulkDeleteTasks = useBulkDeleteTasks();

  // Filter tasks based on search term and selectedFilter
  const getFilteredTasks = (tabValue: string) => {
    return allTasks.filter((task) => {
      // Filter by tab value
      const matchesTab = tabValue === "all" || task.status === tabValue;
      
      // Filter by search term
      const matchesSearch =
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (task.description || "").toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesTab && matchesSearch;
    });
  };
  
  const filteredTasks = getFilteredTasks(activeTab);
  
  // Selection helpers
  const toggleSelect = (id: string) => {
    setSelectedTaskIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };
  
  const clearSelection = () => {
    setSelectedTaskIds(new Set());
  };
  
  const selectAllVisible = () => {
    const visibleIds = filteredTasks.map(t => t.id);
    setSelectedTaskIds(new Set(visibleIds));
  };
  
  const deselectAllVisible = () => {
    const visibleIds = new Set(filteredTasks.map(t => t.id));
    setSelectedTaskIds(prev => {
      const newSet = new Set(prev);
      visibleIds.forEach(id => newSet.delete(id));
      return newSet;
    });
  };
  
  // Derived selection state
  const visibleIds = useMemo(() => filteredTasks.map(t => t.id), [filteredTasks]);
  const selectedInView = useMemo(
    () => visibleIds.filter(id => selectedTaskIds.has(id)),
    [visibleIds, selectedTaskIds]
  );
  const isAllSelectedInView = selectedInView.length === visibleIds.length && visibleIds.length > 0;
  const isIndeterminate = selectedInView.length > 0 && !isAllSelectedInView;
  
  // Cleanup selected IDs when tasks are deleted/removed
  useEffect(() => {
    if (selectedTaskIds.size === 0) return;
    
    const currentTaskIds = new Set(allTasks.map(t => t.id));
    setSelectedTaskIds(prev => {
      // Only create a new Set if there are actually changes
      let hasChanges = false;
      const newSet = new Set<string>();
      
      prev.forEach(id => {
        if (currentTaskIds.has(id)) {
          newSet.add(id);
        } else {
          hasChanges = true;
        }
      });
      
      // Return the same reference if nothing changed to avoid re-renders
      return hasChanges ? newSet : prev;
    });
  }, [allTasks, selectedTaskIds.size]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-500/20 text-red-400";
      case "medium":
        return "bg-yellow-500/20 text-yellow-400";
      case "low":
        return "bg-green-500/20 text-green-400";
      default:
        return "bg-gray-500/20 text-gray-400";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500/20 text-green-400";
      case "in-progress":
        return "bg-blue-500/20 text-blue-400";
      case "pending":
        return "bg-orange-500/20 text-orange-400";
      default:
        return "bg-gray-500/20 text-gray-400";
    }
  };

  const handleAddTask = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to create tasks.",
        variant: "destructive",
      });
      return;
    }

    if (!newTask.title?.trim()) {
      toast({
        title: "Title Required",
        description: "Please enter a task title.",
        variant: "destructive",
      });
      return;
    }

    try {
      const taskData: CreateTaskData = {
        title: newTask.title.trim(),
        description: newTask.description?.trim() || null,
        status: (newTask.status as CreateTaskData["status"]) || "pending",
        priority: (newTask.priority as CreateTaskData["priority"]) || "medium",
        assignee: newTask.assignee?.trim() || null,
        due_date: newTask.due_date ? newTask.due_date.toISOString() : null,
        category: (newTask.category as CreateTaskData["category"]) || "general",
        user_id: user.id,
      };

      await createTaskMutation.mutateAsync(taskData);

      // Reset form
      setNewTask({
        title: "",
        description: "",
        status: "pending",
        priority: "medium",
        assignee: "",
        due_date: null,
        category: "general",
      });
      setIsAddTaskOpen(false);

      toast({
        title: "Task Created",
        description: "Your task has been successfully created.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getTaskCount = (status: string) => {
    if (status === "all") return allTasks.length;
    return allTasks.filter((task) => task.status === status).length;
  };

  // Task interaction handlers
  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
    setTaskDetailMode("view");
    setIsTaskDetailOpen(true);
  };

  const handleTaskEdit = (task: Task) => {
    setSelectedTask(task);
    setTaskDetailMode("edit");
    setIsTaskDetailOpen(true);
  };

  const handleTaskDetailClose = () => {
    setIsTaskDetailOpen(false);
    setSelectedTask(null);
  };
  
  // Handle deletion from batch toolbar
  const handleBatchDeleted = () => {
    clearSelection();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-0 min-w-0">
        <div className="backdrop-blur-xl bg-white/5 dark:bg-gray-900/30 rounded-xl border border-white/10 shadow-xl p-8">
          <div className="flex items-center justify-center">
            <Loader2 className="w-8 h-8 animate-spin text-purple-500" />
            <span className="ml-2 text-gray-300">Loading tasks...</span>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-0 min-w-0">
        <div className="backdrop-blur-xl bg-white/5 dark:bg-gray-900/30 rounded-xl border border-white/10 shadow-xl p-8 text-center">
          <h3 className="text-lg font-medium mb-2 text-red-400">
            Error Loading Tasks
          </h3>
          <p className="text-sm text-gray-400">
            {error instanceof Error
              ? error.message
              : "Failed to load tasks. Please try again."}
          </p>
        </div>
      </div>
    );
  }

  // Show login required state
  if (!user) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-0 min-w-0">
        <div className="backdrop-blur-xl bg-white/5 dark:bg-gray-900/30 rounded-xl border border-white/10 shadow-xl p-8 text-center">
          <h3 className="text-lg font-medium mb-2 text-gray-200">Authentication Required</h3>
          <p className="text-sm text-gray-400">
            Please log in to view and manage your tasks.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header Section with Glass Effect */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 sm:p-6 hover-lift fade-up">
        <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
          <div className="flex items-center gap-3 min-w-0">
            <div className="p-2 rounded-lg bg-primary/10 ring-1 ring-primary/20 hover:bg-primary/15 transition-colors duration-300">
              <CheckSquare className="w-6 h-6 sm:w-8 sm:h-8 text-primary flex-shrink-0" />
            </div>
            <div className="min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">
                Task Manager
              </h1>
              <p className="text-sm sm:text-base text-muted-foreground truncate">
                Organize and track recruitment tasks and activities
              </p>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <Dialog open={isAddTaskOpen} onOpenChange={setIsAddTaskOpen}>
              <DialogTrigger asChild>
                <Button className="w-full sm:w-auto hover-lift transition-all duration-300 bg-primary/90 hover:bg-primary shadow-soft">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Task
                </Button>
              </DialogTrigger>
          <DialogContent className="mx-4 sm:mx-0 sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Task</DialogTitle>
              <DialogDescription>
                Create a new task to track recruitment activities.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={newTask.title || ""}
                  onChange={(e) =>
                    setNewTask({ ...newTask, title: e.target.value })
                  }
                  placeholder="Enter task title"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newTask.description || ""}
                  onChange={(e) =>
                    setNewTask({ ...newTask, description: e.target.value })
                  }
                  placeholder="Enter task description"
                  className="resize-none"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select
                    value={newTask.priority}
                    onValueChange={(value) =>
                      setNewTask({
                        ...newTask,
                        priority: value as CreateTaskData["priority"],
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={newTask.category}
                    onValueChange={(value) =>
                      setNewTask({
                        ...newTask,
                        category: value as CreateTaskData["category"],
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="recruitment">Recruitment</SelectItem>
                      <SelectItem value="screening">Screening</SelectItem>
                      <SelectItem value="interview">Interview</SelectItem>
                      <SelectItem value="onboarding">Onboarding</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="assignee">Assignee</Label>
                <Input
                  id="assignee"
                  value={newTask.assignee || ""}
                  onChange={(e) =>
                    setNewTask({ ...newTask, assignee: e.target.value })
                  }
                  placeholder="Enter assignee name"
                />
              </div>
              <div className="grid gap-2">
                <Label>Due Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !newTask.due_date && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newTask.due_date
                        ? format(newTask.due_date, "PPP")
                        : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={newTask.due_date || undefined}
                      onSelect={(date) =>
                        setNewTask({ ...newTask, due_date: date || null })
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setIsAddTaskOpen(false)}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddTask}
                className="w-full sm:w-auto"
                disabled={createTaskMutation.isPending}
              >
                {createTaskMutation.isPending ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Add Task"
                )}
              </Button>
            </div>
          </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Controls section */}
      <div className="glass-card ring-subtle soft-shadow rounded-xl p-4 space-y-4 motion-safe:animate-fade-in [animation-delay:160ms]">
        {/* Selection controls */}
        {filteredTasks.length > 0 && (
          <div className="flex items-center gap-4">
            <Checkbox
              checked={isAllSelectedInView ? true : (isIndeterminate ? "indeterminate" : false)}
              onCheckedChange={() => {
                if (isAllSelectedInView) {
                  deselectAllVisible();
                } else {
                  selectAllVisible();
                }
              }}
              aria-label="Select all visible tasks"
            />
            {selectedTaskIds.size > 0 && (
              <span className="text-sm text-muted-foreground">
                {selectedTaskIds.size} selected
              </span>
            )}
          </div>
        )}
        
        {/* Search and filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1 group">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground transition-colors group-focus-within:text-primary" />
            <Input
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full bg-background/60 border-white/10 focus:bg-background/80 focus:ring-2 focus:ring-primary/20 transition-all duration-300"
            />
          </div>
          <Select value={selectedFilter} onValueChange={setSelectedFilter}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tasks</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Enhanced Tabs with Modern Design */}
      <EnhancedTabs 
        tabs={[
          { 
            value: "all", 
            label: `All Tasks${getTaskCount("all") > 0 ? ` (${getTaskCount("all")})` : ''}`,
            icon: ListTodo
          },
          { 
            value: "pending", 
            label: `Pending${getTaskCount("pending") > 0 ? ` (${getTaskCount("pending")})` : ''}`,
            icon: AlertCircle
          },
          { 
            value: "in-progress", 
            label: `In Progress${getTaskCount("in-progress") > 0 ? ` (${getTaskCount("in-progress")})` : ''}`,
            icon: PlayCircle
          },
          { 
            value: "completed", 
            label: `Completed${getTaskCount("completed") > 0 ? ` (${getTaskCount("completed")})` : ''}`,
            icon: CheckCircle
          },
        ]}
        value={activeTab}
        onValueChange={handleTabChange}
        variant="navigation"
        indicatorStyle="underline"
        className="space-y-6"
      >
        {["all", "pending", "in-progress", "completed"].map((tabValue) => {
          const tasksForTab = getFilteredTasks(tabValue);
          return (
          <TabPanel key={tabValue} value={tabValue} className="space-y-4 motion-safe:animate-fade-in [animation-delay:160ms]">
          <div className="glass-card ring-subtle soft-shadow rounded-xl p-2 sm:p-3 hover-lift">
            <Card className="min-w-0 border-0 bg-transparent shadow-none">
              <CardContent className="p-0">
                <div className="grid gap-4 min-w-0">
                  {tasksForTab.map((task) => (
                    <Card
                      key={task.id}
                      className={cn(
                        "hover:bg-muted/50 transition-all duration-300 min-w-0 cursor-pointer relative",
                        selectedTaskIds.has(task.id) && "ring-2 ring-primary"
                      )}
                      onClick={() => handleTaskClick(task)}
                    >
                      <CardHeader className="pb-2">
                        {/* Selection checkbox */}
                        <div
                          className="absolute top-3 left-3 z-10"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Checkbox
                            checked={selectedTaskIds.has(task.id)}
                            onCheckedChange={() => toggleSelect(task.id)}
                            aria-label={`Select ${task.title}`}
                      />
                    </div>
                    
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 pl-8">
                      <div className="space-y-1 min-w-0 flex-1">
                          <CardTitle className="text-base sm:text-lg break-words">
                            {task.title}
                          </CardTitle>
                          {task.description && (
                            <CardDescription className="break-words">
                            {task.description}
                          </CardDescription>
                        )}
                      </div>
                    <div onClick={(e) => e.stopPropagation()}>
                      <TaskActionMenu task={task} onEdit={handleTaskEdit} />
                    </div>
                  </div>
                </CardHeader>
                  <CardContent className="min-w-0 px-3 pb-3">
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Badge className={cn(
                        "border-0 shadow-sm",
                        task.status === "completed" && "bg-green-500/20 text-green-400",
                        task.status === "in-progress" && "bg-blue-500/20 text-blue-400",
                        task.status === "pending" && "bg-orange-500/20 text-orange-400"
                      )}>
                        {task.status === "in-progress"
                          ? "In Progress"
                          : task.status.charAt(0).toUpperCase() +
                            task.status.slice(1)}
                      </Badge>
                      <Badge className={cn(
                        "border-0 shadow-sm",
                        task.priority === "high" && "bg-red-500/20 text-red-400",
                        task.priority === "medium" && "bg-yellow-500/20 text-yellow-400",
                        task.priority === "low" && "bg-green-500/20 text-green-400"
                      )}>
                        <span className="hidden sm:inline">
                          {task.priority.charAt(0).toUpperCase() +
                            task.priority.slice(1)}{" "}
                          Priority
                        </span>
                        <span className="sm:hidden">
                          {task.priority.charAt(0).toUpperCase() +
                            task.priority.slice(1)}
                        </span>
                      </Badge>
                      <Badge variant="secondary">
                        {task.category.charAt(0).toUpperCase() +
                          task.category.slice(1)}
                      </Badge>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-sm text-muted-foreground">
                      {task.assignee && (
                        <span className="break-words">
                          Assigned to: <span className="font-medium">{task.assignee}</span>
                        </span>
                      )}
                      {task.due_date && (
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>
                            Due: {format(new Date(task.due_date), "MMM dd, yyyy")}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {tasksForTab.length === 0 && (
              <Card className="p-8 text-center">
                <div className="p-4 bg-primary/10 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                  <CheckCircle2 className="w-10 h-10 text-primary" />
                </div>
                <h3 className="text-base sm:text-lg font-medium mb-2">
                  No tasks found
                </h3>
                <p className="text-sm sm:text-base text-muted-foreground">
                  {searchTerm || selectedFilter !== "all"
                    ? "Try adjusting your search or filter criteria."
                    : "Create your first task to get started."}
                </p>
              </Card>
            )}
          </CardContent>
        </Card>
      </div>
          </TabPanel>
          );
        })}
      </EnhancedTabs>

      {/* Task Detail Modal */}
      <TaskDetailModal
        task={selectedTask}
        isOpen={isTaskDetailOpen}
        onClose={handleTaskDetailClose}
        initialMode={taskDetailMode}
      />
      
      {/* Batch Actions Toolbar */}
      {selectedTaskIds.size > 0 && (
        <BatchActionsToolbar
          selectedIds={Array.from(selectedTaskIds)}
          onClearSelection={clearSelection}
          onDeleted={handleBatchDeleted}
        />
      )}
    </div>
  );
}
