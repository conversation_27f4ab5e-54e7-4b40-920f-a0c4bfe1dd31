import React, { useState, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { EnhancedTabs, TabPanel } from "@/design-system";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import {
  Calendar,
  CheckCircle2,
  Clock,
  AlertTriangle,
  Plus,
  User,
  Flag,
} from "lucide-react";
import {
  format,
  isToday,
  isTomorrow,
  isPast,
  isSameDay,
  startOfDay,
  endOfDay,
} from "date-fns";
import { useAuth } from "@/contexts/AuthContext";
import { useTasks, useOverdueTasks, useTasksDueToday } from "@/hooks/useTasks";
import { Task } from "@/services/TasksService";
import { TaskDetailModal } from "./TaskDetailModal";

interface TaskCalendarWidgetProps {
  selectedDate?: Date;
  onTaskClick?: (task: Task) => void;
  onCreateTask?: () => void;
  showOverdue?: boolean;
  showDueToday?: boolean;
  showSelectedDate?: boolean;
}

export function TaskCalendarWidget({
  selectedDate,
  onTaskClick,
  onCreateTask,
  showOverdue = true,
  showDueToday = true,
  showSelectedDate = true,
}: TaskCalendarWidgetProps) {
  const { user } = useAuth();
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isTaskDetailOpen, setIsTaskDetailOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("overdue");

  // Hooks for task data
  const { data: allTasks = [], isLoading: tasksLoading } = useTasks();
  const { data: overdueTasks = [], isLoading: overdueLoading } =
    useOverdueTasks();
  const { data: tasksDueToday = [], isLoading: dueTodayLoading } =
    useTasksDueToday();

  // Get tasks for selected date
  const selectedDateTasks = useMemo(() => {
    if (!selectedDate) return [];

    return allTasks
      .filter((task) => {
        if (!task.due_date) return false;
        return isSameDay(new Date(task.due_date), selectedDate);
      })
      .sort((a, b) => {
        // Sort by priority (high first) then by status
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const statusOrder = { pending: 3, "in-progress": 2, completed: 1 };

        const aPriority =
          priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
        const bPriority =
          priorityOrder[b.priority as keyof typeof priorityOrder] || 0;

        if (aPriority !== bPriority) return bPriority - aPriority;

        const aStatus = statusOrder[a.status as keyof typeof statusOrder] || 0;
        const bStatus = statusOrder[b.status as keyof typeof statusOrder] || 0;

        return bStatus - aStatus;
      });
  }, [allTasks, selectedDate]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatPillLabel = (value: string) => {
    if (!value) return "";
    // Special handling for "in-progress" to become "In-progress"
    if (value === "in-progress") {
      return "In-progress";
    }
    // Capitalize first letter only
    return value.charAt(0).toUpperCase() + value.slice(1);
  };

  const handleTaskClick = (task: Task) => {
    if (onTaskClick) {
      onTaskClick(task);
    } else {
      setSelectedTask(task);
      setIsTaskDetailOpen(true);
    }
  };

  const formatDueDate = (dueDate: string) => {
    const date = new Date(dueDate);
    if (isToday(date)) return "Today";
    if (isTomorrow(date)) return "Tomorrow";
    if (isPast(date)) return `Overdue`;
    return format(date, "MMM dd");
  };

  const getDateLabel = (date: Date) => {
    if (isToday(date)) return "Today";
    if (isTomorrow(date)) return "Tomorrow";
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    if (isSameDay(date, yesterday)) return "Yesterday";
    return format(date, "EEEE, MMM dd");
  };

  // Reusable component for the stacked status and priority pills
  const TaskPills = ({ status, priority }: { status: string; priority: string }) => (
    <div className="flex flex-col items-end gap-1">
      <Badge
        className={`${getStatusColor(status)} whitespace-nowrap`}
        variant="secondary"
      >
        {formatPillLabel(status)}
      </Badge>
      <Badge
        className={`${getPriorityColor(priority)} whitespace-nowrap`}
        variant="secondary"
      >
        <Flag className="w-3 h-3 mr-1" />
        {formatPillLabel(priority)}
      </Badge>
    </div>
  );

  if (!user) return null;

  const isLoading = tasksLoading || overdueLoading || dueTodayLoading;

  // Create tabs configuration with badges
  const tabs = [
    { 
      value: "overdue", 
      label: "Overdue", 
      icon: AlertTriangle,
      badge: overdueTasks.length > 0 ? overdueTasks.length : undefined
    },
    { 
      value: "today", 
      label: "Today", 
      icon: Clock,
      badge: tasksDueToday.length > 0 ? tasksDueToday.length : undefined
    },
    { 
      value: "selected", 
      label: "Selected", 
      icon: Calendar,
      badge: selectedDateTasks.length > 0 ? selectedDateTasks.length : undefined
    },
  ];

  return (
    <div className="space-y-4 p-4 lg:p-0">
      <div className="hidden sm:block">
        <EnhancedTabs 
          tabs={tabs}
          value={activeTab}
          onValueChange={setActiveTab}
          variant="navigation"
          indicatorStyle="underline"
          size="md"
        >
          <TabPanel value="overdue">
            {showOverdue && overdueTasks.length > 0 && (
              <Card className="border-red-200 bg-red-50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2 text-red-700">
                    <AlertTriangle className="w-5 h-5" />
                    Overdue Tasks ({overdueTasks.length})
                  </CardTitle>
                  <CardDescription className="text-red-600">
                    These tasks are past due
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  {overdueTasks.slice(0, 5).map((task) => (
                    <div
                      key={task.id}
                      className="flex items-center justify-between p-3 bg-white rounded-lg cursor-pointer hover:bg-gray-50 border"
                      onClick={() => handleTaskClick(task)}
                    >
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {task.title}
                        </p>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                          <span className="text-red-600 font-medium">
                            Due {format(new Date(task.due_date!), "MMM dd")}
                          </span>
                          {task.assignee && (
                            <span className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              {task.assignee}
                            </span>
                          )}
                        </div>
                      </div>
                      <TaskPills status={task.status} priority={task.priority} />
                    </div>
                  ))}
                  {overdueTasks.length > 5 && (
                    <p className="text-xs text-muted-foreground text-center pt-2">
                      +{overdueTasks.length - 5} more overdue tasks
                    </p>
                  )}
                </CardContent>
              </Card>
            )}
          </TabPanel>

          <TabPanel value="today">
            {showDueToday && tasksDueToday.length > 0 && (
              <Card className="border-orange-200 bg-orange-50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2 text-orange-700">
                    <Clock className="w-5 h-5" />
                    Due Today ({tasksDueToday.length})
                  </CardTitle>
                  <CardDescription className="text-orange-600">
                    Tasks that need attention today
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  {tasksDueToday.slice(0, 5).map((task) => (
                    <div
                      key={task.id}
                      className="flex items-center justify-between p-3 bg-white rounded-lg cursor-pointer hover:bg-gray-50 border"
                      onClick={() => handleTaskClick(task)}
                    >
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {task.title}
                        </p>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                          {task.assignee && (
                            <span className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              {task.assignee}
                            </span>
                          )}
                        </div>
                      </div>
                      <TaskPills status={task.status} priority={task.priority} />
                    </div>
                  ))}
                  {tasksDueToday.length > 5 && (
                    <p className="text-xs text-muted-foreground text-center pt-2">
                      +{tasksDueToday.length - 5} more tasks due today
                    </p>
                  )}
                </CardContent>
              </Card>
            )}
          </TabPanel>

          <TabPanel value="selected">
            {showSelectedDate && selectedDate && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Tasks for {getDateLabel(selectedDate)}
                  </CardTitle>
                  <CardDescription>
                    {selectedDateTasks.length === 0
                      ? "No tasks scheduled for this date"
                      : `${selectedDateTasks.length} task${selectedDateTasks.length === 1 ? "" : "s"} scheduled`}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  {selectedDateTasks.length === 0 ? (
                    <div className="text-center py-8">
                      <CheckCircle2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-sm text-muted-foreground">
                        No tasks scheduled for this date
                      </p>
                      {onCreateTask && (
                        <Button
                          onClick={onCreateTask}
                          variant="outline"
                          size="sm"
                          className="mt-4"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Create Task
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div>
                      {selectedDateTasks.map((task) => (
                        <div
                          key={task.id}
                          className="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-gray-50 border"
                          onClick={() => handleTaskClick(task)}
                        >
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {task.title}
                            </p>
                            {task.description && (
                              <p className="text-xs text-muted-foreground truncate mt-1">
                                {task.description}
                              </p>
                            )}
                            <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                              {task.assignee && (
                                <span className="flex items-center gap-1">
                                  <User className="w-3 h-3" />
                                  {task.assignee}
                                </span>
                              )}
                              <span className="text-xs">
                                {task.category.charAt(0).toUpperCase() +
                                  task.category.slice(1)}
                              </span>
                            </div>
                          </div>
                          <TaskPills status={task.status} priority={task.priority} />
                        </div>
                      ))}
                      {onCreateTask && (
                        <div className="pt-2">
                          <Button
                            onClick={onCreateTask}
                            variant="outline"
                            size="sm"
                            className="w-full"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add Task for {getDateLabel(selectedDate)}
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabPanel>
        </EnhancedTabs>
      </div>

      <div className="block sm:hidden">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="overdue">
            <AccordionTrigger>Overdue</AccordionTrigger>
            <AccordionContent>
              {showOverdue && overdueTasks.length > 0 && (
                <Card className="border-red-200 bg-red-50">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2 text-red-700">
                      <AlertTriangle className="w-5 h-5" />
                      Overdue Tasks ({overdueTasks.length})
                    </CardTitle>
                    <CardDescription className="text-red-600">
                      These tasks are past their due date
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {overdueTasks.slice(0, 5).map((task) => (
                      <div
                        key={task.id}
                        className="flex items-center justify-between p-3 bg-white rounded-lg cursor-pointer hover:bg-gray-50 border"
                        onClick={() => handleTaskClick(task)}
                      >
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {task.title}
                          </p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                            <span className="text-red-600 font-medium">
                              Due {format(new Date(task.due_date!), "MMM dd")}
                            </span>
                            {task.assignee && (
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {task.assignee}
                              </span>
                            )}
                          </div>
                        </div>
                        <TaskPills status={task.status} priority={task.priority} />
                      </div>
                    ))}
                    {overdueTasks.length > 5 && (
                      <p className="text-xs text-muted-foreground text-center pt-2">
                        +{overdueTasks.length - 5} more overdue tasks
                      </p>
                    )}
                  </CardContent>
                </Card>
              )}
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="today">
            <AccordionTrigger>Today</AccordionTrigger>
            <AccordionContent>
              {showDueToday && tasksDueToday.length > 0 && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2 text-orange-700">
                      <Clock className="w-5 h-5" />
                      Due Today ({tasksDueToday.length})
                    </CardTitle>
                    <CardDescription className="text-orange-600">
                      Tasks that need attention today
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {tasksDueToday.slice(0, 5).map((task) => (
                      <div
                        key={task.id}
                        className="flex items-center justify-between p-3 bg-white rounded-lg cursor-pointer hover:bg-gray-50 border"
                        onClick={() => handleTaskClick(task)}
                      >
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {task.title}
                          </p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                            {task.assignee && (
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {task.assignee}
                              </span>
                            )}
                          </div>
                        </div>
                        <TaskPills status={task.status} priority={task.priority} />
                      </div>
                    ))}
                    {tasksDueToday.length > 5 && (
                      <p className="text-xs text-muted-foreground text-center pt-2">
                        +{tasksDueToday.length - 5} more tasks due today
                      </p>
                    )}
                  </CardContent>
                </Card>
              )}
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="selected">
            <AccordionTrigger>Selected Date</AccordionTrigger>
            <AccordionContent>
              {showSelectedDate && selectedDate && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      Tasks for {getDateLabel(selectedDate)}
                    </CardTitle>
                    <CardDescription>
                      {selectedDateTasks.length === 0
                        ? "No tasks scheduled for this date"
                        : `${selectedDateTasks.length} task${selectedDateTasks.length === 1 ? "" : "s"} scheduled`}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {selectedDateTasks.length === 0 ? (
                      <div className="text-center py-8">
                        <CheckCircle2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-sm text-muted-foreground">
                          No tasks scheduled for this date
                        </p>
                        {onCreateTask && (
                          <Button
                            onClick={onCreateTask}
                            variant="outline"
                            size="sm"
                            className="mt-4"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Create Task
                          </Button>
                        )}
                      </div>
                    ) : (
                      <div>
                        {selectedDateTasks.map((task) => (
                          <div
                            key={task.id}
                            className="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-gray-50 border"
                            onClick={() => handleTaskClick(task)}
                          >
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">
                                {task.title}
                              </p>
                              {task.description && (
                                <p className="text-xs text-muted-foreground truncate mt-1">
                                  {task.description}
                                </p>
                              )}
                              <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                                {task.assignee && (
                                  <span className="flex items-center gap-1">
                                    <User className="w-3 h-3" />
                                    {task.assignee}
                                  </span>
                                )}
                                <span className="text-xs">
                                  {task.category.charAt(0).toUpperCase() +
                                    task.category.slice(1)}
                                </span>
                              </div>
                            </div>
                            <TaskPills status={task.status} priority={task.priority} />
                          </div>
                        ))}
                        {onCreateTask && (
                          <div className="pt-2">
                            <Button
                              onClick={onCreateTask}
                              variant="outline"
                              size="sm"
                              className="w-full"
                            >
                              <Plus className="w-4 h-4 mr-2" />
                              Add Task for {getDateLabel(selectedDate)}
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      {/* Task Detail Modal */}
      <TaskDetailModal
        task={selectedTask}
        isOpen={isTaskDetailOpen}
        onClose={() => {
          setIsTaskDetailOpen(false);
          setSelectedTask(null);
        }}
        initialMode="view"
      />
    </div>
  );
}
