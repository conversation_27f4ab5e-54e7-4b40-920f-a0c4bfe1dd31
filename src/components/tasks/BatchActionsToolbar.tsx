import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Trash2,
  X,
  Loader2,
  CheckCircle2,
  Flag,
  FolderOpen,
  User,
} from "lucide-react";
import {
  useBulkUpdateTaskStatus,
  useBulkUpdateTaskPriority,
  useBulkAssignTasks,
  useBulkUpdateTaskCategory,
  useBulkDeleteTasks,
} from "@/hooks/useTasks";

interface BatchActionsToolbarProps {
  selectedIds: string[];
  onClearSelection: () => void;
  onDeleted?: () => void;
}

export function BatchActionsToolbar({
  selectedIds,
  onClearSelection,
  onDeleted,
}: BatchActionsToolbarProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [assigneeInput, setAssigneeInput] = useState("");
  const [showAssignDialog, setShowAssignDialog] = useState(false);

  // Bulk operation hooks
  const bulkUpdateStatus = useBulkUpdateTaskStatus();
  const bulkUpdatePriority = useBulkUpdateTaskPriority();
  const bulkUpdateCategory = useBulkUpdateTaskCategory();
  const bulkAssignTasks = useBulkAssignTasks();
  const bulkDeleteTasks = useBulkDeleteTasks();

  // Check if any operation is pending
  const isAnyOperationPending =
    bulkUpdateStatus.isPending ||
    bulkUpdatePriority.isPending ||
    bulkUpdateCategory.isPending ||
    bulkAssignTasks.isPending ||
    bulkDeleteTasks.isPending;

  const handleStatusChange = async (status: "pending" | "in-progress" | "completed") => {
    await bulkUpdateStatus.mutateAsync({
      taskIds: selectedIds,
      status,
    });
  };

  const handlePriorityChange = async (priority: "low" | "medium" | "high") => {
    await bulkUpdatePriority.mutateAsync({
      taskIds: selectedIds,
      priority,
    });
  };

  const handleCategoryChange = async (
    category: "recruitment" | "screening" | "interview" | "onboarding" | "general"
  ) => {
    await bulkUpdateCategory.mutateAsync({
      taskIds: selectedIds,
      category,
    });
  };

  const handleAssign = async () => {
    await bulkAssignTasks.mutateAsync({
      taskIds: selectedIds,
      assignee: assigneeInput.trim() || null,
    });
    setShowAssignDialog(false);
    setAssigneeInput("");
  };

  const handleDelete = async () => {
    await bulkDeleteTasks.mutateAsync(selectedIds);
    setShowDeleteDialog(false);
    if (onDeleted) {
      onDeleted();
    } else {
      onClearSelection();
    }
  };

  return (
    <>
      <Card className="glass-md glass-inset fixed bottom-4 left-1/2 -translate-x-1/2 w-[calc(100%-2rem)] max-w-4xl z-50">
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              {isAnyOperationPending && (
                <Loader2 className="w-4 h-4 animate-spin" />
              )}
              <span className="text-sm font-medium">
                {selectedIds.length} task{selectedIds.length > 1 ? "s" : ""} selected
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearSelection}
              disabled={isAnyOperationPending}
              aria-label="Clear selection"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {/* Status Update */}
            <Select
              onValueChange={handleStatusChange}
              disabled={isAnyOperationPending}
            >
              <SelectTrigger
                className="w-[140px]"
                aria-label="Update status"
              >
                <CheckCircle2 className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>

            {/* Priority Update */}
            <Select
              onValueChange={handlePriorityChange}
              disabled={isAnyOperationPending}
            >
              <SelectTrigger
                className="w-[140px]"
                aria-label="Update priority"
              >
                <Flag className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
              </SelectContent>
            </Select>

            {/* Category Update */}
            <Select
              onValueChange={handleCategoryChange}
              disabled={isAnyOperationPending}
            >
              <SelectTrigger
                className="w-[160px]"
                aria-label="Update category"
              >
                <FolderOpen className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recruitment">Recruitment</SelectItem>
                <SelectItem value="screening">Screening</SelectItem>
                <SelectItem value="interview">Interview</SelectItem>
                <SelectItem value="onboarding">Onboarding</SelectItem>
                <SelectItem value="general">General</SelectItem>
              </SelectContent>
            </Select>

            {/* Assign Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAssignDialog(true)}
              disabled={isAnyOperationPending}
              aria-label="Assign tasks"
            >
              <User className="w-4 h-4 mr-2" />
              Assign
            </Button>

            {/* Delete Button */}
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setShowDeleteDialog(true)}
              disabled={isAnyOperationPending}
              aria-label="Delete tasks"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </Card>

      {/* Assign Dialog */}
      <AlertDialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Assign Tasks</AlertDialogTitle>
            <AlertDialogDescription>
              Assign {selectedIds.length} task{selectedIds.length > 1 ? "s" : ""} to a team member.
              Leave empty to unassign.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <Input
              placeholder="Enter assignee name"
              value={assigneeInput}
              onChange={(e) => setAssigneeInput(e.target.value)}
              aria-label="Assignee name"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleAssign}
              disabled={bulkAssignTasks.isPending}
            >
              {bulkAssignTasks.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Assigning...
                </>
              ) : (
                "Assign"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tasks</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedIds.length} task
              {selectedIds.length > 1 ? "s" : ""}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={bulkDeleteTasks.isPending}
            >
              {bulkDeleteTasks.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
