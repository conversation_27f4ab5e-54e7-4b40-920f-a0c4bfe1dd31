/**
 * Enhanced Screening Panel - Advanced AI-powered candidate screening
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { 
  Loader2, 
  Brain, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp,
  Users,
  MapPin,
  DollarSign,
  Clock,
  Target,
  MessageSquare,
  Shield
} from 'lucide-react';
import { useScreeningAgent, useScreeningCriteria, useScreeningAnalysis } from '@/hooks/useScreeningAgent';
import { CandidateType } from '@/types/candidate';
import { ScreeningResult } from '@/services/agents/ScreeningAgent';

interface EnhancedScreeningPanelProps {
  candidate?: CandidateType;
  onScreeningComplete?: (result: ScreeningResult) => void;
}

export function EnhancedScreeningPanel({ candidate, onScreeningComplete }: EnhancedScreeningPanelProps) {
  const [formData, setFormData] = useState({
    jobTitle: '',
    requiredSkills: '',
    experienceLevel: 'mid',
    department: '',
    location: '',
    remoteOk: false,
    salaryMin: '',
    salaryMax: '',
    customCriteria: '',
  });

  const [includeInterviewQuestions, setIncludeInterviewQuestions] = useState(true);
  const [includeRiskAssessment, setIncludeRiskAssessment] = useState(true);
  const [autoSave, setAutoSave] = useState(true);

  const { screenCandidate, isScreening, lastResult, error, clearError, saveScreeningResult } = useScreeningAgent({
    includeInterviewQuestions,
    includeRiskAssessment,
    autoSave,
  });

  const { createCriteriaFromForm } = useScreeningCriteria();
  const { getScoreColor, getScoreBadgeVariant, getConfidenceLabel, getRecommendation } = useScreeningAnalysis();

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleScreening = async () => {
    if (!candidate) {
      return;
    }

    clearError();

    try {
      const criteria = createCriteriaFromForm({
        ...formData,
        salaryMin: formData.salaryMin ? parseInt(formData.salaryMin) : undefined,
        salaryMax: formData.salaryMax ? parseInt(formData.salaryMax) : undefined,
      });

      const result = await screenCandidate(candidate, criteria);
      onScreeningComplete?.(result);
    } catch (err) {
      console.error('Screening failed:', err);
    }
  };

  const handleSaveResult = async () => {
    if (!candidate || !lastResult) return;

    try {
      await saveScreeningResult(candidate.id, lastResult);
    } catch (err) {
      console.error('Failed to save result:', err);
    }
  };

  const isFormValid = formData.jobTitle && formData.requiredSkills && formData.department;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Enhanced AI Screening
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Candidate Info */}
          {candidate && (
            <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-medium mb-2">Candidate: {candidate.name}</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Role:</span>
                  <p className="font-medium">{candidate.role}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Experience:</span>
                  <p className="font-medium">{candidate.experience}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Location:</span>
                  <p className="font-medium">{candidate.location || 'Not specified'}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Remote:</span>
                  <p className="font-medium">{candidate.remotePreference || 'Not specified'}</p>
                </div>
              </div>
            </div>
          )}

          {/* Screening Criteria Form */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="jobTitle">Job Title *</Label>
              <Input
                id="jobTitle"
                value={formData.jobTitle}
                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                placeholder="e.g., Senior Frontend Developer"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="department">Department *</Label>
              <Input
                id="department"
                value={formData.department}
                onChange={(e) => handleInputChange('department', e.target.value)}
                placeholder="e.g., Engineering"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="experienceLevel">Experience Level</Label>
              <Select value={formData.experienceLevel} onValueChange={(value) => handleInputChange('experienceLevel', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="junior">Junior</SelectItem>
                  <SelectItem value="mid">Mid-level</SelectItem>
                  <SelectItem value="senior">Senior</SelectItem>
                  <SelectItem value="lead">Lead</SelectItem>
                  <SelectItem value="executive">Executive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="e.g., San Francisco, CA"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="salaryMin">Min Salary</Label>
              <Input
                id="salaryMin"
                type="number"
                value={formData.salaryMin}
                onChange={(e) => handleInputChange('salaryMin', e.target.value)}
                placeholder="e.g., 120000"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="salaryMax">Max Salary</Label>
              <Input
                id="salaryMax"
                type="number"
                value={formData.salaryMax}
                onChange={(e) => handleInputChange('salaryMax', e.target.value)}
                placeholder="e.g., 180000"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="requiredSkills">Required Skills *</Label>
            <Textarea
              id="requiredSkills"
              value={formData.requiredSkills}
              onChange={(e) => handleInputChange('requiredSkills', e.target.value)}
              placeholder="e.g., React, TypeScript, Node.js, GraphQL"
              rows={2}
            />
            <p className="text-sm text-muted-foreground">Separate skills with commas</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customCriteria">Custom Criteria (JSON)</Label>
            <Textarea
              id="customCriteria"
              value={formData.customCriteria}
              onChange={(e) => handleInputChange('customCriteria', e.target.value)}
              placeholder='{"leadership": true, "startupExperience": "preferred"}'
              rows={2}
            />
          </div>

          {/* Options */}
          <div className="space-y-4">
            <Separator />
            <h3 className="font-medium">Screening Options</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="remoteOk"
                  checked={formData.remoteOk}
                  onCheckedChange={(checked) => handleInputChange('remoteOk', checked)}
                />
                <Label htmlFor="remoteOk">Remote OK</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="includeQuestions"
                  checked={includeInterviewQuestions}
                  onCheckedChange={setIncludeInterviewQuestions}
                />
                <Label htmlFor="includeQuestions">Interview Questions</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="includeRisk"
                  checked={includeRiskAssessment}
                  onCheckedChange={setIncludeRiskAssessment}
                />
                <Label htmlFor="includeRisk">Risk Assessment</Label>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="autoSave"
                checked={autoSave}
                onCheckedChange={setAutoSave}
              />
              <Label htmlFor="autoSave">Auto-save results to candidate profile</Label>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={handleScreening} 
              disabled={!candidate || !isFormValid || isScreening}
              className="flex-1"
            >
              {isScreening ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing Candidate...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4 mr-2" />
                  Run AI Screening
                </>
              )}
            </Button>

            {lastResult && !autoSave && (
              <Button variant="outline" onClick={handleSaveResult}>
                Save Result
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Results Display */}
      {lastResult && (
        <ScreeningResultsDisplay result={lastResult} />
      )}
    </div>
  );
}

// Results Display Component
function ScreeningResultsDisplay({ result }: { result: ScreeningResult }) {
  const { getScoreColor, getScoreBadgeVariant, getConfidenceLabel, getRecommendation } = useScreeningAnalysis();
  const recommendation = getRecommendation(result);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Screening Results
          </span>
          <Badge variant={getScoreBadgeVariant(result.overallScore)}>
            {result.overallScore}% Overall Score
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className={`text-2xl font-bold ${getScoreColor(result.overallScore)}`}>
              {result.overallScore}%
            </div>
            <p className="text-sm text-muted-foreground">Overall Score</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Math.round(result.confidenceLevel * 100)}%
            </div>
            <p className="text-sm text-muted-foreground">{getConfidenceLabel(result.confidenceLevel)}</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {result.executionTimeMs}ms
            </div>
            <p className="text-sm text-muted-foreground">Analysis Time</p>
          </div>
        </div>

        {/* Recommendation */}
        <Alert>
          <Target className="h-4 w-4" />
          <AlertDescription>
            <strong>Recommendation:</strong> {recommendation.message}
          </AlertDescription>
        </Alert>

        {/* Criteria Scores */}
        <div>
          <h3 className="font-medium mb-3">Detailed Scores</h3>
          <div className="space-y-3">
            {Object.entries(result.criteriaScores).map(([key, score]) => (
              <div key={key} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                  <span className={getScoreColor(score)}>{score}%</span>
                </div>
                <Progress value={score} className="h-2" />
              </div>
            ))}
          </div>
        </div>

        {/* Analysis */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-green-600 mb-2">Strengths</h4>
            <ul className="text-sm space-y-1">
              {result.analysis.strengths.map((strength, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                  {strength}
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-orange-600 mb-2">Concerns</h4>
            <ul className="text-sm space-y-1">
              {result.analysis.concerns.map((concern, index) => (
                <li key={index} className="flex items-start gap-2">
                  <AlertTriangle className="h-3 w-3 text-orange-500 mt-0.5 flex-shrink-0" />
                  {concern}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Reasoning */}
        <div>
          <h4 className="font-medium mb-2">AI Reasoning</h4>
          <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
            {result.reasoning}
          </p>
        </div>

        {/* Next Steps */}
        {result.nextSteps.length > 0 && (
          <div>
            <h4 className="font-medium mb-2">Recommended Next Steps</h4>
            <ul className="text-sm space-y-1">
              {result.nextSteps.map((step, index) => (
                <li key={index} className="flex items-start gap-2">
                  <TrendingUp className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                  {step}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Interview Questions */}
        {result.interviewQuestions.length > 0 && (
          <div>
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Suggested Interview Questions
            </h4>
            <ul className="text-sm space-y-2">
              {result.interviewQuestions.map((question, index) => (
                <li key={index} className="bg-muted/50 p-2 rounded">
                  {index + 1}. {question}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Risk Factors */}
        {result.riskFactors.length > 0 && (
          <div>
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Risk Factors
            </h4>
            <ul className="text-sm space-y-1">
              {result.riskFactors.map((risk, index) => (
                <li key={index} className="flex items-start gap-2">
                  <AlertTriangle className="h-3 w-3 text-red-500 mt-0.5 flex-shrink-0" />
                  {risk}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
