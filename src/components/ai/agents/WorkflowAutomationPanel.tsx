/**
 * Workflow Automation Panel - Proactive workflow orchestration and bottleneck detection
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { EnhancedTabs, TabPanel } from '@/design-system';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Loader2, 
  Workflow, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Zap,
  Clock,
  Users,
  DollarSign,
  Target
} from 'lucide-react';
import { 
  useWorkflowAutomation, 
  useBottleneckAnalysis, 
  useOptimizationAnalysis,
  useWorkflowMonitoring 
} from '@/hooks/useWorkflowAutomation';
import { BottleneckAnalysis, WorkflowOptimization } from '@/services/agents/WorkflowAutomationAgent';

export function WorkflowAutomationPanel() {
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<string>('all');
  const [automationLevel, setAutomationLevel] = useState<'manual' | 'assisted' | 'autonomous'>('assisted');
  const [isMonitoringActive, setIsMonitoringActive] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { value: 'overview', label: 'Overview', icon: BarChart3 },
    { value: 'bottlenecks', label: 'Bottlenecks', icon: AlertTriangle },
    { value: 'optimizations', label: 'Optimizations', icon: Zap },
    { value: 'monitoring', label: 'Monitoring', icon: Clock },
  ];

  const {
    analyzeWorkflowHealth,
    optimizeWorkflow,
    monitorWorkflow,
    isAnalyzing,
    isOptimizing,
    isMonitoring,
    lastAnalysis,
    bottlenecks,
    optimizations,
    error,
    clearError,
    startMonitoring,
    stopMonitoring,
  } = useWorkflowAutomation({
    automationLevel,
    autoMonitor: isMonitoringActive,
    monitoringInterval: 30000, // 30 seconds
  });

  const { 
    getSeverityColor, 
    getSeverityBadgeVariant, 
    getBottleneckIcon, 
    formatImpact,
    prioritizeBottlenecks 
  } = useBottleneckAnalysis();

  const { 
    getOptimizationIcon, 
    calculateROI, 
    prioritizeOptimizations,
    getImplementationComplexity 
  } = useOptimizationAnalysis();

  const { getHealthScore, getRecommendedActions } = useWorkflowMonitoring();

  const handleAnalyzeWorkflow = async () => {
    if (selectedWorkflowId === 'all') {
      // Analyze all workflows if "all" selected
      await analyzeWorkflowHealth();
    } else {
      await analyzeWorkflowHealth(selectedWorkflowId);
    }
  };

  const handleOptimizeWorkflow = async () => {
    const workflowId = selectedWorkflowId === 'all' ? undefined : selectedWorkflowId;
    await optimizeWorkflow(workflowId);
  };

  const handleToggleMonitoring = () => {
    if (isMonitoringActive) {
      stopMonitoring();
      setIsMonitoringActive(false);
    } else {
      if (selectedWorkflowId && selectedWorkflowId !== 'all') {
        startMonitoring(selectedWorkflowId);
        setIsMonitoringActive(true);
      }
    }
  };

  const healthScore = getHealthScore(bottlenecks, optimizations);
  const recommendedActions = getRecommendedActions(bottlenecks, optimizations);
  const prioritizedBottlenecks = prioritizeBottlenecks(bottlenecks);
  const prioritizedOptimizations = prioritizeOptimizations(optimizations);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Workflow className="h-5 w-5" />
            Workflow Automation Agent
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Workflow (Optional)</label>
              <Select value={selectedWorkflowId} onValueChange={setSelectedWorkflowId}>
                <SelectTrigger>
                  <SelectValue placeholder="All workflows" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Workflows</SelectItem>
                  <SelectItem value="candidate-screening">Candidate Screening</SelectItem>
                  <SelectItem value="interview-process">Interview Process</SelectItem>
                  <SelectItem value="onboarding">Onboarding</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Automation Level</label>
              <Select value={automationLevel} onValueChange={(value: any) => setAutomationLevel(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manual">Manual</SelectItem>
                  <SelectItem value="assisted">Assisted</SelectItem>
                  <SelectItem value="autonomous">Autonomous</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Monitoring</label>
              <Button
                variant={isMonitoringActive ? "destructive" : "default"}
                onClick={handleToggleMonitoring}
                disabled={selectedWorkflowId === 'all'}
                className="w-full"
              >
                {isMonitoringActive ? (
                  <>
                    <Pause className="h-4 w-4 mr-2" />
                    Stop Monitoring
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Start Monitoring
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={handleAnalyzeWorkflow} 
              disabled={isAnalyzing}
              className="flex-1"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analyze Workflow Health
                </>
              )}
            </Button>

            <Button 
              onClick={handleOptimizeWorkflow} 
              disabled={isOptimizing || optimizations.length === 0}
              variant="outline"
            >
              {isOptimizing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Apply Optimizations
                </>
              )}
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Results */}
      {lastAnalysis && (
        <EnhancedTabs
          tabs={tabs}
          value={activeTab}
          onValueChange={setActiveTab}
          variant="navigation"
          indicatorStyle="underline"
          size="md"
        >
          {/* Overview Tab */}
          <TabPanel value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{healthScore.score}</div>
                  <p className="text-sm text-muted-foreground">Health Score</p>
                  <Badge variant={healthScore.status === 'excellent' ? 'default' : 'secondary'}>
                    {healthScore.status}
                  </Badge>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <AlertTriangle className="h-8 w-8 mx-auto text-orange-500 mb-2" />
                  <div className="text-2xl font-bold">{bottlenecks.length}</div>
                  <p className="text-sm text-muted-foreground">Bottlenecks</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <TrendingUp className="h-8 w-8 mx-auto text-green-500 mb-2" />
                  <div className="text-2xl font-bold">{optimizations.length}</div>
                  <p className="text-sm text-muted-foreground">Optimizations</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <Clock className="h-8 w-8 mx-auto text-purple-500 mb-2" />
                  <div className="text-2xl font-bold">{lastAnalysis.executionTimeMs}ms</div>
                  <p className="text-sm text-muted-foreground">Analysis Time</p>
                </CardContent>
              </Card>
            </div>

            {/* Health Factors */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Health Factors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Overall Health</span>
                    <div className="flex items-center gap-2">
                      <Progress value={healthScore.score} className="w-20" />
                      <span className="text-sm font-medium">{healthScore.score}%</span>
                    </div>
                  </div>
                  {healthScore.factors.map((factor, index) => (
                    <div key={index} className="text-sm text-muted-foreground">
                      • {factor}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recommended Actions */}
            {recommendedActions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Recommended Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {recommendedActions.map((action, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <Target className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                        {action}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </TabPanel>

          {/* Bottlenecks Tab */}
          <TabPanel value="bottlenecks" className="space-y-4">
            {prioritizedBottlenecks.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-4" />
                  <h3 className="font-medium mb-2">No Bottlenecks Detected</h3>
                  <p className="text-sm text-muted-foreground">
                    Your workflow is running smoothly without any significant bottlenecks.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {prioritizedBottlenecks.map((bottleneck, index) => (
                  <BottleneckCard key={index} bottleneck={bottleneck} />
                ))}
              </div>
            )}
          </TabPanel>

          {/* Optimizations Tab */}
          <TabPanel value="optimizations" className="space-y-4">
            {prioritizedOptimizations.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-4" />
                  <h3 className="font-medium mb-2">No Optimizations Available</h3>
                  <p className="text-sm text-muted-foreground">
                    Your workflow is already optimized. Run analysis again later to check for new opportunities.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {prioritizedOptimizations.map((optimization, index) => (
                  <OptimizationCard key={index} optimization={optimization} />
                ))}
              </div>
            )}
          </TabPanel>

          {/* Monitoring Tab */}
          <TabPanel value="monitoring" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <RefreshCw className={`h-4 w-4 ${isMonitoringActive ? 'animate-spin' : ''}`} />
                  Real-time Monitoring
                  {isMonitoringActive && <Badge variant="outline">Active</Badge>}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isMonitoringActive ? (
                  <div className="space-y-4">
                    <Alert>
                      <RefreshCw className="h-4 w-4" />
                      <AlertDescription>
                        Monitoring workflow execution every 30 seconds. 
                        {selectedWorkflowId ? ` Watching: ${selectedWorkflowId}` : ' Watching all workflows.'}
                      </AlertDescription>
                    </Alert>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-lg font-bold text-green-600">98.5%</div>
                          <p className="text-sm text-muted-foreground">Success Rate</p>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-lg font-bold text-blue-600">2.3s</div>
                          <p className="text-sm text-muted-foreground">Avg Response Time</p>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-lg font-bold text-purple-600">156</div>
                          <p className="text-sm text-muted-foreground">Active Processes</p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Play className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="font-medium mb-2">Monitoring Inactive</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Start monitoring to track workflow performance in real-time.
                    </p>
                    <Button onClick={handleToggleMonitoring} disabled={selectedWorkflowId === 'all'}>
                      <Play className="h-4 w-4 mr-2" />
                      Start Monitoring
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabPanel>
        </EnhancedTabs>
      )}
    </div>
  );
}

// Bottleneck Card Component
function BottleneckCard({ bottleneck }: { bottleneck: BottleneckAnalysis }) {
  const { getSeverityColor, getSeverityBadgeVariant, getBottleneckIcon, formatImpact } = useBottleneckAnalysis();
  const impact = formatImpact(bottleneck.impact);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <span className="text-lg">{getBottleneckIcon(bottleneck.bottleneckType)}</span>
            {bottleneck.location}
          </span>
          <Badge variant={getSeverityBadgeVariant(bottleneck.severity)}>
            {bottleneck.severity.toUpperCase()}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm">{bottleneck.description}</p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-blue-500" />
            {impact.candidates}
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-orange-500" />
            {impact.delay}
          </div>
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-green-500" />
            {impact.cost}
          </div>
        </div>

        {bottleneck.recommendations.length > 0 && (
          <div>
            <h4 className="font-medium text-sm mb-2">Recommendations</h4>
            <ul className="text-sm space-y-1">
              {bottleneck.recommendations.map((rec, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        )}

        {bottleneck.automatedSolutions.length > 0 && (
          <div>
            <h4 className="font-medium text-sm mb-2">Automated Solutions</h4>
            <ul className="text-sm space-y-1">
              {bottleneck.automatedSolutions.map((solution, index) => (
                <li key={index} className="flex items-start gap-2">
                  <Zap className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                  {solution}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Optimization Card Component
function OptimizationCard({ optimization }: { optimization: WorkflowOptimization }) {
  const { getOptimizationIcon, calculateROI, getImplementationComplexity } = useOptimizationAnalysis();
  const roi = calculateROI(optimization);
  const complexity = getImplementationComplexity(optimization);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <span className="text-lg">{getOptimizationIcon(optimization.optimizationType)}</span>
            {optimization.optimizationType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </span>
          <Badge variant="outline">
            {roi.roi > 0 ? `+${Math.round(roi.roi)}% ROI` : 'Low ROI'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium text-sm mb-1">Current State</h4>
          <p className="text-sm text-muted-foreground">{optimization.currentState}</p>
        </div>
        
        <div>
          <h4 className="font-medium text-sm mb-1">Proposed State</h4>
          <p className="text-sm">{optimization.proposedState}</p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="font-bold text-green-600">{optimization.expectedBenefits.timeReduction}%</div>
            <p className="text-muted-foreground">Time Reduction</p>
          </div>
          <div className="text-center">
            <div className="font-bold text-blue-600">${optimization.expectedBenefits.costSavings}</div>
            <p className="text-muted-foreground">Cost Savings</p>
          </div>
          <div className="text-center">
            <div className="font-bold text-purple-600">{optimization.expectedBenefits.qualityImprovement}%</div>
            <p className="text-muted-foreground">Quality Improvement</p>
          </div>
          <div className="text-center">
            <div className="font-bold text-orange-600">{optimization.expectedBenefits.candidateExperience}%</div>
            <p className="text-muted-foreground">Candidate Experience</p>
          </div>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span>Implementation Complexity:</span>
          <Badge variant={complexity.level === 'low' ? 'default' : complexity.level === 'medium' ? 'secondary' : 'destructive'}>
            {complexity.level} ({complexity.estimatedTime})
          </Badge>
        </div>

        {optimization.implementationSteps.length > 0 && (
          <div>
            <h4 className="font-medium text-sm mb-2">Implementation Steps</h4>
            <ul className="text-sm space-y-1">
              {optimization.implementationSteps.slice(0, 3).map((step, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-muted-foreground">{index + 1}.</span>
                  {step}
                </li>
              ))}
              {optimization.implementationSteps.length > 3 && (
                <li className="text-muted-foreground">
                  +{optimization.implementationSteps.length - 3} more steps...
                </li>
              )}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
