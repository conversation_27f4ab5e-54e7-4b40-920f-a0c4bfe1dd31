/**
 * Agent Test Panel - Component for testing autonomous agent functionality
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Play, CheckCircle, XCircle, Clock, Zap } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { AutonomousAgent, AgentTask, AgentResult } from '@/services/agents/AutonomousAgent';
import { SimpleLLMTest } from './SimpleLLMTest';

export function AgentTestPanel() {
  const { user } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [taskType, setTaskType] = useState<'candidate_screening' | 'interview_scheduling' | 'candidate_communication' | 'analysis'>('candidate_screening');
  const [taskDescription, setTaskDescription] = useState('');
  const [taskContext, setTaskContext] = useState('{}');
  const [result, setResult] = useState<AgentResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleRunTest = async () => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    if (!taskDescription.trim()) {
      setError('Please provide a task description');
      return;
    }

    setIsRunning(true);
    setError(null);
    setResult(null);

    try {
      // Parse context JSON
      let context: Record<string, any> = {};
      if (taskContext.trim()) {
        try {
          context = JSON.parse(taskContext);
        } catch (e) {
          throw new Error('Invalid JSON in task context');
        }
      }

      // Create agent
      const agent = new AutonomousAgent(user.id, {
        securityLevel: 'safe',
        permissions: ['read:candidates', 'read:jobs'],
      });

      // Create task
      const task: AgentTask = {
        id: `test_${Date.now()}`,
        type: taskType,
        description: taskDescription,
        context,
        priority: 'medium',
        maxExecutionTime: 60000, // 60 seconds
      };

      // Execute task
      const taskResult = await agent.executeTask(task);
      setResult(taskResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsRunning(false);
    }
  };

  const getTaskTypeDescription = (type: string) => {
    const descriptions = {
      candidate_screening: 'Analyze candidate profiles and provide fit scores for job positions',
      interview_scheduling: 'Coordinate interview scheduling between candidates and interviewers',
      candidate_communication: 'Draft and send personalized communications to candidates',
      analysis: 'Analyze recruitment data and provide insights and recommendations',
    };
    return descriptions[type as keyof typeof descriptions] || '';
  };

  const getExampleContext = (type: string) => {
    const examples = {
      candidate_screening: JSON.stringify({
        candidateId: 'candidate-uuid-here',
        jobId: 'job-uuid-here',
        requiredSkills: ['React', 'TypeScript', 'Node.js'],
        experienceLevel: 'senior'
      }, null, 2),
      interview_scheduling: JSON.stringify({
        candidateId: 'candidate-uuid-here',
        interviewerIds: ['interviewer1-uuid', 'interviewer2-uuid'],
        interviewType: 'technical',
        duration: 60
      }, null, 2),
      candidate_communication: JSON.stringify({
        candidateId: 'candidate-uuid-here',
        messageType: 'interview_invitation',
        jobTitle: 'Senior Frontend Developer'
      }, null, 2),
      analysis: JSON.stringify({
        analysisType: 'hiring_funnel',
        timeRange: '30_days',
        department: 'engineering'
      }, null, 2),
    };
    return examples[type as keyof typeof examples] || '{}';
  };

  const handleTaskTypeChange = (newType: string) => {
    const type = newType as typeof taskType;
    setTaskType(type);
    setTaskContext(getExampleContext(type));
    
    // Set example descriptions
    const exampleDescriptions = {
      candidate_screening: 'Screen the candidate for the senior frontend developer position and provide a fit score',
      interview_scheduling: 'Schedule a technical interview for the candidate with available interviewers',
      candidate_communication: 'Send an interview invitation email to the candidate',
      analysis: 'Analyze the hiring funnel performance for the engineering department',
    };
    setTaskDescription(exampleDescriptions[type]);
  };

  return (
    <div className="space-y-6">
      <SimpleLLMTest />

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Autonomous Agent Test Panel
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Task Type</label>
            <Select value={taskType} onValueChange={handleTaskTypeChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="candidate_screening">Candidate Screening</SelectItem>
                <SelectItem value="interview_scheduling">Interview Scheduling</SelectItem>
                <SelectItem value="candidate_communication">Candidate Communication</SelectItem>
                <SelectItem value="analysis">Analysis</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground mt-1">
              {getTaskTypeDescription(taskType)}
            </p>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Task Description</label>
            <Textarea
              value={taskDescription}
              onChange={(e) => setTaskDescription(e.target.value)}
              placeholder="Describe what you want the agent to do..."
              rows={3}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Task Context (JSON)</label>
            <Textarea
              value={taskContext}
              onChange={(e) => setTaskContext(e.target.value)}
              placeholder="Provide context data as JSON..."
              rows={6}
              className="font-mono text-sm"
            />
          </div>

          <Button 
            onClick={handleRunTest} 
            disabled={isRunning || !user}
            className="w-full"
          >
            {isRunning ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Running Agent...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Run Agent Test
              </>
            )}
          </Button>

          {error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              Agent Execution Result
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm font-medium">Status</p>
                <Badge variant={result.success ? "default" : "destructive"}>
                  {result.success ? 'Success' : 'Failed'}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium">Execution Time</p>
                <p className="text-sm text-muted-foreground flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {result.executionTimeMs}ms
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Tools Used</p>
                <p className="text-sm text-muted-foreground">{result.toolsUsed.length}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Confidence</p>
                <p className="text-sm text-muted-foreground">
                  {Math.round(result.confidenceScore * 100)}%
                </p>
              </div>
            </div>

            {result.toolsUsed.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Tools Used</p>
                <div className="flex flex-wrap gap-2">
                  {result.toolsUsed.map((tool, index) => (
                    <Badge key={index} variant="outline">{tool}</Badge>
                  ))}
                </div>
              </div>
            )}

            <div>
              <p className="text-sm font-medium mb-2">Agent Reasoning</p>
              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm whitespace-pre-wrap">{result.reasoning}</p>
              </div>
            </div>

            {result.result && (
              <div>
                <p className="text-sm font-medium mb-2">Result Data</p>
                <div className="bg-muted p-3 rounded-md">
                  <pre className="text-sm overflow-auto">
                    {JSON.stringify(result.result, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {result.error && (
              <div>
                <p className="text-sm font-medium mb-2">Error</p>
                <Alert variant="destructive">
                  <AlertDescription>{result.error}</AlertDescription>
                </Alert>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
              <div>
                <p className="font-medium">LLM Interactions: {result.llmInteractions}</p>
              </div>
              <div>
                <p className="font-medium">Tokens Used: {result.tokensUsed}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
