/**
 * Advanced Communication Panel - AI-powered communication with sentiment analysis
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { EnhancedTabs, TabPanel } from '@/design-system';
import { 
  Loader2, 
  MessageSquare, 
  Brain, 
  Send,
  BarChart3,
  Heart,
  Clock,
  AlertTriangle,
  CheckCircle,
  Mail,
  Phone,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { 
  useCommunicationAgent, 
  useEmailTemplates, 
  useSentimentAnalysis,
  useCommunicationAnalytics 
} from '@/hooks/useCommunicationAgent';
import { SentimentAnalysis } from '@/services/agents/CommunicationAgent';

export function CommunicationPanel() {
  const [activeTab, setActiveTab] = useState('compose');
  const [formData, setFormData] = useState({
    recipient: '',
    subject: '',
    content: '',
    tone: 'professional',
    template: '',
    customInstructions: '',
  });
  const [sentimentText, setSentimentText] = useState('');
  const [sentimentResult, setSentimentResult] = useState<SentimentAnalysis | null>(null);
  
  const tabs = [
    { value: 'compose', label: 'Compose Email', icon: Mail },
    { value: 'sentiment', label: 'Sentiment Analysis', icon: Brain },
    { value: 'response', label: 'Response Suggestions', icon: MessageSquare },
    { value: 'analytics', label: 'Analytics', icon: BarChart3 },
  ];

  const { 
    generateEmail, 
    analyzeSentiment, 
    suggestResponse, 
    isProcessing, 
    lastResult, 
    error, 
    clearError 
  } = useCommunicationAgent({
    defaultTone: formData.tone as any,
    defaultPriority: 'medium',
  });

  const { getTemplate } = useEmailTemplates();
  const { 
    getSentimentColor, 
    getSentimentIcon, 
    getResponseUrgency, 
    formatEmotionAnalysis 
  } = useSentimentAnalysis();

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleGenerateEmail = async () => {
    clearError();

    try {
      const template = formData.template ? getTemplate(formData.template) : undefined;
      
      await generateEmail({
        subject: formData.subject || template?.subject,
        content: formData.content,
        template: template?.template,
        customInstructions: formData.customInstructions,
      });
    } catch (err) {
      console.error('Email generation failed:', err);
    }
  };

  const handleAnalyzeSentiment = async () => {
    if (!sentimentText.trim()) return;

    clearError();

    try {
      const result = await analyzeSentiment(sentimentText);
      setSentimentResult(result);
    } catch (err) {
      console.error('Sentiment analysis failed:', err);
    }
  };

  const handleSuggestResponse = async () => {
    if (!sentimentText.trim()) return;

    clearError();

    try {
      await suggestResponse({
        content: sentimentText,
        customInstructions: formData.customInstructions,
      });
    } catch (err) {
      console.error('Response suggestion failed:', err);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Advanced Communication Agent
          </CardTitle>
        </CardHeader>
        <CardContent>
          <EnhancedTabs
            tabs={tabs}
            value={activeTab}
            onValueChange={setActiveTab}
            variant="navigation"
            indicatorStyle="underline"
            size="md"
          >
            {/* Email Composition Tab */}
            <TabPanel value="compose" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="recipient">Recipient</Label>
                  <Input
                    id="recipient"
                    value={formData.recipient}
                    onChange={(e) => handleInputChange('recipient', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tone">Communication Tone</Label>
                  <Select value={formData.tone} onValueChange={(value) => handleInputChange('tone', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="friendly">Friendly</SelectItem>
                      <SelectItem value="formal">Formal</SelectItem>
                      <SelectItem value="casual">Casual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template">Email Template</Label>
                  <Select value={formData.template} onValueChange={(value) => handleInputChange('template', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select template" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="initial_outreach">Initial Outreach</SelectItem>
                      <SelectItem value="interview_invitation">Interview Invitation</SelectItem>
                      <SelectItem value="follow_up">Follow Up</SelectItem>
                      <SelectItem value="rejection">Rejection</SelectItem>
                      <SelectItem value="offer">Job Offer</SelectItem>
                      <SelectItem value="onboarding">Onboarding</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Subject Line</Label>
                <Input
                  id="subject"
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  placeholder="Email subject (leave blank for AI generation)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Email Content</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  placeholder="Describe what you want to communicate, or leave blank for AI generation based on template"
                  rows={6}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="instructions">Custom Instructions</Label>
                <Textarea
                  id="instructions"
                  value={formData.customInstructions}
                  onChange={(e) => handleInputChange('customInstructions', e.target.value)}
                  placeholder="Any specific instructions for the AI (e.g., mention specific benefits, include salary range, etc.)"
                  rows={2}
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button 
                onClick={handleGenerateEmail} 
                disabled={isProcessing}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating Email...
                  </>
                ) : (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Generate AI Email
                  </>
                )}
              </Button>

              {/* Generated Email Display */}
              {lastResult && lastResult.content && (
                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Generated Email
                      <Badge variant="outline">
                        {Math.round(lastResult.confidence * 100)}% confidence
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {lastResult.subject && (
                      <div>
                        <Label className="text-xs text-muted-foreground">Subject:</Label>
                        <p className="font-medium">{lastResult.subject}</p>
                      </div>
                    )}
                    <div>
                      <Label className="text-xs text-muted-foreground">Content:</Label>
                      <div className="bg-muted/50 p-3 rounded-lg mt-1">
                        <p className="whitespace-pre-wrap text-sm">{lastResult.content}</p>
                      </div>
                    </div>
                    {lastResult.suggestions && lastResult.suggestions.length > 0 && (
                      <div>
                        <Label className="text-xs text-muted-foreground">Follow-up Suggestions:</Label>
                        <ul className="text-sm space-y-1 mt-1">
                          {lastResult.suggestions.map((suggestion, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <TrendingUp className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                              {suggestion}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabPanel>

            {/* Sentiment Analysis Tab */}
            <TabPanel value="sentiment" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sentimentText">Message to Analyze</Label>
                <Textarea
                  id="sentimentText"
                  value={sentimentText}
                  onChange={(e) => setSentimentText(e.target.value)}
                  placeholder="Paste the candidate's message here for sentiment analysis..."
                  rows={6}
                />
              </div>

              <Button 
                onClick={handleAnalyzeSentiment} 
                disabled={!sentimentText.trim() || isProcessing}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Analyzing Sentiment...
                  </>
                ) : (
                  <>
                    <Heart className="h-4 w-4 mr-2" />
                    Analyze Sentiment
                  </>
                )}
              </Button>

              {/* Sentiment Results */}
              {sentimentResult && (
                <SentimentResultsDisplay sentiment={sentimentResult} />
              )}
            </TabPanel>

            {/* Response Suggestions Tab */}
            <TabPanel value="response" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="responseText">Original Message</Label>
                <Textarea
                  id="responseText"
                  value={sentimentText}
                  onChange={(e) => setSentimentText(e.target.value)}
                  placeholder="Paste the message you need to respond to..."
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="responseInstructions">Response Instructions</Label>
                <Textarea
                  id="responseInstructions"
                  value={formData.customInstructions}
                  onChange={(e) => handleInputChange('customInstructions', e.target.value)}
                  placeholder="Any specific instructions for the response (e.g., address their concerns about salary, schedule interview, etc.)"
                  rows={2}
                />
              </div>

              <Button 
                onClick={handleSuggestResponse} 
                disabled={!sentimentText.trim() || isProcessing}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating Suggestions...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Suggest Response
                  </>
                )}
              </Button>

              {/* Response Suggestions Display */}
              {lastResult && lastResult.suggestions && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Response Suggestions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {lastResult.content && (
                      <div>
                        <Label className="text-xs text-muted-foreground">Primary Response:</Label>
                        <div className="bg-muted/50 p-3 rounded-lg mt-1">
                          <p className="text-sm">{lastResult.content}</p>
                        </div>
                      </div>
                    )}
                    
                    {lastResult.suggestions.length > 1 && (
                      <div>
                        <Label className="text-xs text-muted-foreground">Alternative Approaches:</Label>
                        <div className="space-y-2 mt-1">
                          {lastResult.suggestions.slice(1).map((suggestion, index) => (
                            <div key={index} className="bg-muted/30 p-2 rounded text-sm">
                              {suggestion}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {lastResult.followUpActions && lastResult.followUpActions.length > 0 && (
                      <div>
                        <Label className="text-xs text-muted-foreground">Next Steps:</Label>
                        <ul className="text-sm space-y-1 mt-1">
                          {lastResult.followUpActions.map((action, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <Calendar className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                              {action}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabPanel>

            {/* Analytics Tab */}
            <TabPanel value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <BarChart3 className="h-8 w-8 mx-auto text-blue-500 mb-2" />
                    <div className="text-2xl font-bold">0</div>
                    <p className="text-sm text-muted-foreground">Emails Generated</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4 text-center">
                    <Heart className="h-8 w-8 mx-auto text-red-500 mb-2" />
                    <div className="text-2xl font-bold">0</div>
                    <p className="text-sm text-muted-foreground">Sentiment Analyses</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4 text-center">
                    <Clock className="h-8 w-8 mx-auto text-green-500 mb-2" />
                    <div className="text-2xl font-bold">0ms</div>
                    <p className="text-sm text-muted-foreground">Avg Response Time</p>
                  </CardContent>
                </Card>
              </div>

              <Alert>
                <BarChart3 className="h-4 w-4" />
                <AlertDescription>
                  Communication analytics will be populated as you use the AI communication features.
                </AlertDescription>
              </Alert>
            </TabPanel>
          </EnhancedTabs>
        </CardContent>
      </Card>
    </div>
  );
}

// Sentiment Results Display Component
function SentimentResultsDisplay({ sentiment }: { sentiment: SentimentAnalysis }) {
  const { getSentimentColor, getSentimentIcon, getResponseUrgency, formatEmotionAnalysis } = useSentimentAnalysis();
  const urgency = getResponseUrgency(sentiment);
  const emotions = formatEmotionAnalysis(sentiment.emotions);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <span className="text-2xl">{getSentimentIcon(sentiment.sentiment)}</span>
            Sentiment Analysis
          </span>
          <Badge variant="outline" className={getSentimentColor(sentiment.sentiment)}>
            {sentiment.sentiment.toUpperCase()}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label className="text-sm text-muted-foreground">Confidence Level</Label>
            <div className="flex items-center gap-2 mt-1">
              <Progress value={sentiment.confidence * 100} className="flex-1" />
              <span className="text-sm font-medium">{Math.round(sentiment.confidence * 100)}%</span>
            </div>
          </div>
          
          <div>
            <Label className="text-sm text-muted-foreground">Response Urgency</Label>
            <div className={`text-sm font-medium mt-1 ${urgency.color}`}>
              {urgency.message}
            </div>
          </div>
        </div>

        {/* Emotions */}
        {emotions.length > 0 && (
          <div>
            <Label className="text-sm text-muted-foreground mb-2 block">Detected Emotions</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {emotions.map(({ emotion, intensity, level }) => (
                <div key={emotion} className="bg-muted/50 p-2 rounded text-center">
                  <div className="text-sm font-medium">{emotion}</div>
                  <div className="text-xs text-muted-foreground">{level} ({Math.round(intensity * 100)}%)</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Key Phrases */}
        {sentiment.keyPhrases.length > 0 && (
          <div>
            <Label className="text-sm text-muted-foreground mb-2 block">Key Phrases</Label>
            <div className="flex flex-wrap gap-1">
              {sentiment.keyPhrases.map((phrase, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {phrase}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Concerns */}
        {sentiment.concerns.length > 0 && (
          <div>
            <Label className="text-sm text-muted-foreground mb-2 block">Concerns Identified</Label>
            <ul className="text-sm space-y-1">
              {sentiment.concerns.map((concern, index) => (
                <li key={index} className="flex items-start gap-2">
                  <AlertTriangle className="h-3 w-3 text-orange-500 mt-0.5 flex-shrink-0" />
                  {concern}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Opportunities */}
        {sentiment.opportunities.length > 0 && (
          <div>
            <Label className="text-sm text-muted-foreground mb-2 block">Opportunities</Label>
            <ul className="text-sm space-y-1">
              {sentiment.opportunities.map((opportunity, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                  {opportunity}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
