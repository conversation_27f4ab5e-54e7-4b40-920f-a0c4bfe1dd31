import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Search, PlayCircle, Zap, GitBranch, Send, Shuffle, Link2 } from "lucide-react";
import { workflowNodes } from "./workflow-nodes";
import { WorkflowNodeConfig } from "./WorkflowNodeConfig";

interface WorkflowLibraryProps {
  onNodeSelect: (node: any) => void;
}

export function WorkflowLibrary({ onNodeSelect }: WorkflowLibraryProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedLibraryNode, setSelectedLibraryNode] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("triggers");
  
  // Define tabs for the workflow library with descriptions
  const tabs = [
    { 
      value: "triggers", 
      label: "Triggers", 
      icon: PlayCircle,
      description: "Events that initiate AI workflows. These nodes detect changes in your recruitment pipeline like new applications, status updates, or scheduled times to automatically start intelligent processing and candidate evaluation."
    },
    { 
      value: "actions", 
      label: "Actions", 
      icon: Zap,
      description: "AI-powered operations that perform tasks within your workflow. These nodes handle candidate screening, resume parsing, skill extraction, interview scheduling, and automated communications to streamline your hiring process."
    },
    { 
      value: "conditions", 
      label: "Conditions", 
      icon: GitBranch,
      description: "Decision points that control workflow logic. These nodes evaluate candidate scores, check qualifications, compare requirements, and route applicants through different paths based on AI-analyzed criteria and business rules."
    },
    { 
      value: "outputs", 
      label: "Outputs", 
      icon: Send,
      description: "Final destinations for workflow results. These nodes send notifications to hiring managers, update candidate statuses, generate reports, trigger external systems, and deliver AI insights to stakeholders."
    },
    { 
      value: "transformations", 
      label: "Transforms", 
      icon: Shuffle,
      description: "Data processing and enrichment nodes. These transform candidate information, normalize data formats, aggregate scores, extract insights from resumes, and prepare structured data for AI analysis and decision-making."
    },
    { 
      value: "integrations", 
      label: "Integrations", 
      icon: Link2,
      description: "External system connectors for your workflow. These nodes integrate with ATS platforms, job boards, background check services, assessment tools, and communication channels to create a unified AI-driven recruitment ecosystem."
    },
  ];

  const filteredNodes = (category: string) => {
    // If there's a search query, search across all nodes regardless of category
    if (searchQuery) {
      return workflowNodes.filter(
        (node) =>
          node.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          node.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // If no search query, show only nodes from the selected category
    return workflowNodes.filter((node) => node.category === category);
  };

  const handleNodeClick = (node: any) => {
    onNodeSelect(node);
    setSelectedLibraryNode({
      id: "preview",
      data: {
        ...node,
        originalId: node.id,
      },
    });
  };

  return (
    <>
      <Card className="w-80 max-h-[calc(100vh-8rem)] overflow-y-auto">
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search nodes..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <EnhancedTabs
          tabs={tabs.map(tab => ({ 
            value: tab.value, 
            label: tab.label, 
            icon: tab.icon,
            description: tab.description
          }))}
          value={activeTab}
          onValueChange={setActiveTab}
          variant="navigation"
          indicatorStyle="underline"
          size="sm"
          className="h-[calc(100%-5rem)] flex flex-col"
          fullWidth={false}
          alignTabs="center"
          hideLabels
          showLabelTooltips
          tooltipSide="bottom"
          tabListClassName="flex-nowrap"
        >
          <ScrollArea className="h-[calc(100%-3rem)] rounded-none mt-4">
            {tabs.map(({ value: category }) => (
              <TabPanel key={category} value={category} className="m-0">
                <div className="p-4 grid gap-2">
                  {filteredNodes(category).map((node) => (
                    <div
                      key={node.id}
                      className="p-3 rounded-md border bg-card hover:bg-accent hover:border-primary cursor-pointer transition-all"
                      onClick={() => handleNodeClick(node)}
                      onDragStart={(e) => {
                        e.dataTransfer.setData(
                          "application/reactflow",
                          JSON.stringify({
                            ...node,
                            icon: null,
                          }),
                        );
                        e.dataTransfer.effectAllowed = "move";
                      }}
                      draggable
                    >
                      <div className="flex items-center gap-2 text-sm font-medium">
                        {node.icon}
                        {node.label}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {node.description}
                      </p>
                    </div>
                  ))}
                </div>
              </TabPanel>
            ))}
          </ScrollArea>
        </EnhancedTabs>
      </Card>
      {selectedLibraryNode && (
        <WorkflowNodeConfig
          node={selectedLibraryNode}
          onClose={() => setSelectedLibraryNode(null)}
          onUpdate={() => {}}
        />
      )}
    </>
  );
}
