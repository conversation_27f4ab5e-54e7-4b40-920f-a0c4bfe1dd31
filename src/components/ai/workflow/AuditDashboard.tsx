import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Shield, 
  FileText, 
  Download, 
  Search, 
  Filter,
  Calendar,
  User,
  Workflow,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Brain,
  Users,
  Eye,
  TrendingUp,
  BarChart3
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { AdvancedAuditTrail, AuditEvent, AuditQuery } from "@/services/audit/AdvancedAuditTrail";
import { RiskAssessmentEngine } from "@/services/risk/RiskAssessmentEngine";
import { formatDistanceToNow, format } from "date-fns";

export function AuditDashboard() {
  const [auditEvents, setAuditEvents] = useState<AuditEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState<AuditQuery>({
    limit: 50,
    offset: 0,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedEvent, setSelectedEvent] = useState<AuditEvent | null>(null);
  const [activeTab, setActiveTab] = useState("events");
  const { toast } = useToast();
  
  const tabs = [
    { value: "events", label: "Audit Events", icon: FileText },
    { value: "decisions", label: "Decision Log", icon: Brain },
    { value: "approvals", label: "Approval Chain", icon: CheckCircle },
    { value: "compliance", label: "Compliance", icon: Shield },
  ];

  const auditTrail = AdvancedAuditTrail.getInstance();

  useEffect(() => {
    loadAuditEvents();
  }, [query]);

  const loadAuditEvents = async () => {
    try {
      setLoading(true);
      const events = await auditTrail.queryEvents(query);
      setAuditEvents(events);
    } catch (error) {
      console.error('Error loading audit events:', error);
      toast({
        title: "Error",
        description: "Failed to load audit events",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setQuery(prev => ({
      ...prev,
      entityId: searchTerm || undefined,
      offset: 0,
    }));
  };

  const handleFilterChange = (field: keyof AuditQuery, value: any) => {
    setQuery(prev => ({
      ...prev,
      [field]: value === 'all' ? undefined : value || undefined,
      offset: 0,
    }));
  };

  const generateComplianceReport = async (reportType: 'gdpr' | 'audit' | 'decision_log' | 'approval_chain') => {
    try {
      const period = {
        from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        to: new Date(),
      };

      toast({
        title: "Generating Report",
        description: `Creating ${reportType} compliance report...`,
      });

      const report = await auditTrail.generateComplianceReport(reportType, period, 'current-user');
      
      // In a real implementation, this would trigger a download
      toast({
        title: "Report Generated",
        description: `${reportType} report generated successfully with ${report.summary.totalEvents} events.`,
      });
    } catch (error) {
      toast({
        title: "Report Generation Failed",
        description: "Failed to generate compliance report",
        variant: "destructive",
      });
    }
  };

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case 'decision': return <Brain className="h-4 w-4 text-blue-500" />;
      case 'approval': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'escalation': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'override': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'execution': return <Workflow className="h-4 w-4 text-purple-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'decision': return 'default';
      case 'approval': return 'default';
      case 'escalation': return 'secondary';
      case 'override': return 'destructive';
      case 'execution': return 'outline';
      case 'error': return 'destructive';
      default: return 'secondary';
    }
  };

  const getSensitivityColor = (level: string) => {
    switch (level) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const filteredEvents = auditEvents.filter(event => 
    !searchTerm || 
    event.entityId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.userId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.eventType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6 text-blue-600" />
            Advanced Audit Trail
          </h2>
          <p className="text-muted-foreground">
            Comprehensive audit logging with decision tracking and compliance reporting
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => generateComplianceReport('audit')}>
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
          <Button size="sm" onClick={loadAuditEvents}>
            <TrendingUp className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <EnhancedTabs
        tabs={tabs}
        value={activeTab}
        onValueChange={setActiveTab}
        variant="navigation"
        indicatorStyle="underline"
        size="md"
      >
        <TabPanel value="events" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters & Search
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">Search</Label>
                  <div className="flex gap-2">
                    <Input
                      id="search"
                      placeholder="Entity ID, User ID, Event Type..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button size="sm" onClick={handleSearch}>
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Event Type</Label>
                  <Select onValueChange={(value) => handleFilterChange('eventType', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All types</SelectItem>
                      <SelectItem value="decision">Decision</SelectItem>
                      <SelectItem value="approval">Approval</SelectItem>
                      <SelectItem value="escalation">Escalation</SelectItem>
                      <SelectItem value="override">Override</SelectItem>
                      <SelectItem value="execution">Execution</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Entity Type</Label>
                  <Select onValueChange={(value) => handleFilterChange('entityType', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="All entities" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All entities</SelectItem>
                      <SelectItem value="workflow">Workflow</SelectItem>
                      <SelectItem value="candidate">Candidate</SelectItem>
                      <SelectItem value="job">Job</SelectItem>
                      <SelectItem value="communication">Communication</SelectItem>
                      <SelectItem value="approval">Approval</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Sensitivity</Label>
                  <Select onValueChange={(value) => handleFilterChange('sensitivityLevel', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="All levels" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All levels</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Events List */}
          <div className="space-y-3">
            {loading ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p>Loading audit events...</p>
                </CardContent>
              </Card>
            ) : filteredEvents.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No audit events found</h3>
                  <p className="text-muted-foreground">
                    No events match your current filters. Try adjusting your search criteria.
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredEvents.map((event) => (
                <Card key={event.id} className="hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => setSelectedEvent(event)}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2 flex-1">
                        <div className="flex items-center gap-2">
                          {getEventTypeIcon(event.eventType)}
                          <span className="font-medium">{event.eventType.toUpperCase()}</span>
                          <Badge variant={getEventTypeColor(event.eventType)}>
                            {event.entityType}
                          </Badge>
                          <Badge variant={getSensitivityColor(event.compliance.sensitivityLevel)}>
                            {event.compliance.sensitivityLevel}
                          </Badge>
                          {event.compliance.gdprRelevant && (
                            <Badge variant="outline">GDPR</Badge>
                          )}
                        </div>
                        
                        <div className="text-sm text-muted-foreground">
                          <span className="font-medium">Entity:</span> {event.entityId} | 
                          <span className="font-medium"> User:</span> {event.userId} |
                          {event.workflowId && (
                            <><span className="font-medium"> Workflow:</span> {event.workflowId}</>
                          )}
                        </div>

                        {event.decisionPoint && (
                          <div className="text-sm">
                            <span className="font-medium">Decision:</span> {event.decisionPoint.condition} → 
                            <Badge variant={event.decisionPoint.result ? 'default' : 'secondary'} className="ml-1">
                              {event.decisionPoint.result ? 'TRUE' : 'FALSE'}
                            </Badge>
                            {event.decisionPoint.confidence && (
                              <span className="ml-2 text-muted-foreground">
                                ({event.decisionPoint.confidence}% confidence)
                              </span>
                            )}
                          </div>
                        )}

                        {event.approvalChain && (
                          <div className="text-sm">
                            <span className="font-medium">Approval:</span> {event.approvalChain.decision.toUpperCase()}
                            <span className="ml-2 text-muted-foreground">
                              Level {event.approvalChain.approverLevel}
                            </span>
                            {event.approvalChain.timeToDecision && (
                              <span className="ml-2 text-muted-foreground">
                                ({Math.round(event.approvalChain.timeToDecision / 1000)}s)
                              </span>
                            )}
                          </div>
                        )}

                        {event.humanOverride && (
                          <div className="text-sm">
                            <span className="font-medium">Override:</span> {event.humanOverride.overrideType}
                            <span className="ml-2 text-muted-foreground">
                              Reason: {event.humanOverride.reason}
                            </span>
                          </div>
                        )}
                      </div>
                      
                      <div className="text-right text-sm text-muted-foreground">
                        <div>{formatDistanceToNow(event.createdAt, { addSuffix: true })}</div>
                        <div>{format(event.createdAt, 'MMM dd, HH:mm')}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Pagination */}
          <div className="flex justify-center gap-2">
            <Button 
              variant="outline" 
              size="sm"
              disabled={!query.offset}
              onClick={() => setQuery(prev => ({ ...prev, offset: Math.max(0, (prev.offset || 0) - (prev.limit || 50)) }))}
            >
              Previous
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              disabled={filteredEvents.length < (query.limit || 50)}
              onClick={() => setQuery(prev => ({ ...prev, offset: (prev.offset || 0) + (prev.limit || 50) }))}
            >
              Next
            </Button>
          </div>
        </TabPanel>

        <TabPanel value="decisions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI Decision Log
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">AI Decision Analytics</h3>
                <p className="text-muted-foreground mb-4">
                  Detailed analysis of AI decision-making patterns, confidence levels, and accuracy metrics.
                </p>
                <Button onClick={() => generateComplianceReport('decision_log')}>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Generate Decision Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="approvals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Approval Chain Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Approval Chain Tracking</h3>
                <p className="text-muted-foreground mb-4">
                  Complete audit trail of approval processes, escalations, and decision timelines.
                </p>
                <Button onClick={() => generateComplianceReport('approval_chain')}>
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Approval Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Compliance & GDPR
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">GDPR Events</p>
                        <p className="text-2xl font-bold">
                          {auditEvents.filter(e => e.compliance.gdprRelevant).length}
                        </p>
                      </div>
                      <Shield className="h-8 w-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Critical Events</p>
                        <p className="text-2xl font-bold">
                          {auditEvents.filter(e => e.compliance.sensitivityLevel === 'critical').length}
                        </p>
                      </div>
                      <AlertTriangle className="h-8 w-8 text-red-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-2">
                <Button onClick={() => generateComplianceReport('gdpr')} className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Generate GDPR Compliance Report
                </Button>
                <Button variant="outline" onClick={() => generateComplianceReport('audit')} className="w-full">
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Full Audit Report
                </Button>
              </div>

              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  All audit events are retained according to compliance requirements. 
                  GDPR-relevant events are automatically flagged and include appropriate retention periods.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabPanel>
      </EnhancedTabs>

      {/* Event Detail Modal would go here */}
    </div>
  );
}
