import { WorkflowTemplate } from "../types";

export const AutonomousScreeningWorkflow: WorkflowTemplate = {
  id: "autonomous-screening-v2",
  name: "Autonomous Candidate Screening v2.0",
  description: "Production-ready autonomous workflow with AI agents, human oversight, and intelligent decision-making",
  category: "screening",
  version: "2.0.0",
  tags: ["ai", "autonomous", "screening", "production"],
  
  nodes: [
    // Trigger: New Application
    {
      id: "trigger-new-app",
      type: "new-application",
      position: { x: 100, y: 100 },
      data: {
        id: "new-application",
        type: "trigger",
        label: "New Application",
        category: "triggers",
        config: {
          jobTypes: "all",
          notifyTeam: true,
          autoScreen: true,
        },
      },
    },

    // AI Screening with Enhanced Features
    {
      id: "ai-screen-enhanced",
      type: "ai-screen",
      position: { x: 300, y: 100 },
      data: {
        id: "ai-screen",
        type: "action",
        label: "Enhanced AI Screening",
        category: "actions",
        config: {
          criteria: "comprehensive",
          minScore: 75,
          useAdvancedAI: true,
          includeInterviewQuestions: true,
          includeRiskAssessment: true,
          confidenceThreshold: 85,
          llmProvider: "auto",
          customInstructions: "Focus on technical skills, cultural fit, and growth potential. Consider remote work capabilities and communication skills.",
          generateSummary: true,
          compareToJobDescription: true,
        },
      },
    },

    // Decision Point: High Score Auto-Approve
    {
      id: "score-check-high",
      type: "data-comparison",
      position: { x: 500, y: 50 },
      data: {
        id: "data-comparison",
        type: "condition",
        label: "High Score Check",
        category: "conditions",
        config: {
          field: "screeningResult.score",
          operator: "greater_than_or_equal",
          value: 90,
          dataType: "number",
        },
      },
    },

    // Auto-Approve Path for High Scores
    {
      id: "auto-approve-high",
      type: "update-status",
      position: { x: 700, y: 50 },
      data: {
        id: "update-status",
        type: "output",
        label: "Auto-Approve (High Score)",
        category: "outputs",
        config: {
          status: "interview_scheduled",
          reason: "Automatically approved - exceptional candidate (score >= 90%)",
          notifyCandidate: true,
          notifyTeam: true,
        },
      },
    },

    // Schedule Interview for High Scores
    {
      id: "schedule-interview-high",
      type: "schedule-interview",
      position: { x: 900, y: 50 },
      data: {
        id: "schedule-interview",
        type: "action",
        label: "Auto-Schedule Interview",
        category: "actions",
        config: {
          interviewType: "technical",
          duration: 60,
          autoSchedule: true,
          interviewers: ["hiring_manager", "tech_lead"],
          priority: "high",
        },
      },
    },

    // Decision Point: Medium Score Human Review
    {
      id: "score-check-medium",
      type: "data-comparison",
      position: { x: 500, y: 150 },
      data: {
        id: "data-comparison",
        type: "condition",
        label: "Medium Score Check",
        category: "conditions",
        config: {
          field: "screeningResult.score",
          operator: "between",
          minValue: 70,
          maxValue: 89,
          dataType: "number",
        },
      },
    },

    // Human Approval for Medium Scores
    {
      id: "human-approval-medium",
      type: "human-approval",
      position: { x: 700, y: 150 },
      data: {
        id: "human-approval",
        type: "action",
        label: "Human Review Required",
        category: "actions",
        config: {
          approvalType: "single",
          approvers: ["<EMAIL>"],
          approvalMessage: "Candidate scored in medium range (70-89%). Please review AI analysis and decide on next steps.",
          priority: "medium",
          timeoutHours: 48,
          escalationHours: 24,
          escalationApprovers: ["<EMAIL>"],
          allowComments: true,
          autoApproveConditions: {
            enabled: true,
            conditions: [
              {
                field: "screeningResult.confidence",
                operator: "greater_than",
                value: 95,
              },
              {
                field: "screeningResult.riskAssessment.overallRisk",
                operator: "equals",
                value: "low",
              },
            ],
          },
        },
      },
    },

    // Conditional Action Based on Approval
    {
      id: "approval-decision",
      type: "data-comparison",
      position: { x: 900, y: 150 },
      data: {
        id: "data-comparison",
        type: "condition",
        label: "Approval Decision",
        category: "conditions",
        config: {
          field: "approvalResult.approved",
          operator: "equals",
          value: true,
          dataType: "boolean",
        },
      },
    },

    // Approved: Schedule Interview
    {
      id: "schedule-interview-approved",
      type: "schedule-interview",
      position: { x: 1100, y: 120 },
      data: {
        id: "schedule-interview",
        type: "action",
        label: "Schedule Interview (Approved)",
        category: "actions",
        config: {
          interviewType: "behavioral",
          duration: 45,
          autoSchedule: false,
          interviewers: ["hiring_manager"],
          priority: "medium",
        },
      },
    },

    // Rejected: Send Rejection Email
    {
      id: "send-rejection-approved",
      type: "send-email",
      position: { x: 1100, y: 180 },
      data: {
        id: "send-email",
        type: "action",
        label: "Send Rejection (Human Decision)",
        category: "actions",
        config: {
          template: "rejection_after_review",
          personalizeContent: true,
          includeAIFeedback: false,
          scheduleSend: false,
        },
      },
    },

    // Decision Point: Low Score Auto-Reject
    {
      id: "score-check-low",
      type: "data-comparison",
      position: { x: 500, y: 250 },
      data: {
        id: "data-comparison",
        type: "condition",
        label: "Low Score Check",
        category: "conditions",
        config: {
          field: "screeningResult.score",
          operator: "less_than",
          value: 70,
          dataType: "number",
        },
      },
    },

    // Auto-Reject Path for Low Scores
    {
      id: "auto-reject-low",
      type: "update-status",
      position: { x: 700, y: 250 },
      data: {
        id: "update-status",
        type: "output",
        label: "Auto-Reject (Low Score)",
        category: "outputs",
        config: {
          status: "rejected",
          reason: "Automatically rejected - insufficient match (score < 70%)",
          notifyCandidate: true,
          notifyTeam: false,
        },
      },
    },

    // Send Personalized Rejection Email
    {
      id: "send-rejection-auto",
      type: "send-email",
      position: { x: 900, y: 250 },
      data: {
        id: "send-email",
        type: "action",
        label: "Send Personalized Rejection",
        category: "actions",
        config: {
          template: "rejection_with_feedback",
          personalizeContent: true,
          includeAIFeedback: true,
          scheduleSend: false,
          aiPersonalization: {
            enabled: true,
            includeStrengths: true,
            includeSuggestions: true,
            tone: "encouraging",
          },
        },
      },
    },

    // Add to Talent Pool for Future Opportunities
    {
      id: "add-to-pool-future",
      type: "add-to-pool",
      position: { x: 1100, y: 250 },
      data: {
        id: "add-to-pool",
        type: "output",
        label: "Add to Future Talent Pool",
        category: "outputs",
        config: {
          poolName: "future_opportunities",
          tags: "ai_screened,potential,follow_up_6_months",
          tagColor: "blue",
          overwriteExisting: false,
          visibility: "team",
          autoExpire: true,
          expireDays: 180,
          notifyChanges: false,
        },
      },
    },

    // Analytics and Logging
    {
      id: "log-decision",
      type: "create-task",
      position: { x: 500, y: 350 },
      data: {
        id: "create-task",
        type: "action",
        label: "Log AI Decision",
        category: "actions",
        config: {
          title: "AI Screening Decision Logged",
          description: "Autonomous screening workflow completed. Review analytics for continuous improvement.",
          assignee: "system",
          priority: "low",
          category: "screening",
          autoComplete: true,
          completionCondition: "immediate",
        },
      },
    },
  ],

  edges: [
    // Main flow
    { id: "e1", source: "trigger-new-app", target: "ai-screen-enhanced", type: "default" },
    { id: "e2", source: "ai-screen-enhanced", target: "score-check-high", type: "default" },
    { id: "e3", source: "ai-screen-enhanced", target: "score-check-medium", type: "default" },
    { id: "e4", source: "ai-screen-enhanced", target: "score-check-low", type: "default" },

    // High score path
    { id: "e5", source: "score-check-high", target: "auto-approve-high", type: "success", label: "Score >= 90%" },
    { id: "e6", source: "auto-approve-high", target: "schedule-interview-high", type: "default" },

    // Medium score path
    { id: "e7", source: "score-check-medium", target: "human-approval-medium", type: "success", label: "Score 70-89%" },
    { id: "e8", source: "human-approval-medium", target: "approval-decision", type: "default" },
    { id: "e9", source: "approval-decision", target: "schedule-interview-approved", type: "success", label: "Approved" },
    { id: "e10", source: "approval-decision", target: "send-rejection-approved", type: "failure", label: "Rejected" },

    // Low score path
    { id: "e11", source: "score-check-low", target: "auto-reject-low", type: "success", label: "Score < 70%" },
    { id: "e12", source: "auto-reject-low", target: "send-rejection-auto", type: "default" },
    { id: "e13", source: "send-rejection-auto", target: "add-to-pool-future", type: "default" },

    // Analytics logging
    { id: "e14", source: "schedule-interview-high", target: "log-decision", type: "default" },
    { id: "e15", source: "schedule-interview-approved", target: "log-decision", type: "default" },
    { id: "e16", source: "send-rejection-approved", target: "log-decision", type: "default" },
    { id: "e17", source: "add-to-pool-future", target: "log-decision", type: "default" },
  ],

  metadata: {
    author: "RMS-Refresh AI System",
    createdAt: new Date().toISOString(),
    estimatedExecutionTime: "2-5 minutes",
    successRate: "95%",
    humanInterventionRate: "15%",
    features: [
      "Autonomous decision-making",
      "Human-in-the-loop controls",
      "Risk assessment",
      "Intelligent escalation",
      "Personalized communication",
      "Continuous learning",
      "Audit trail",
    ],
    requirements: {
      aiProviders: ["groq", "gemini"],
      permissions: ["candidate_read", "candidate_update", "email_send", "interview_schedule"],
      integrations: ["calendar", "email"],
    },
  },
};
