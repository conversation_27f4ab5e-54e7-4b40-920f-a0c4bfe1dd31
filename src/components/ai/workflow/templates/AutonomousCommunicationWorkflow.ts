import { WorkflowTemplate } from "../types";

export const AutonomousCommunicationWorkflow: WorkflowTemplate = {
  id: "autonomous-communication-v2",
  name: "Autonomous Communication Management v2.0",
  description: "AI-powered communication workflow with sentiment analysis, intelligent responses, and escalation management",
  category: "communication",
  version: "2.0.0",
  tags: ["ai", "communication", "autonomous", "sentiment"],
  
  nodes: [
    // Trigger: New Message Received
    {
      id: "trigger-message",
      type: "webhook-trigger",
      position: { x: 100, y: 100 },
      data: {
        id: "webhook-trigger",
        type: "trigger",
        label: "Message Received",
        category: "triggers",
        config: {
          webhookUrl: "/api/webhooks/message-received",
          authentication: "bearer_token",
          expectedPayload: {
            messageId: "string",
            senderId: "string",
            content: "string",
            channel: "email|slack|sms",
            priority: "low|medium|high|urgent",
          },
        },
      },
    },

    // AI Sentiment Analysis
    {
      id: "sentiment-analysis",
      type: "ai-screen", // Reusing AI screen for sentiment analysis
      position: { x: 300, y: 100 },
      data: {
        id: "ai-screen",
        type: "action",
        label: "Sentiment Analysis",
        category: "actions",
        config: {
          criteria: "comprehensive",
          useAdvancedAI: true,
          llmProvider: "auto",
          customInstructions: "Analyze the sentiment, urgency, and emotional tone of this message. Identify key concerns, requests, and required actions. Assess if human intervention is needed.",
          generateSummary: true,
          confidenceThreshold: 80,
        },
      },
    },

    // Decision: Urgent/Negative Sentiment
    {
      id: "urgent-check",
      type: "data-comparison",
      position: { x: 500, y: 50 },
      data: {
        id: "data-comparison",
        type: "condition",
        label: "Urgent/Negative Check",
        category: "conditions",
        config: {
          field: "sentimentAnalysis.urgency",
          operator: "in",
          value: ["high", "urgent"],
          dataType: "string",
        },
      },
    },

    // Immediate Human Escalation
    {
      id: "escalate-urgent",
      type: "human-approval",
      position: { x: 700, y: 50 },
      data: {
        id: "human-approval",
        type: "action",
        label: "Urgent Escalation",
        category: "actions",
        config: {
          approvalType: "single",
          approvers: ["<EMAIL>", "<EMAIL>"],
          approvalMessage: "URGENT: Message requires immediate attention due to negative sentiment or high urgency.",
          priority: "urgent",
          timeoutHours: 2,
          escalationHours: 1,
          escalationApprovers: ["<EMAIL>"],
          allowComments: true,
        },
      },
    },

    // Send Immediate Acknowledgment
    {
      id: "send-acknowledgment",
      type: "send-email",
      position: { x: 900, y: 50 },
      data: {
        id: "send-email",
        type: "action",
        label: "Send Acknowledgment",
        category: "actions",
        config: {
          template: "urgent_acknowledgment",
          personalizeContent: true,
          scheduleSend: false,
          aiPersonalization: {
            enabled: true,
            tone: "empathetic",
            includeTimeline: true,
          },
        },
      },
    },

    // Decision: Routine Inquiry
    {
      id: "routine-check",
      type: "data-comparison",
      position: { x: 500, y: 150 },
      data: {
        id: "data-comparison",
        type: "condition",
        label: "Routine Inquiry Check",
        category: "conditions",
        config: {
          field: "sentimentAnalysis.category",
          operator: "in",
          value: ["question", "request", "information"],
          dataType: "string",
        },
      },
    },

    // Generate AI Response
    {
      id: "generate-response",
      type: "send-email",
      position: { x: 700, y: 150 },
      data: {
        id: "send-email",
        type: "action",
        label: "Generate AI Response",
        category: "actions",
        config: {
          template: "ai_generated_response",
          personalizeContent: true,
          scheduleSend: false,
          aiPersonalization: {
            enabled: true,
            useContextualKnowledge: true,
            includeRelevantLinks: true,
            tone: "professional",
            maxLength: 500,
          },
          requireHumanReview: false,
        },
      },
    },

    // Quality Check for AI Response
    {
      id: "response-quality-check",
      type: "data-comparison",
      position: { x: 900, y: 150 },
      data: {
        id: "data-comparison",
        type: "condition",
        label: "Response Quality Check",
        category: "conditions",
        config: {
          field: "aiResponse.confidence",
          operator: "greater_than_or_equal",
          value: 85,
          dataType: "number",
        },
      },
    },

    // Send AI Response (High Confidence)
    {
      id: "send-ai-response",
      type: "send-message",
      position: { x: 1100, y: 120 },
      data: {
        id: "send-message",
        type: "output",
        label: "Send AI Response",
        category: "outputs",
        config: {
          template: "ai_response",
          messageContent: "{{aiResponse.content}}",
          includeDisclaimer: true,
          trackEngagement: true,
        },
      },
    },

    // Human Review Required (Low Confidence)
    {
      id: "human-review-response",
      type: "human-approval",
      position: { x: 1100, y: 180 },
      data: {
        id: "human-approval",
        type: "action",
        label: "Human Review Required",
        category: "actions",
        config: {
          approvalType: "single",
          approvers: ["<EMAIL>"],
          approvalMessage: "AI response has low confidence. Please review and approve or modify the response.",
          priority: "medium",
          timeoutHours: 24,
          escalationHours: 12,
          allowComments: true,
        },
      },
    },

    // Decision: Complex/Complaint
    {
      id: "complex-check",
      type: "data-comparison",
      position: { x: 500, y: 250 },
      data: {
        id: "data-comparison",
        type: "condition",
        label: "Complex/Complaint Check",
        category: "conditions",
        config: {
          field: "sentimentAnalysis.category",
          operator: "in",
          value: ["complaint", "complex", "legal", "escalation"],
          dataType: "string",
        },
      },
    },

    // Route to Specialist
    {
      id: "route-specialist",
      type: "create-task",
      position: { x: 700, y: 250 },
      data: {
        id: "create-task",
        type: "action",
        label: "Route to Specialist",
        category: "actions",
        config: {
          title: "Complex Communication Requires Specialist",
          description: "Message categorized as complex/complaint. Requires specialist attention.",
          assignee: "specialist_team",
          priority: "high",
          category: "communication",
          dueDate: "24_hours",
          autoComplete: false,
        },
      },
    },

    // Send Specialist Routing Notification
    {
      id: "notify-specialist-routing",
      type: "send-email",
      position: { x: 900, y: 250 },
      data: {
        id: "send-email",
        type: "action",
        label: "Notify Specialist Routing",
        category: "actions",
        config: {
          template: "specialist_routing_notification",
          personalizeContent: true,
          scheduleSend: false,
          aiPersonalization: {
            enabled: true,
            tone: "professional",
            includeTimeline: true,
            estimatedResponseTime: "24 hours",
          },
        },
      },
    },

    // Follow-up Scheduler
    {
      id: "schedule-followup",
      type: "schedule",
      position: { x: 500, y: 350 },
      data: {
        id: "schedule",
        type: "transformation",
        label: "Schedule Follow-up",
        category: "transformations",
        config: {
          scheduleType: "relative",
          delay: "3_days",
          action: "send_followup_email",
          conditions: {
            onlyIfNoResponse: true,
            skipIfResolved: true,
          },
        },
      },
    },

    // Analytics and Learning
    {
      id: "log-interaction",
      type: "create-task",
      position: { x: 700, y: 350 },
      data: {
        id: "create-task",
        type: "action",
        label: "Log Interaction Analytics",
        category: "actions",
        config: {
          title: "Communication Analytics Logged",
          description: "Autonomous communication workflow completed. Data logged for ML improvement.",
          assignee: "system",
          priority: "low",
          category: "analytics",
          autoComplete: true,
          completionCondition: "immediate",
        },
      },
    },
  ],

  edges: [
    // Main flow
    { id: "e1", source: "trigger-message", target: "sentiment-analysis", type: "default" },
    { id: "e2", source: "sentiment-analysis", target: "urgent-check", type: "default" },
    { id: "e3", source: "sentiment-analysis", target: "routine-check", type: "default" },
    { id: "e4", source: "sentiment-analysis", target: "complex-check", type: "default" },

    // Urgent path
    { id: "e5", source: "urgent-check", target: "escalate-urgent", type: "success", label: "Urgent/Negative" },
    { id: "e6", source: "escalate-urgent", target: "send-acknowledgment", type: "default" },

    // Routine path
    { id: "e7", source: "routine-check", target: "generate-response", type: "success", label: "Routine Inquiry" },
    { id: "e8", source: "generate-response", target: "response-quality-check", type: "default" },
    { id: "e9", source: "response-quality-check", target: "send-ai-response", type: "success", label: "High Confidence" },
    { id: "e10", source: "response-quality-check", target: "human-review-response", type: "failure", label: "Low Confidence" },

    // Complex path
    { id: "e11", source: "complex-check", target: "route-specialist", type: "success", label: "Complex/Complaint" },
    { id: "e12", source: "route-specialist", target: "notify-specialist-routing", type: "default" },

    // Follow-up and analytics
    { id: "e13", source: "send-acknowledgment", target: "schedule-followup", type: "default" },
    { id: "e14", source: "send-ai-response", target: "schedule-followup", type: "default" },
    { id: "e15", source: "human-review-response", target: "schedule-followup", type: "default" },
    { id: "e16", source: "notify-specialist-routing", target: "schedule-followup", type: "default" },
    { id: "e17", source: "schedule-followup", target: "log-interaction", type: "default" },
  ],

  metadata: {
    author: "RMS-Refresh AI System",
    createdAt: new Date().toISOString(),
    estimatedExecutionTime: "30 seconds - 2 minutes",
    successRate: "92%",
    humanInterventionRate: "25%",
    features: [
      "Real-time sentiment analysis",
      "Intelligent response generation",
      "Automatic escalation",
      "Quality assurance",
      "Follow-up scheduling",
      "Specialist routing",
      "Analytics tracking",
    ],
    requirements: {
      aiProviders: ["groq", "gemini"],
      permissions: ["message_read", "message_send", "task_create", "user_notify"],
      integrations: ["email", "slack", "calendar"],
    },
  },
};
