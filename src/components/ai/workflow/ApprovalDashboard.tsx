import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { EnhancedTabs, TabPanel } from "@/design-system";

import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  User, 
  Calendar,
  MessageSquare,
  Loader2,
  RefreshCw
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";

interface ApprovalRequest {
  id: string;
  workflow_id: string;
  execution_id: string;
  node_id: string;
  requester_id: string;
  approvers: string[];
  approval_type: string;
  status: string;
  approval_message: string;
  context_data: any;
  priority: string;
  timeout_at?: string;
  escalation_at?: string;
  responses: any[];
  created_at: string;
  updated_at: string;
  workflow_configurations?: {
    name: string;
  };
  profiles?: {
    first_name: string;
    last_name: string;
  };
}

export function ApprovalDashboard() {
  const [approvals, setApprovals] = useState<ApprovalRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [comments, setComments] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState("pending");
  const { toast } = useToast();
  
  const tabs = [
    { value: "pending", label: "Pending", icon: Clock },
    { value: "escalated", label: "Escalated", icon: AlertTriangle },
  ];

  useEffect(() => {
    loadApprovals();
    
    // Set up real-time subscription for approval updates
    const channel = supabase
      .channel('approval-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'workflow_approvals',
        },
        () => {
          loadApprovals();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const loadApprovals = async () => {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) return;

      const { data, error } = await supabase
        .from('workflow_approvals')
        .select(`
          *,
          workflow_configurations!inner(name),
          profiles!workflow_approvals_requester_id_fkey(first_name, last_name)
        `)
        .or(`approvers.cs.{${user.user.id}},approvers.cs.{${user.user.email}}`)
        .in('status', ['pending', 'escalated'])
        .order('created_at', { ascending: false });

      if (error) throw error;

      setApprovals(data || []);
    } catch (error) {
      console.error('Error loading approvals:', error);
      toast({
        title: "Error",
        description: "Failed to load approval requests",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApproval = async (approvalId: string, decision: 'approve' | 'reject') => {
    setProcessingId(approvalId);
    
    try {
      const comment = comments[approvalId] || '';
      
      const { data, error } = await supabase.rpc('handle_approval_response', {
        approval_id: approvalId,
        decision,
        comment: comment || null,
      });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error || 'Failed to process approval');
      }

      toast({
        title: "Success",
        description: `Approval ${decision === 'approve' ? 'granted' : 'denied'} successfully`,
      });

      // Clear comment and reload approvals
      setComments(prev => ({ ...prev, [approvalId]: '' }));
      await loadApprovals();
      
    } catch (error) {
      console.error('Error processing approval:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process approval",
        variant: "destructive",
      });
    } finally {
      setProcessingId(null);
    }
  };

  const getPriorityColor = (priority: string): "default" | "destructive" | "outline" | "secondary" => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'escalated': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const formatTimeRemaining = (timeoutAt?: string) => {
    if (!timeoutAt) return null;
    
    const timeout = new Date(timeoutAt);
    const now = new Date();
    
    if (timeout < now) {
      return <span className="text-red-500">Expired</span>;
    }
    
    return (
      <span className="text-muted-foreground">
        Expires {formatDistanceToNow(timeout, { addSuffix: true })}
      </span>
    );
  };

  const pendingApprovals = approvals.filter(a => a.status === 'pending');
  const escalatedApprovals = approvals.filter(a => a.status === 'escalated');

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading approvals...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Approval Dashboard</h2>
          <p className="text-muted-foreground">
            Manage workflow approval requests requiring your attention
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={loadApprovals}
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {approvals.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">All caught up!</h3>
            <p className="text-muted-foreground">
              You have no pending approval requests at this time.
            </p>
          </CardContent>
        </Card>
      ) : (
        <EnhancedTabs
          tabs={tabs.map(tab => ({ 
            ...tab, 
            label: tab.value === "pending" 
              ? `Pending (${pendingApprovals.length})`
              : `Escalated (${escalatedApprovals.length})`
          }))}
          value={activeTab}
          onValueChange={setActiveTab}
          variant="navigation"
          indicatorStyle="underline"
          size="md"
        >
          <TabPanel value="pending" className="space-y-4">
            {pendingApprovals.map((approval) => (
              <ApprovalCard
                key={approval.id}
                approval={approval}
                comment={comments[approval.id] || ''}
                onCommentChange={(comment) => 
                  setComments(prev => ({ ...prev, [approval.id]: comment }))
                }
                onApprove={() => handleApproval(approval.id, 'approve')}
                onReject={() => handleApproval(approval.id, 'reject')}
                processing={processingId === approval.id}
                getPriorityColor={getPriorityColor}
                getStatusIcon={getStatusIcon}
                formatTimeRemaining={formatTimeRemaining}
              />
            ))}
          </TabPanel>

          <TabPanel value="escalated" className="space-y-4">
            {escalatedApprovals.map((approval) => (
              <ApprovalCard
                key={approval.id}
                approval={approval}
                comment={comments[approval.id] || ''}
                onCommentChange={(comment) => 
                  setComments(prev => ({ ...prev, [approval.id]: comment }))
                }
                onApprove={() => handleApproval(approval.id, 'approve')}
                onReject={() => handleApproval(approval.id, 'reject')}
                processing={processingId === approval.id}
                getPriorityColor={getPriorityColor}
                getStatusIcon={getStatusIcon}
                formatTimeRemaining={formatTimeRemaining}
                isEscalated={true}
              />
            ))}
          </TabPanel>
        </EnhancedTabs>
      )}
    </div>
  );
}

interface ApprovalCardProps {
  approval: ApprovalRequest;
  comment: string;
  onCommentChange: (comment: string) => void;
  onApprove: () => void;
  onReject: () => void;
  processing: boolean;
  getPriorityColor: (priority: string) => "default" | "destructive" | "outline" | "secondary";
  getStatusIcon: (status: string) => React.ReactNode;
  formatTimeRemaining: (timeoutAt?: string) => React.ReactNode;
  isEscalated?: boolean;
}

function ApprovalCard({
  approval,
  comment,
  onCommentChange,
  onApprove,
  onReject,
  processing,
  getPriorityColor,
  getStatusIcon,
  formatTimeRemaining,
  isEscalated = false,
}: ApprovalCardProps) {
  return (
    <Card className={isEscalated ? "border-red-200 bg-red-50" : ""}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {getStatusIcon(approval.status)}
              <CardTitle className="text-lg">
                {approval.workflow_configurations?.name || 'Workflow Approval'}
              </CardTitle>
              <Badge variant={getPriorityColor(approval.priority)}>
                {approval.priority}
              </Badge>
              {isEscalated && (
                <Badge variant="destructive">Escalated</Badge>
              )}
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <User className="h-3 w-3" />
                {approval.profiles ? `${approval.profiles.first_name} ${approval.profiles.last_name}` : 'Unknown'}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {formatDistanceToNow(new Date(approval.created_at), { addSuffix: true })}
              </div>
            </div>
          </div>
          <div className="text-right text-sm">
            {formatTimeRemaining(approval.timeout_at)}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Approval Request</h4>
          <p className="text-sm text-muted-foreground">
            {approval.approval_message}
          </p>
        </div>

        {approval.context_data && Object.keys(approval.context_data).length > 0 && (
          <div>
            <h4 className="font-medium mb-2">Context Information</h4>
            <div className="bg-muted p-3 rounded-md text-sm">
              <pre className="whitespace-pre-wrap">
                {JSON.stringify(approval.context_data, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor={`comment-${approval.id}`}>
            <MessageSquare className="h-4 w-4 inline mr-1" />
            Comment (optional)
          </Label>
          <Textarea
            id={`comment-${approval.id}`}
            placeholder="Add a comment with your decision..."
            value={comment}
            onChange={(e) => onCommentChange(e.target.value)}
            disabled={processing}
          />
        </div>

        <div className="flex gap-2 pt-2">
          <Button
            onClick={onApprove}
            disabled={processing}
            className="flex-1"
          >
            {processing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Approve
          </Button>
          <Button
            variant="destructive"
            onClick={onReject}
            disabled={processing}
            className="flex-1"
          >
            {processing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <XCircle className="h-4 w-4 mr-2" />
            )}
            Reject
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
