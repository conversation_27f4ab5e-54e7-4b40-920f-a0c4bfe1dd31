import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Activity, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle, 
  CheckCircle,
  Clock,
  Zap,
  Users,
  Brain,
  Shield,
  Database,
  Wifi,
  Server,
  BarChart3,
  LineChart,
  Pie<PERSON>hart,
  RefreshCw,
  Settings,
  Download,
  Play,
  Pause,
  Square
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SystemMetrics {
  uptime: number; // seconds
  totalWorkflows: number;
  activeWorkflows: number;
  completedToday: number;
  failedToday: number;
  averageExecutionTime: number; // milliseconds
  throughput: number; // workflows per hour
  errorRate: number; // percentage
  cpuUsage: number; // percentage
  memoryUsage: number; // percentage
  diskUsage: number; // percentage
  networkLatency: number; // milliseconds
}

interface WorkflowStatus {
  id: string;
  name: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  progress: number; // 0-100
  currentNode: string;
  totalNodes: number;
  completedNodes: number;
  errorMessage?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  humanInterventions: number;
}

interface AlertItem {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  workflowId?: string;
  resolved: boolean;
}

export function ProductionMonitoringDashboard() {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    uptime: 2847392, // ~33 days
    totalWorkflows: 1247,
    activeWorkflows: 23,
    completedToday: 156,
    failedToday: 8,
    averageExecutionTime: 3200,
    throughput: 47,
    errorRate: 4.9,
    cpuUsage: 34,
    memoryUsage: 67,
    diskUsage: 23,
    networkLatency: 45,
  });
  const [activeTab, setActiveTab] = useState("overview");
  
  const tabs = [
    { value: "overview", label: "System Overview", icon: Activity },
    { value: "workflows", label: "Active Workflows", icon: Zap },
    { value: "performance", label: "Performance", icon: TrendingUp },
    { value: "alerts", label: "Alerts & Issues", icon: AlertTriangle },
  ];

  const [activeWorkflows, setActiveWorkflows] = useState<WorkflowStatus[]>([
    {
      id: "wf-001",
      name: "Autonomous Candidate Screening",
      status: "running",
      startTime: new Date(Date.now() - 1000 * 60 * 15),
      progress: 75,
      currentNode: "ai-screen-enhanced",
      totalNodes: 12,
      completedNodes: 9,
      riskLevel: "low",
      humanInterventions: 0,
    },
    {
      id: "wf-002", 
      name: "Communication Management",
      status: "running",
      startTime: new Date(Date.now() - 1000 * 60 * 8),
      progress: 45,
      currentNode: "sentiment-analysis",
      totalNodes: 8,
      completedNodes: 4,
      riskLevel: "medium",
      humanInterventions: 1,
    },
    {
      id: "wf-003",
      name: "Risk Assessment Pipeline",
      status: "failed",
      startTime: new Date(Date.now() - 1000 * 60 * 22),
      endTime: new Date(Date.now() - 1000 * 60 * 18),
      duration: 240000,
      progress: 60,
      currentNode: "risk-evaluation",
      totalNodes: 10,
      completedNodes: 6,
      errorMessage: "Risk threshold exceeded - manual review required",
      riskLevel: "high",
      humanInterventions: 2,
    },
  ]);

  const [alerts, setAlerts] = useState<AlertItem[]>([
    {
      id: "alert-001",
      type: "warning",
      title: "High Memory Usage",
      message: "System memory usage is at 67% - consider scaling resources",
      timestamp: new Date(Date.now() - 1000 * 60 * 5),
      resolved: false,
    },
    {
      id: "alert-002",
      type: "error",
      title: "Workflow Failure",
      message: "Risk Assessment Pipeline failed due to threshold exceeded",
      timestamp: new Date(Date.now() - 1000 * 60 * 18),
      workflowId: "wf-003",
      resolved: false,
    },
    {
      id: "alert-003",
      type: "info",
      title: "Scheduled Maintenance",
      message: "System maintenance scheduled for tonight at 2:00 AM",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
      resolved: true,
    },
  ]);

  const [isMonitoring, setIsMonitoring] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(() => {
        // Simulate real-time updates
        setMetrics(prev => ({
          ...prev,
          uptime: prev.uptime + 5,
          cpuUsage: Math.max(10, Math.min(90, prev.cpuUsage + (Math.random() - 0.5) * 10)),
          memoryUsage: Math.max(20, Math.min(95, prev.memoryUsage + (Math.random() - 0.5) * 5)),
          networkLatency: Math.max(10, Math.min(200, prev.networkLatency + (Math.random() - 0.5) * 20)),
        }));

        // Update workflow progress
        setActiveWorkflows(prev => prev.map(wf => 
          wf.status === 'running' ? {
            ...wf,
            progress: Math.min(100, wf.progress + Math.random() * 5),
            completedNodes: Math.min(wf.totalNodes, wf.completedNodes + (Math.random() > 0.7 ? 1 : 0)),
          } : wf
        ));
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [isMonitoring]);

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-4 w-4 text-green-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'paused': return <Pause className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'default';
      case 'completed': return 'default';
      case 'failed': return 'destructive';
      case 'paused': return 'secondary';
      default: return 'outline';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'default';
      default: return 'outline';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info': return <CheckCircle className="h-4 w-4 text-blue-500" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
    toast({
      title: isMonitoring ? "Monitoring Paused" : "Monitoring Resumed",
      description: isMonitoring ? "Real-time updates paused" : "Real-time updates resumed",
    });
  };

  const exportMetrics = () => {
    toast({
      title: "Exporting Metrics",
      description: "Generating comprehensive metrics report...",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Activity className="h-6 w-6 text-green-600" />
            Production Monitoring
          </h2>
          <p className="text-muted-foreground">
            Real-time monitoring of autonomous workflow operations and system health
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={exportMetrics}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
          <Button size="sm" onClick={toggleMonitoring}>
            {isMonitoring ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Resume
              </>
            )}
          </Button>
        </div>
      </div>

      <EnhancedTabs
        tabs={tabs}
        value={activeTab}
        onValueChange={setActiveTab}
        variant="navigation"
        indicatorStyle="underline"
        size="md"
      >
        <TabPanel value="overview" className="space-y-4">
          {/* System Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">System Uptime</p>
                    <p className="text-lg font-bold">{formatUptime(metrics.uptime)}</p>
                  </div>
                  <Server className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Active Workflows</p>
                    <p className="text-2xl font-bold text-blue-600">{metrics.activeWorkflows}</p>
                  </div>
                  <Activity className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Throughput</p>
                    <p className="text-2xl font-bold text-purple-600">{metrics.throughput}/hr</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Error Rate</p>
                    <p className="text-2xl font-bold text-red-600">{metrics.errorRate}%</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Resource Usage */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Resource Usage
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">CPU Usage</span>
                  <span className="text-sm text-muted-foreground">{metrics.cpuUsage}%</span>
                </div>
                <Progress value={metrics.cpuUsage} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Memory Usage</span>
                  <span className="text-sm text-muted-foreground">{metrics.memoryUsage}%</span>
                </div>
                <Progress value={metrics.memoryUsage} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Disk Usage</span>
                  <span className="text-sm text-muted-foreground">{metrics.diskUsage}%</span>
                </div>
                <Progress value={metrics.diskUsage} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Network Latency</span>
                  <span className="text-sm text-muted-foreground">{metrics.networkLatency}ms</span>
                </div>
                <Progress value={(metrics.networkLatency / 200) * 100} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Today's Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Completed Today</p>
                    <p className="text-2xl font-bold text-green-600">{metrics.completedToday}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Failed Today</p>
                    <p className="text-2xl font-bold text-red-600">{metrics.failedToday}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Avg Execution</p>
                    <p className="text-2xl font-bold text-blue-600">{formatDuration(metrics.averageExecutionTime)}</p>
                  </div>
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Alert>
            <Activity className="h-4 w-4" />
            <AlertDescription>
              System is operating normally. {isMonitoring ? "Real-time monitoring active" : "Monitoring paused"} | 
              Last updated: {new Date().toLocaleTimeString()}
            </AlertDescription>
          </Alert>
        </TabPanel>

        <TabPanel value="workflows" className="space-y-4">
          <div className="space-y-3">
            {activeWorkflows.map((workflow) => (
              <Card key={workflow.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-4">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(workflow.status)}
                        <span className="font-medium">{workflow.name}</span>
                        <Badge variant={getStatusColor(workflow.status)}>
                          {workflow.status.toUpperCase()}
                        </Badge>
                        <Badge variant={getRiskColor(workflow.riskLevel)}>
                          {workflow.riskLevel} risk
                        </Badge>
                        {workflow.humanInterventions > 0 && (
                          <Badge variant="outline">
                            {workflow.humanInterventions} intervention{workflow.humanInterventions > 1 ? 's' : ''}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        <span className="font-medium">ID:</span> {workflow.id} | 
                        <span className="font-medium"> Current:</span> {workflow.currentNode} |
                        <span className="font-medium"> Progress:</span> {workflow.completedNodes}/{workflow.totalNodes} nodes
                      </div>

                      {workflow.errorMessage && (
                        <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                          <AlertTriangle className="h-4 w-4 inline mr-1" />
                          {workflow.errorMessage}
                        </div>
                      )}
                    </div>
                    
                    <div className="text-right text-sm text-muted-foreground">
                      <div>Started: {workflow.startTime.toLocaleTimeString()}</div>
                      {workflow.endTime && (
                        <div>Ended: {workflow.endTime.toLocaleTimeString()}</div>
                      )}
                      {workflow.duration && (
                        <div>Duration: {formatDuration(workflow.duration)}</div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{workflow.progress.toFixed(1)}%</span>
                    </div>
                    <Progress value={workflow.progress} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabPanel>

        <TabPanel value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Performance Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Performance Metrics</h3>
                <p className="text-muted-foreground mb-4">
                  Detailed performance analytics, trends, and optimization insights.
                </p>
                <Button>
                  <LineChart className="h-4 w-4 mr-2" />
                  View Performance Charts
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="alerts" className="space-y-4">
          <div className="space-y-3">
            {alerts.map((alert) => (
              <Card key={alert.id} className={`border-l-4 ${
                alert.type === 'error' ? 'border-l-red-500' :
                alert.type === 'warning' ? 'border-l-yellow-500' : 'border-l-blue-500'
              } ${alert.resolved ? 'opacity-60' : ''}`}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        {getAlertIcon(alert.type)}
                        <span className="font-medium">{alert.title}</span>
                        <Badge variant={alert.resolved ? 'outline' : 'default'}>
                          {alert.resolved ? 'RESOLVED' : alert.type.toUpperCase()}
                        </Badge>
                        {alert.workflowId && (
                          <Badge variant="outline">
                            {alert.workflowId}
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm text-muted-foreground">
                        {alert.message}
                      </p>
                    </div>
                    
                    <div className="text-right text-sm text-muted-foreground">
                      <div>{alert.timestamp.toLocaleString()}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabPanel>
      </EnhancedTabs>
    </div>
  );
}
