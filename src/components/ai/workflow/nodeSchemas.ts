// JSON Schema definitions for all workflow node types

export const nodeSchemas: Record<string, any> = {
  // Trigger Nodes
  "new-application": {
    type: "object",
    title: "New Application Trigger",
    description: "Triggers when a new application is received",
    properties: {
      jobTypes: {
        type: "string",
        title: "Job Types",
        description: "Filter by job types",
        enum: ["all", "full-time", "part-time", "contract", "internship"],
        default: "all",
      },
      notifyTeam: {
        type: "boolean",
        title: "Notify Team",
        description: "Send notification to team members",
        default: true,
      },
      autoScreen: {
        type: "boolean",
        title: "Auto Screen",
        description: "Automatically screen applications",
        default: false,
      },
    },
    required: ["jobTypes"],
  },

  "application-status": {
    type: "object",
    title: "Application Status Change",
    description: "Triggers when application status changes",
    properties: {
      statusType: {
        type: "string",
        title: "Status Type",
        description: "Which status changes to monitor",
        enum: [
          "any",
          "submitted",
          "screening",
          "interview",
          "offer",
          "rejected",
          "withdrawn",
        ],
        default: "any",
      },
      includeInternal: {
        type: "boolean",
        title: "Include Internal Changes",
        description: "Include status changes made by the system",
        default: false,
      },
    },
    required: ["statusType"],
  },

  "scheduled-trigger": {
    type: "object",
    title: "Scheduled Trigger",
    description: "Triggers on a schedule",
    properties: {
      scheduleType: {
        type: "string",
        title: "Schedule Type",
        enum: ["hourly", "daily", "weekly", "monthly"],
        default: "daily",
      },
      time: {
        type: "string",
        title: "Time",
        description: "Time in HH:MM format",
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$",
        default: "09:00",
      },
      timezone: {
        type: "string",
        title: "Timezone",
        default: "UTC",
      },
    },
    required: ["scheduleType", "time"],
  },

  // Action Nodes
  "send-email": {
    type: "object",
    title: "Send Email",
    description: "Send email to candidates or team members",
    properties: {
      template: {
        type: "string",
        title: "Email Template",
        enum: [
          "welcome",
          "interview",
          "offer",
          "rejection",
          "follow_up",
          "assessment",
          "document_request",
          "custom",
        ],
        default: "welcome",
      },
      customMessage: {
        type: "string",
        title: "Custom Message",
        description: 'Custom email content (used when template is "custom")',
        format: "textarea",
        maxLength: 5000,
      },
      ccRecruiter: {
        type: "boolean",
        title: "CC Recruiter",
        description: "Send copy to assigned recruiter",
        default: false,
      },
      trackOpens: {
        type: "boolean",
        title: "Track Opens",
        description: "Track email opens and clicks",
        default: true,
      },
      attachments: {
        type: "array",
        title: "Attachments",
        description: "List of attachment types to include",
        items: {
          type: "string",
          enum: ["job_description", "company_brochure", "benefits_guide"],
        },
      },
    },
    required: ["template"],
  },

  "ai-screen": {
    type: "object",
    title: "AI Screening",
    description: "Screen candidates using AI with advanced analysis capabilities",
    properties: {
      criteria: {
        type: "string",
        title: "Screening Criteria",
        enum: [
          "comprehensive",
          "technical",
          "cultural_fit",
          "document_verification",
        ],
        default: "comprehensive",
      },
      minScore: {
        type: "number",
        title: "Minimum Score (%)",
        description: "Minimum passing score",
        minimum: 0,
        maximum: 100,
        default: 75,
      },
      useAdvancedAI: {
        type: "boolean",
        title: "Use Advanced AI Analysis",
        description: "Enable advanced AI analysis with detailed scoring, confidence levels, and interview questions",
        default: true,
      },
      includeInterviewQuestions: {
        type: "boolean",
        title: "Generate Interview Questions",
        description: "Generate targeted interview questions based on candidate analysis",
        default: true,
      },
      includeRiskAssessment: {
        type: "boolean",
        title: "Include Risk Assessment",
        description: "Perform risk assessment and identify potential concerns",
        default: true,
      },
      confidenceThreshold: {
        type: "number",
        title: "Confidence Threshold (%)",
        description: "Minimum confidence level required for AI recommendations",
        minimum: 0,
        maximum: 100,
        default: 80,
      },
      llmProvider: {
        type: "string",
        title: "AI Provider",
        description: "AI provider to use for analysis (auto = intelligent selection)",
        enum: ["groq", "gemini", "auto"],
        default: "auto",
      },
      customInstructions: {
        type: "string",
        title: "Custom Instructions",
        description: "Additional screening criteria and instructions",
        format: "textarea",
        maxLength: 1000,
      },
      generateSummary: {
        type: "boolean",
        title: "Generate Summary",
        description: "Generate AI summary of screening results",
        default: true,
      },
      compareToJobDescription: {
        type: "boolean",
        title: "Compare to Job Description",
        description: "Match candidate against specific job requirements",
        default: true,
      },
    },
    required: ["criteria", "minScore"],
  },

  "schedule-interview": {
    type: "object",
    title: "Schedule Interview",
    description: "Schedule interviews with candidates",
    properties: {
      interviewType: {
        type: "string",
        title: "Interview Type",
        enum: [
          "technical",
          "behavioral",
          "cultural",
          "final",
          "screening",
          "panel",
        ],
        default: "technical",
      },
      duration: {
        type: "number",
        title: "Duration (minutes)",
        minimum: 15,
        maximum: 480,
        default: 60,
      },
      interviewers: {
        type: "string",
        title: "Interviewers",
        description: "Comma-separated email addresses",
      },
      location: {
        type: "string",
        title: "Location/Platform",
        enum: ["zoom", "teams", "google_meet", "phone", "onsite"],
        default: "zoom",
      },
      sendCalendarInvite: {
        type: "boolean",
        title: "Send Calendar Invite",
        default: true,
      },
      sendPrepEmail: {
        type: "boolean",
        title: "Send Preparation Email",
        default: true,
      },
      leadTime: {
        type: "number",
        title: "Lead Time (days)",
        description: "Days in advance to schedule",
        minimum: 1,
        maximum: 30,
        default: 3,
      },
    },
    required: ["interviewType", "duration"],
  },

  // Condition Nodes
  "skills-match": {
    type: "object",
    title: "Skills Match Check",
    description: "Check if candidate has required skills",
    properties: {
      requiredSkills: {
        type: "string",
        title: "Required Skills",
        description: "Comma-separated list of required skills",
      },
      minMatchPercentage: {
        type: "number",
        title: "Minimum Match %",
        minimum: 0,
        maximum: 100,
        default: 70,
      },
      considerSimilarSkills: {
        type: "boolean",
        title: "Consider Similar Skills",
        description: "Match related/similar skills",
        default: true,
      },
      skillCategories: {
        type: "array",
        title: "Skill Categories",
        items: {
          type: "string",
          enum: [
            "technical",
            "soft_skills",
            "languages",
            "tools",
            "certifications",
          ],
        },
      },
    },
    required: ["requiredSkills", "minMatchPercentage"],
  },

  "experience-check": {
    type: "object",
    title: "Experience Check",
    description: "Check candidate experience requirements",
    properties: {
      minYears: {
        type: "number",
        title: "Minimum Years",
        minimum: 0,
        maximum: 50,
        default: 3,
      },
      industry: {
        type: "string",
        title: "Industry",
        description: "Required industry experience",
        default: "any",
      },
      considerInternships: {
        type: "boolean",
        title: "Include Internships",
        default: true,
      },
      specificRoles: {
        type: "string",
        title: "Specific Roles",
        description: "Comma-separated list of role titles",
      },
    },
    required: ["minYears"],
  },

  // Output Nodes
  "update-status": {
    type: "object",
    title: "Update Candidate Status",
    description: "Update the status of a candidate",
    properties: {
      newStatus: {
        type: "string",
        title: "New Status",
        enum: [
          "new",
          "screening",
          "shortlisted",
          "interview",
          "assessment",
          "reference_check",
          "offer",
          "hired",
          "rejected",
          "withdrawn",
        ],
        default: "screening",
      },
      statusNote: {
        type: "string",
        title: "Status Note",
        description: "Note about the status change",
        format: "textarea",
        maxLength: 500,
      },
      notifyCandidate: {
        type: "boolean",
        title: "Notify Candidate",
        default: true,
      },
      notifyTeam: {
        type: "boolean",
        title: "Notify Team",
        default: false,
      },
    },
    required: ["newStatus"],
  },

  "add-to-pool": {
    type: "object",
    title: "Add to Talent Pool",
    description: "Add candidate to a talent pool",
    properties: {
      poolName: {
        type: "string",
        title: "Pool Name",
        enum: [
          "engineering",
          "sales",
          "marketing",
          "design",
          "operations",
          "executive",
          "general",
        ],
        default: "general",
      },
      tags: {
        type: "string",
        title: "Tags",
        description: "Comma-separated tags",
      },
      followUpDate: {
        type: "string",
        title: "Follow-up Date",
        format: "date",
      },
      notes: {
        type: "string",
        title: "Notes",
        format: "textarea",
        maxLength: 1000,
      },
      priority: {
        type: "string",
        title: "Priority",
        enum: ["low", "medium", "high"],
        default: "medium",
      },
    },
    required: ["poolName"],
  },

  // Transformation Nodes
  delay: {
    type: "object",
    title: "Delay",
    description: "Add a delay to the workflow",
    properties: {
      delayType: {
        type: "string",
        title: "Delay Type",
        enum: ["minutes", "hours", "days"],
        default: "hours",
      },
      delayValue: {
        type: "number",
        title: "Delay Value",
        minimum: 1,
        maximum: 365,
        default: 1,
      },
      businessDaysOnly: {
        type: "boolean",
        title: "Business Days Only",
        description: "Skip weekends and holidays",
        default: false,
      },
    },
    required: ["delayType", "delayValue"],
  },

  "data-transform": {
    type: "object",
    title: "Data Transformation",
    description: "Transform workflow data",
    properties: {
      transformationType: {
        type: "string",
        title: "Transformation Type",
        enum: ["map", "filter", "reduce", "custom"],
        default: "map",
      },
      mappingRules: {
        type: "object",
        title: "Mapping Rules",
        properties: {
          sourceField: {
            type: "string",
            title: "Source Field",
          },
          targetField: {
            type: "string",
            title: "Target Field",
          },
          transformation: {
            type: "string",
            title: "Transformation",
            enum: ["uppercase", "lowercase", "trim", "parse_json", "stringify"],
          },
        },
      },
      customScript: {
        type: "string",
        title: "Custom Script",
        description: "JavaScript transformation code",
        format: "textarea",
      },
    },
    required: ["transformationType"],
  },

  // Missing Trigger Nodes
  "document-upload": {
    type: "object",
    title: "Document Upload Trigger",
    description: "Triggers when a document is uploaded",
    properties: {
      documentType: {
        type: "string",
        title: "Document Type",
        description: "Filter by document type",
        enum: [
          "all",
          "resume",
          "cover_letter",
          "portfolio",
          "certificate",
          "reference",
          "other",
        ],
        default: "all",
      },
      fileFormat: {
        type: "array",
        title: "Allowed File Formats",
        description: "Accepted file formats",
        items: {
          type: "string",
          enum: ["pdf", "doc", "docx", "txt", "rtf", "jpg", "png", "gif"],
        },
        default: ["pdf", "doc", "docx"],
      },
      maxFileSize: {
        type: "number",
        title: "Max File Size (MB)",
        description: "Maximum file size in megabytes",
        minimum: 1,
        maximum: 100,
        default: 10,
      },
      autoProcess: {
        type: "boolean",
        title: "Auto Process",
        description: "Automatically process uploaded documents",
        default: true,
      },
      notifyUpload: {
        type: "boolean",
        title: "Notify on Upload",
        description: "Send notification when document is uploaded",
        default: true,
      },
      virusScan: {
        type: "boolean",
        title: "Virus Scan",
        description: "Scan uploaded files for viruses",
        default: true,
      },
    },
    required: ["documentType"],
  },

  "webhook-trigger": {
    type: "object",
    title: "Webhook Trigger",
    description: "Triggers from external webhook calls",
    properties: {
      webhookUrl: {
        type: "string",
        title: "Webhook URL",
        description: "Generated webhook URL (read-only)",
        format: "uri",
        readOnly: true,
      },
      event: {
        type: "string",
        title: "Event Type",
        description: "Type of events to listen for",
        enum: [
          "all",
          "application_received",
          "status_changed",
          "interview_scheduled",
          "assessment_completed",
          "custom",
        ],
        default: "all",
      },
      secretKey: {
        type: "string",
        title: "Secret Key",
        description: "Secret key for webhook validation",
        format: "password",
      },
      validateSignature: {
        type: "boolean",
        title: "Validate Signature",
        description: "Validate webhook signature for security",
        default: true,
      },
      allowedIPs: {
        type: "string",
        title: "Allowed IP Addresses",
        description: "Comma-separated list of allowed IP addresses (optional)",
        pattern: "^((\\d{1,3}\\.){3}\\d{1,3}(,\\s*)?)*$",
      },
      retryAttempts: {
        type: "number",
        title: "Retry Attempts",
        description: "Number of retry attempts for failed webhooks",
        minimum: 0,
        maximum: 5,
        default: 3,
      },
      timeout: {
        type: "number",
        title: "Timeout (seconds)",
        description: "Webhook timeout in seconds",
        minimum: 5,
        maximum: 300,
        default: 30,
      },
    },
    required: ["event"],
  },

  "database-trigger": {
    type: "object",
    title: "Database Change Trigger",
    description: "Triggers on database record changes",
    properties: {
      table: {
        type: "string",
        title: "Database Table",
        description: "Table to monitor for changes",
        enum: [
          "candidates",
          "applications",
          "jobs",
          "interviews",
          "assessments",
          "notes",
          "tasks",
        ],
        default: "candidates",
      },
      operation: {
        type: "string",
        title: "Operation Type",
        description: "Type of database operation to monitor",
        enum: ["all", "insert", "update", "delete"],
        default: "all",
      },
      conditions: {
        type: "string",
        title: "Filter Conditions",
        description: "SQL-like conditions to filter changes (optional)",
        format: "textarea",
        placeholder: 'e.g., status = "new" AND department = "engineering"',
      },
      includeOldData: {
        type: "boolean",
        title: "Include Old Data",
        description: "Include previous values in trigger data",
        default: false,
      },
      batchChanges: {
        type: "boolean",
        title: "Batch Changes",
        description: "Group multiple changes into single trigger",
        default: false,
      },
      batchTimeout: {
        type: "number",
        title: "Batch Timeout (seconds)",
        description: "Time to wait before processing batched changes",
        minimum: 1,
        maximum: 300,
        default: 30,
      },
    },
    required: ["table", "operation"],
  },

  // Missing Action Nodes
  "send-assessment": {
    type: "object",
    title: "Send Assessment",
    description: "Send skills assessment test to candidates",
    properties: {
      assessmentType: {
        type: "string",
        title: "Assessment Type",
        description: "Type of assessment to send",
        enum: ["technical", "behavioral", "cognitive", "personality", "custom"],
        default: "technical",
      },
      assessmentTemplate: {
        type: "string",
        title: "Assessment Template",
        description: "Pre-configured assessment template",
        enum: [
          "javascript_developer",
          "python_developer",
          "data_analyst",
          "project_manager",
          "sales_rep",
          "custom",
        ],
      },
      timeLimit: {
        type: "number",
        title: "Time Limit (minutes)",
        description: "Time limit for completing the assessment",
        minimum: 15,
        maximum: 480,
        default: 60,
      },
      passingScore: {
        type: "number",
        title: "Passing Score (%)",
        description: "Minimum score required to pass",
        minimum: 0,
        maximum: 100,
        default: 70,
      },
      autoGrade: {
        type: "boolean",
        title: "Auto Grade",
        description: "Automatically grade the assessment",
        default: true,
      },
      sendReminders: {
        type: "boolean",
        title: "Send Reminders",
        description: "Send reminder emails if not completed",
        default: true,
      },
      reminderInterval: {
        type: "number",
        title: "Reminder Interval (hours)",
        description: "Hours between reminder emails",
        minimum: 1,
        maximum: 168,
        default: 24,
      },
      maxAttempts: {
        type: "number",
        title: "Max Attempts",
        description: "Maximum number of attempts allowed",
        minimum: 1,
        maximum: 5,
        default: 1,
      },
      includeInstructions: {
        type: "boolean",
        title: "Include Instructions",
        description: "Include detailed instructions in the email",
        default: true,
      },
      customInstructions: {
        type: "string",
        title: "Custom Instructions",
        description: "Additional instructions for the candidate",
        format: "textarea",
        maxLength: 1000,
      },
    },
    required: ["assessmentType", "timeLimit"],
  },

  "notify-team": {
    type: "object",
    title: "Notify Team",
    description: "Send notification to team members",
    properties: {
      notificationChannel: {
        type: "string",
        title: "Notification Channel",
        description: "How to send the notification",
        enum: ["email", "slack", "teams", "webhook", "in_app"],
        default: "email",
      },
      recipients: {
        type: "string",
        title: "Recipients",
        description: "Who should receive the notification",
        enum: [
          "all_team",
          "recruiters",
          "hiring_managers",
          "interviewers",
          "custom",
        ],
        default: "recruiters",
      },
      customRecipients: {
        type: "string",
        title: "Custom Recipients",
        description:
          'Comma-separated email addresses (when recipients is "custom")',
        pattern:
          "^[\\w\\.-]+@[\\w\\.-]+\\.[a-zA-Z]{2,}(,\\s*[\\w\\.-]+@[\\w\\.-]+\\.[a-zA-Z]{2,})*$",
      },
      messageTemplate: {
        type: "string",
        title: "Message Template",
        description: "Pre-defined message template",
        enum: [
          "new_application",
          "status_change",
          "interview_scheduled",
          "assessment_completed",
          "urgent_review",
          "custom",
        ],
        default: "new_application",
      },
      customMessage: {
        type: "string",
        title: "Custom Message",
        description: 'Custom notification message (when template is "custom")',
        format: "textarea",
        maxLength: 2000,
      },
      priority: {
        type: "string",
        title: "Priority Level",
        description: "Notification priority",
        enum: ["low", "normal", "high", "urgent"],
        default: "normal",
      },
      includeCandidate: {
        type: "boolean",
        title: "Include Candidate Info",
        description: "Include candidate details in notification",
        default: true,
      },
      includeJob: {
        type: "boolean",
        title: "Include Job Info",
        description: "Include job details in notification",
        default: true,
      },
      actionRequired: {
        type: "boolean",
        title: "Action Required",
        description: "Mark as requiring action from recipient",
        default: false,
      },
      actionDeadline: {
        type: "string",
        title: "Action Deadline",
        description: "Deadline for required action",
        format: "datetime-local",
      },
    },
    required: ["notificationChannel", "recipients", "messageTemplate"],
  },

  "data-enrichment": {
    type: "object",
    title: "Data Enrichment",
    description: "Enrich candidate data from external sources",
    properties: {
      enrichmentSource: {
        type: "string",
        title: "Enrichment Source",
        description: "Source for data enrichment",
        enum: [
          "linkedin",
          "github",
          "clearbit",
          "hunter",
          "pipl",
          "custom_api",
        ],
        default: "linkedin",
      },
      dataTypes: {
        type: "array",
        title: "Data Types to Enrich",
        description: "Types of data to enrich",
        items: {
          type: "string",
          enum: [
            "contact_info",
            "work_history",
            "education",
            "skills",
            "social_profiles",
            "company_info",
            "salary_data",
          ],
        },
        default: ["contact_info", "work_history", "skills"],
      },
      apiKey: {
        type: "string",
        title: "API Key",
        description: "API key for the enrichment service",
        format: "password",
      },
      customEndpoint: {
        type: "string",
        title: "Custom API Endpoint",
        description: "Custom API endpoint URL (for custom_api source)",
        format: "uri",
      },
      autoUpdate: {
        type: "boolean",
        title: "Auto Update",
        description: "Automatically update existing data",
        default: true,
      },
      overwriteExisting: {
        type: "boolean",
        title: "Overwrite Existing",
        description: "Overwrite existing data if found",
        default: false,
      },
      confidenceThreshold: {
        type: "number",
        title: "Confidence Threshold (%)",
        description: "Minimum confidence level for accepting enriched data",
        minimum: 0,
        maximum: 100,
        default: 80,
      },
      rateLimitDelay: {
        type: "number",
        title: "Rate Limit Delay (ms)",
        description: "Delay between API calls to respect rate limits",
        minimum: 0,
        maximum: 10000,
        default: 1000,
      },
      retryFailures: {
        type: "boolean",
        title: "Retry Failures",
        description: "Retry failed enrichment attempts",
        default: true,
      },
      maxRetries: {
        type: "number",
        title: "Max Retries",
        description: "Maximum number of retry attempts",
        minimum: 0,
        maximum: 5,
        default: 3,
      },
    },
    required: ["enrichmentSource", "dataTypes"],
  },

  "create-task": {
    type: "object",
    title: "Create Task",
    description: "Create a task for team members",
    properties: {
      taskTitle: {
        type: "string",
        title: "Task Title",
        description: "Title of the task",
        maxLength: 200,
      },
      taskDescription: {
        type: "string",
        title: "Task Description",
        description: "Detailed description of the task",
        format: "textarea",
        maxLength: 2000,
      },
      assignee: {
        type: "string",
        title: "Assignee",
        description: "Who should be assigned this task",
        enum: [
          "recruiter",
          "hiring_manager",
          "interviewer",
          "hr_admin",
          "custom",
        ],
        default: "recruiter",
      },
      customAssignee: {
        type: "string",
        title: "Custom Assignee",
        description: 'Email of custom assignee (when assignee is "custom")',
        format: "email",
      },
      priority: {
        type: "string",
        title: "Priority",
        description: "Task priority level",
        enum: ["low", "medium", "high", "urgent"],
        default: "medium",
      },
      dueDate: {
        type: "string",
        title: "Due Date",
        description: "When the task should be completed",
        format: "datetime-local",
      },
      dueDateOffset: {
        type: "number",
        title: "Due Date Offset (days)",
        description:
          "Days from now to set due date (if due date not specified)",
        minimum: 0,
        maximum: 365,
        default: 7,
      },
      category: {
        type: "string",
        title: "Task Category",
        description: "Category of the task",
        enum: [
          "screening",
          "interview",
          "reference_check",
          "offer",
          "onboarding",
          "follow_up",
          "other",
        ],
        default: "screening",
      },
      autoComplete: {
        type: "boolean",
        title: "Auto Complete",
        description: "Automatically complete task when conditions are met",
        default: false,
      },
      completionCondition: {
        type: "string",
        title: "Completion Condition",
        description: "Condition for auto-completion",
        enum: [
          "status_change",
          "document_upload",
          "assessment_complete",
          "interview_scheduled",
        ],
        default: "status_change",
      },
      sendReminders: {
        type: "boolean",
        title: "Send Reminders",
        description: "Send reminder notifications",
        default: true,
      },
      reminderInterval: {
        type: "number",
        title: "Reminder Interval (hours)",
        description: "Hours between reminder notifications",
        minimum: 1,
        maximum: 168,
        default: 24,
      },
    },
    required: ["taskTitle", "assignee"],
  },

  "add-tag": {
    type: "object",
    title: "Add Tag",
    description: "Add tags to a candidate",
    properties: {
      tagCategory: {
        type: "string",
        title: "Tag Category",
        description: "Category of tags to add",
        enum: [
          "skills",
          "experience",
          "education",
          "location",
          "status",
          "source",
          "custom",
        ],
        default: "skills",
      },
      tags: {
        type: "string",
        title: "Tags",
        description: "Comma-separated list of tags to add",
        placeholder: "e.g., javascript, react, senior-level",
      },
      tagColor: {
        type: "string",
        title: "Tag Color",
        description: "Color for the tags",
        enum: ["blue", "green", "yellow", "red", "purple", "gray"],
        default: "blue",
      },
      overwriteExisting: {
        type: "boolean",
        title: "Overwrite Existing",
        description: "Replace existing tags in this category",
        default: false,
      },
      visibility: {
        type: "string",
        title: "Tag Visibility",
        description: "Who can see these tags",
        enum: ["public", "team", "recruiters", "private"],
        default: "team",
      },
      autoExpire: {
        type: "boolean",
        title: "Auto Expire",
        description: "Automatically remove tags after a period",
        default: false,
      },
      expireDays: {
        type: "number",
        title: "Expire After (days)",
        description: "Days after which tags expire",
        minimum: 1,
        maximum: 365,
        default: 90,
      },
      notifyChanges: {
        type: "boolean",
        title: "Notify Changes",
        description: "Notify team when tags are added",
        default: false,
      },
    },
    required: ["tagCategory", "tags"],
  },

  "score-candidate": {
    type: "object",
    title: "Score Candidate",
    description: "Assign a score to a candidate",
    properties: {
      scoringMethod: {
        type: "string",
        title: "Scoring Method",
        description: "How to calculate the score",
        enum: ["manual", "weighted_criteria", "ai_assessment", "composite"],
        default: "weighted_criteria",
      },
      scoreRange: {
        type: "string",
        title: "Score Range",
        description: "Range for the score",
        enum: ["1-5", "1-10", "0-100", "A-F"],
        default: "1-10",
      },
      criteria: {
        type: "array",
        title: "Scoring Criteria",
        description: "Criteria to evaluate",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Criteria Name",
            },
            weight: {
              type: "number",
              title: "Weight (%)",
              minimum: 0,
              maximum: 100,
            },
          },
        },
        default: [
          { name: "Technical Skills", weight: 30 },
          { name: "Experience", weight: 25 },
          { name: "Cultural Fit", weight: 20 },
          { name: "Communication", weight: 15 },
          { name: "Education", weight: 10 },
        ],
      },
      manualScore: {
        type: "number",
        title: "Manual Score",
        description: "Manual score value (when method is manual)",
        minimum: 0,
        maximum: 100,
      },
      aiModel: {
        type: "string",
        title: "AI Model",
        description: "AI model for assessment (when method is ai_assessment)",
        enum: ["gpt-4", "claude", "custom"],
        default: "gpt-4",
      },
      scoreNote: {
        type: "string",
        title: "Score Note",
        description: "Note explaining the score",
        format: "textarea",
        maxLength: 1000,
      },
      updateExisting: {
        type: "boolean",
        title: "Update Existing Score",
        description: "Update existing score if present",
        default: true,
      },
      notifyTeam: {
        type: "boolean",
        title: "Notify Team",
        description: "Notify team of score changes",
        default: false,
      },
      thresholdActions: {
        type: "boolean",
        title: "Enable Threshold Actions",
        description: "Trigger actions based on score thresholds",
        default: false,
      },
      highThreshold: {
        type: "number",
        title: "High Score Threshold",
        description: "Score above which candidate is considered high-potential",
        minimum: 0,
        maximum: 100,
        default: 80,
      },
      lowThreshold: {
        type: "number",
        title: "Low Score Threshold",
        description: "Score below which candidate may be rejected",
        minimum: 0,
        maximum: 100,
        default: 40,
      },
    },
    required: ["scoringMethod", "scoreRange"],
  },

  // Missing Condition Nodes
  "education-check": {
    type: "object",
    title: "Education Check",
    description: "Verify education requirements",
    properties: {
      minDegree: {
        type: "string",
        title: "Minimum Degree Level",
        description: "Required minimum education level",
        enum: [
          "high_school",
          "associate",
          "bachelors",
          "masters",
          "doctorate",
          "professional",
        ],
        default: "bachelors",
      },
      fieldOfStudy: {
        type: "string",
        title: "Field of Study",
        description: "Required field of study (optional)",
        placeholder: "e.g., Computer Science, Engineering, Business",
      },
      specificMajors: {
        type: "string",
        title: "Specific Majors",
        description: "Comma-separated list of acceptable majors",
        placeholder:
          "e.g., Computer Science, Software Engineering, Information Technology",
      },
      institutionType: {
        type: "string",
        title: "Institution Type",
        description: "Type of educational institution",
        enum: ["any", "accredited", "top_tier", "specific_list"],
        default: "accredited",
      },
      specificInstitutions: {
        type: "string",
        title: "Specific Institutions",
        description:
          "Comma-separated list of acceptable institutions (when type is specific_list)",
        format: "textarea",
      },
      gpaRequirement: {
        type: "number",
        title: "Minimum GPA",
        description: "Minimum GPA requirement (optional)",
        minimum: 0.0,
        maximum: 4.0,
        step: 0.1,
      },
      considerEquivalentExp: {
        type: "boolean",
        title: "Consider Equivalent Experience",
        description: "Accept equivalent work experience in lieu of degree",
        default: true,
      },
      experienceRatio: {
        type: "number",
        title: "Experience Ratio (years per degree level)",
        description: "Years of experience equivalent to each degree level",
        minimum: 1,
        maximum: 10,
        default: 2,
      },
      verifyCertificates: {
        type: "boolean",
        title: "Verify Certificates",
        description: "Require verification of educational certificates",
        default: false,
      },
      allowOngoing: {
        type: "boolean",
        title: "Allow Ongoing Education",
        description: "Accept candidates currently pursuing required degree",
        default: true,
      },
    },
    required: ["minDegree"],
  },

  "location-check": {
    type: "object",
    title: "Location Check",
    description: "Verify location requirements",
    properties: {
      locationType: {
        type: "string",
        title: "Location Type",
        description: "Type of location requirement",
        enum: ["onsite", "remote", "hybrid", "flexible"],
        default: "onsite",
      },
      requiredLocation: {
        type: "string",
        title: "Required Location",
        description: "Specific location requirement (city, state, country)",
        placeholder: "e.g., San Francisco, CA, USA",
      },
      maxDistance: {
        type: "number",
        title: "Max Distance (miles)",
        description: "Maximum distance from required location",
        minimum: 0,
        maximum: 5000,
        default: 50,
      },
      allowRelocation: {
        type: "boolean",
        title: "Allow Relocation",
        description: "Accept candidates willing to relocate",
        default: true,
      },
      relocationAssistance: {
        type: "boolean",
        title: "Provide Relocation Assistance",
        description: "Company provides relocation assistance",
        default: false,
      },
      timeZoneRequirement: {
        type: "string",
        title: "Time Zone Requirement",
        description: "Required time zone overlap",
        enum: [
          "none",
          "same_timezone",
          "overlap_4h",
          "overlap_6h",
          "overlap_8h",
        ],
        default: "none",
      },
      travelRequirement: {
        type: "string",
        title: "Travel Requirement",
        description: "Amount of travel required",
        enum: ["none", "minimal", "occasional", "frequent", "extensive"],
        default: "none",
      },
      visaSponsorship: {
        type: "boolean",
        title: "Visa Sponsorship Available",
        description: "Company sponsors work visas",
        default: false,
      },
      remoteEquipment: {
        type: "boolean",
        title: "Provide Remote Equipment",
        description: "Company provides equipment for remote work",
        default: false,
      },
    },
    required: ["locationType"],
  },

  "salary-check": {
    type: "object",
    title: "Salary Check",
    description: "Check salary requirements and expectations",
    properties: {
      salaryRange: {
        type: "object",
        title: "Salary Range",
        properties: {
          min: {
            type: "number",
            title: "Minimum Salary",
            minimum: 0,
          },
          max: {
            type: "number",
            title: "Maximum Salary",
            minimum: 0,
          },
          currency: {
            type: "string",
            title: "Currency",
            enum: ["USD", "EUR", "GBP", "CAD", "AUD", "JPY"],
            default: "USD",
          },
        },
        required: ["min", "max", "currency"],
      },
      salaryType: {
        type: "string",
        title: "Salary Type",
        description: "Type of compensation",
        enum: ["hourly", "annual", "contract", "commission"],
        default: "annual",
      },
      includeBonus: {
        type: "boolean",
        title: "Include Bonus/Equity",
        description: "Include bonus and equity in total compensation",
        default: true,
      },
      bonusPercentage: {
        type: "number",
        title: "Bonus Percentage",
        description: "Expected bonus as percentage of base salary",
        minimum: 0,
        maximum: 200,
        default: 10,
      },
      equityOffered: {
        type: "boolean",
        title: "Equity Offered",
        description: "Position includes equity compensation",
        default: false,
      },
      negotiable: {
        type: "boolean",
        title: "Salary Negotiable",
        description: "Salary is open to negotiation",
        default: true,
      },
      experienceAdjustment: {
        type: "boolean",
        title: "Adjust for Experience",
        description: "Adjust salary expectations based on experience level",
        default: true,
      },
      locationAdjustment: {
        type: "boolean",
        title: "Location-based Adjustment",
        description: "Adjust salary for cost of living differences",
        default: true,
      },
      marketComparison: {
        type: "boolean",
        title: "Compare to Market Rate",
        description: "Compare expectations to market rates",
        default: true,
      },
      confidential: {
        type: "boolean",
        title: "Keep Confidential",
        description: "Keep salary information confidential",
        default: true,
      },
    },
    required: ["salaryRange", "salaryType"],
  },

  "availability-check": {
    type: "object",
    title: "Availability Check",
    description: "Check candidate availability and start date",
    properties: {
      startDateRequirement: {
        type: "string",
        title: "Start Date Requirement",
        description: "When candidate should be available to start",
        enum: [
          "immediate",
          "within_2_weeks",
          "within_month",
          "within_3_months",
          "flexible",
        ],
        default: "within_month",
      },
      specificStartDate: {
        type: "string",
        title: "Specific Start Date",
        description: "Specific required start date (optional)",
        format: "date",
      },
      noticePeriod: {
        type: "number",
        title: "Maximum Notice Period (weeks)",
        description: "Maximum acceptable notice period",
        minimum: 0,
        maximum: 26,
        default: 4,
      },
      workSchedule: {
        type: "string",
        title: "Work Schedule",
        description: "Required work schedule",
        enum: ["full_time", "part_time", "contract", "flexible", "shift_work"],
        default: "full_time",
      },
      hoursPerWeek: {
        type: "number",
        title: "Hours Per Week",
        description: "Required hours per week",
        minimum: 1,
        maximum: 80,
        default: 40,
      },
      shiftPreference: {
        type: "string",
        title: "Shift Preference",
        description: "Preferred work shift",
        enum: ["day", "evening", "night", "rotating", "flexible"],
        default: "day",
      },
      weekendWork: {
        type: "boolean",
        title: "Weekend Work Required",
        description: "Position requires weekend work",
        default: false,
      },
      overtimeExpected: {
        type: "boolean",
        title: "Overtime Expected",
        description: "Position may require overtime",
        default: false,
      },
      seasonalWork: {
        type: "boolean",
        title: "Seasonal Work",
        description: "Position is seasonal or temporary",
        default: false,
      },
      contractDuration: {
        type: "number",
        title: "Contract Duration (months)",
        description: "Duration for contract positions",
        minimum: 1,
        maximum: 60,
        default: 12,
      },
    },
    required: ["startDateRequirement", "workSchedule"],
  },

  "visa-check": {
    type: "object",
    title: "Visa/Work Authorization Check",
    description: "Check visa and work authorization status",
    properties: {
      workAuthRequired: {
        type: "boolean",
        title: "Work Authorization Required",
        description: "Position requires work authorization",
        default: true,
      },
      countryRequirement: {
        type: "string",
        title: "Country Requirement",
        description: "Country where work authorization is needed",
        default: "United States",
      },
      acceptedStatuses: {
        type: "array",
        title: "Accepted Work Statuses",
        description: "Acceptable work authorization statuses",
        items: {
          type: "string",
          enum: [
            "citizen",
            "permanent_resident",
            "h1b",
            "l1",
            "opt",
            "cpt",
            "tn",
            "e3",
            "other_visa",
          ],
        },
        default: ["citizen", "permanent_resident"],
      },
      sponsorshipAvailable: {
        type: "boolean",
        title: "Sponsorship Available",
        description: "Company can sponsor work visas",
        default: false,
      },
      sponsorshipTypes: {
        type: "array",
        title: "Sponsorship Types",
        description: "Types of visa sponsorship available",
        items: {
          type: "string",
          enum: ["h1b", "l1", "o1", "tn", "green_card", "other"],
        },
      },
      currentVisaStatus: {
        type: "boolean",
        title: "Check Current Status",
        description: "Verify current visa status",
        default: true,
      },
      expirationCheck: {
        type: "boolean",
        title: "Check Expiration Date",
        description: "Verify visa expiration date",
        default: true,
      },
      minValidityPeriod: {
        type: "number",
        title: "Minimum Validity Period (months)",
        description: "Minimum remaining validity period required",
        minimum: 1,
        maximum: 60,
        default: 6,
      },
      renewalSupport: {
        type: "boolean",
        title: "Renewal Support",
        description: "Company supports visa renewals",
        default: false,
      },
      dependentSponsorship: {
        type: "boolean",
        title: "Dependent Sponsorship",
        description: "Company sponsors dependents",
        default: false,
      },
      backgroundCheckRequired: {
        type: "boolean",
        title: "Background Check Required",
        description: "Position requires security clearance/background check",
        default: false,
      },
    },
    required: ["workAuthRequired", "acceptedStatuses"],
  },

  "filter-condition": {
    type: "object",
    title: "Filter Condition",
    description: "Filter based on custom criteria",
    properties: {
      filterField: {
        type: "string",
        title: "Field to Filter",
        description: "Field to apply filter on",
        enum: [
          "name",
          "email",
          "phone",
          "location",
          "experience",
          "education",
          "skills",
          "status",
          "source",
          "score",
          "tags",
          "custom",
        ],
        default: "status",
      },
      customField: {
        type: "string",
        title: "Custom Field Name",
        description: 'Name of custom field (when filterField is "custom")',
      },
      operator: {
        type: "string",
        title: "Comparison Operator",
        description: "How to compare the values",
        enum: [
          "equals",
          "not_equals",
          "contains",
          "not_contains",
          "starts_with",
          "ends_with",
          "greater_than",
          "less_than",
          "greater_equal",
          "less_equal",
          "in_list",
          "not_in_list",
          "is_empty",
          "is_not_empty",
          "regex_match",
        ],
        default: "equals",
      },
      filterValue: {
        type: "string",
        title: "Filter Value",
        description: "Value to compare against",
        placeholder: "Enter comparison value",
      },
      valueList: {
        type: "string",
        title: "Value List",
        description:
          "Comma-separated list of values (for in_list/not_in_list operators)",
        placeholder: "value1, value2, value3",
      },
      caseSensitive: {
        type: "boolean",
        title: "Case Sensitive",
        description: "Make string comparisons case sensitive",
        default: false,
      },
      regexPattern: {
        type: "string",
        title: "Regex Pattern",
        description: "Regular expression pattern (for regex_match operator)",
        placeholder: "^[A-Za-z]+$",
      },
      logicalOperator: {
        type: "string",
        title: "Logical Operator",
        description: "How to combine with other conditions",
        enum: ["and", "or"],
        default: "and",
      },
      negateResult: {
        type: "boolean",
        title: "Negate Result",
        description: "Invert the filter result",
        default: false,
      },
    },
    required: ["filterField", "operator"],
  },

  "data-comparison": {
    type: "object",
    title: "Data Comparison",
    description: "Compare data values between fields",
    properties: {
      leftField: {
        type: "string",
        title: "Left Field",
        description: "First field to compare",
        placeholder: "e.g., candidate.experience_years",
      },
      rightField: {
        type: "string",
        title: "Right Field",
        description: "Second field to compare (optional if using static value)",
        placeholder: "e.g., job.min_experience",
      },
      staticValue: {
        type: "string",
        title: "Static Value",
        description:
          "Static value to compare against (if not using right field)",
        placeholder: "Enter static comparison value",
      },
      comparisonType: {
        type: "string",
        title: "Comparison Type",
        description: "Type of comparison to perform",
        enum: ["numeric", "string", "date", "boolean", "array"],
        default: "numeric",
      },
      operator: {
        type: "string",
        title: "Comparison Operator",
        description: "How to compare the values",
        enum: [
          "equals",
          "not_equals",
          "greater_than",
          "less_than",
          "greater_equal",
          "less_equal",
          "contains",
          "not_contains",
          "starts_with",
          "ends_with",
        ],
        default: "equals",
      },
      tolerance: {
        type: "number",
        title: "Tolerance",
        description: "Tolerance for numeric comparisons",
        minimum: 0,
        default: 0,
      },
      dateFormat: {
        type: "string",
        title: "Date Format",
        description: "Format for date comparisons",
        enum: ["YYYY-MM-DD", "MM/DD/YYYY", "DD/MM/YYYY", "ISO8601"],
        default: "YYYY-MM-DD",
      },
      caseSensitive: {
        type: "boolean",
        title: "Case Sensitive",
        description: "Make string comparisons case sensitive",
        default: false,
      },
      nullHandling: {
        type: "string",
        title: "Null Value Handling",
        description: "How to handle null/empty values",
        enum: ["fail", "pass", "treat_as_zero", "treat_as_empty_string"],
        default: "fail",
      },
    },
    required: ["leftField", "comparisonType", "operator"],
  },

  // Missing Output Nodes
  "send-message": {
    type: "object",
    title: "Send Message",
    description: "Send message to candidate or team",
    properties: {
      messageType: {
        type: "string",
        title: "Message Type",
        description: "Type of message to send",
        enum: ["email", "sms", "slack", "teams", "in_app"],
        default: "email",
      },
      recipient: {
        type: "string",
        title: "Recipient",
        description: "Who should receive the message",
        enum: ["candidate", "recruiter", "hiring_manager", "team", "custom"],
        default: "candidate",
      },
      customRecipient: {
        type: "string",
        title: "Custom Recipient",
        description: "Email/phone of custom recipient",
        placeholder: "<EMAIL> or +1234567890",
      },
      messageTemplate: {
        type: "string",
        title: "Message Template",
        description: "Pre-defined message template",
        enum: [
          "welcome",
          "status_update",
          "interview_reminder",
          "follow_up",
          "thank_you",
          "rejection",
          "offer",
          "custom",
        ],
        default: "status_update",
      },
      customMessage: {
        type: "string",
        title: "Custom Message",
        description: 'Custom message content (when template is "custom")',
        format: "textarea",
        maxLength: 5000,
      },
      subject: {
        type: "string",
        title: "Subject Line",
        description: "Subject line for email messages",
        maxLength: 200,
      },
      personalize: {
        type: "boolean",
        title: "Personalize Message",
        description: "Include candidate-specific information",
        default: true,
      },
      includeAttachments: {
        type: "boolean",
        title: "Include Attachments",
        description: "Attach relevant documents",
        default: false,
      },
      attachmentTypes: {
        type: "array",
        title: "Attachment Types",
        description: "Types of attachments to include",
        items: {
          type: "string",
          enum: [
            "job_description",
            "company_info",
            "benefits",
            "offer_letter",
            "assessment_results",
          ],
        },
      },
      sendImmediately: {
        type: "boolean",
        title: "Send Immediately",
        description: "Send message immediately or schedule",
        default: true,
      },
      scheduleDelay: {
        type: "number",
        title: "Schedule Delay (hours)",
        description: "Hours to wait before sending",
        minimum: 0,
        maximum: 8760,
        default: 0,
      },
      trackDelivery: {
        type: "boolean",
        title: "Track Delivery",
        description: "Track message delivery status",
        default: true,
      },
      trackOpens: {
        type: "boolean",
        title: "Track Opens",
        description: "Track when message is opened (email only)",
        default: true,
      },
      requireResponse: {
        type: "boolean",
        title: "Require Response",
        description: "Mark message as requiring response",
        default: false,
      },
      responseDeadline: {
        type: "string",
        title: "Response Deadline",
        description: "Deadline for response",
        format: "datetime-local",
      },
    },
    required: ["messageType", "recipient", "messageTemplate"],
  },

  "export-data": {
    type: "object",
    title: "Export Data",
    description: "Export data to external system or file",
    properties: {
      exportFormat: {
        type: "string",
        title: "Export Format",
        description: "Format for exported data",
        enum: ["csv", "json", "xml", "excel", "pdf", "api"],
        default: "csv",
      },
      dataSource: {
        type: "string",
        title: "Data Source",
        description: "What data to export",
        enum: [
          "candidate_profile",
          "application_data",
          "interview_results",
          "assessment_scores",
          "workflow_data",
          "custom_query",
        ],
        default: "candidate_profile",
      },
      customQuery: {
        type: "string",
        title: "Custom Query",
        description:
          "Custom SQL query for data export (when source is custom_query)",
        format: "textarea",
        placeholder: "SELECT * FROM candidates WHERE...",
      },
      fieldsToInclude: {
        type: "array",
        title: "Fields to Include",
        description: "Specific fields to include in export",
        items: {
          type: "string",
        },
      },
      destination: {
        type: "string",
        title: "Export Destination",
        description: "Where to send the exported data",
        enum: [
          "download",
          "email",
          "ftp",
          "sftp",
          "s3",
          "api_endpoint",
          "database",
        ],
        default: "download",
      },
      emailRecipient: {
        type: "string",
        title: "Email Recipient",
        description: "Email address to send export (when destination is email)",
        format: "email",
      },
      ftpSettings: {
        type: "object",
        title: "FTP Settings",
        properties: {
          host: { type: "string", title: "FTP Host" },
          username: { type: "string", title: "Username" },
          password: { type: "string", title: "Password", format: "password" },
          path: { type: "string", title: "Remote Path" },
        },
      },
      apiEndpoint: {
        type: "string",
        title: "API Endpoint",
        description: "API endpoint URL (when destination is api_endpoint)",
        format: "uri",
      },
      apiHeaders: {
        type: "string",
        title: "API Headers",
        description: "JSON object with API headers",
        format: "textarea",
        placeholder:
          '{"Authorization": "Bearer token", "Content-Type": "application/json"}',
      },
      filename: {
        type: "string",
        title: "Filename",
        description: "Name for exported file",
        placeholder: "export_{{date}}_{{time}}",
      },
      includeHeaders: {
        type: "boolean",
        title: "Include Headers",
        description: "Include column headers in export",
        default: true,
      },
      compressFile: {
        type: "boolean",
        title: "Compress File",
        description: "Compress exported file",
        default: false,
      },
      encryptFile: {
        type: "boolean",
        title: "Encrypt File",
        description: "Encrypt exported file",
        default: false,
      },
      encryptionKey: {
        type: "string",
        title: "Encryption Key",
        description: "Key for file encryption",
        format: "password",
      },
    },
    required: ["exportFormat", "dataSource", "destination"],
  },

  "archive-candidate": {
    type: "object",
    title: "Archive Candidate",
    description: "Archive a candidate record",
    properties: {
      archiveReason: {
        type: "string",
        title: "Archive Reason",
        description: "Reason for archiving the candidate",
        enum: [
          "position_filled",
          "not_qualified",
          "withdrew_application",
          "no_response",
          "failed_assessment",
          "failed_interview",
          "salary_mismatch",
          "location_mismatch",
          "other",
        ],
        default: "position_filled",
      },
      customReason: {
        type: "string",
        title: "Custom Reason",
        description: 'Custom reason for archiving (when reason is "other")',
        maxLength: 500,
      },
      retentionPeriod: {
        type: "number",
        title: "Retention Period (months)",
        description: "How long to keep archived data",
        minimum: 1,
        maximum: 120,
        default: 24,
      },
      notifyCandidate: {
        type: "boolean",
        title: "Notify Candidate",
        description: "Send notification to candidate about archiving",
        default: false,
      },
      notificationTemplate: {
        type: "string",
        title: "Notification Template",
        description: "Template for candidate notification",
        enum: ["position_filled", "not_selected", "thank_you", "custom"],
        default: "thank_you",
      },
      customNotification: {
        type: "string",
        title: "Custom Notification",
        description: "Custom notification message",
        format: "textarea",
        maxLength: 2000,
      },
      preserveData: {
        type: "array",
        title: "Data to Preserve",
        description: "Types of data to preserve in archive",
        items: {
          type: "string",
          enum: [
            "profile",
            "resume",
            "assessments",
            "interview_notes",
            "communications",
            "scores",
            "tags",
          ],
        },
        default: ["profile", "resume", "assessments"],
      },
      allowReactivation: {
        type: "boolean",
        title: "Allow Reactivation",
        description: "Allow candidate to be reactivated for future positions",
        default: true,
      },
      reactivationConditions: {
        type: "string",
        title: "Reactivation Conditions",
        description: "Conditions under which candidate can be reactivated",
        format: "textarea",
        placeholder:
          "e.g., Only for senior positions, After 6 months, With manager approval",
      },
      gdprCompliant: {
        type: "boolean",
        title: "GDPR Compliant",
        description: "Ensure archiving complies with GDPR requirements",
        default: true,
      },
      anonymizeData: {
        type: "boolean",
        title: "Anonymize Data",
        description: "Anonymize personal data in archive",
        default: false,
      },
      auditTrail: {
        type: "boolean",
        title: "Maintain Audit Trail",
        description: "Keep audit trail of archiving action",
        default: true,
      },
    },
    required: ["archiveReason", "retentionPeriod"],
  },

  "generate-report": {
    type: "object",
    title: "Generate Report",
    description: "Generate a report from workflow data",
    properties: {
      reportType: {
        type: "string",
        title: "Report Type",
        description: "Type of report to generate",
        enum: [
          "candidate_summary",
          "workflow_performance",
          "hiring_metrics",
          "assessment_results",
          "interview_feedback",
          "time_to_hire",
          "source_effectiveness",
          "custom",
        ],
        default: "candidate_summary",
      },
      reportFormat: {
        type: "string",
        title: "Report Format",
        description: "Format for the generated report",
        enum: ["pdf", "html", "excel", "csv", "json"],
        default: "pdf",
      },
      timeRange: {
        type: "string",
        title: "Time Range",
        description: "Time period for report data",
        enum: [
          "last_7_days",
          "last_30_days",
          "last_90_days",
          "last_year",
          "custom_range",
        ],
        default: "last_30_days",
      },
      customDateRange: {
        type: "object",
        title: "Custom Date Range",
        properties: {
          startDate: {
            type: "string",
            title: "Start Date",
            format: "date",
          },
          endDate: {
            type: "string",
            title: "End Date",
            format: "date",
          },
        },
      },
      includeCharts: {
        type: "boolean",
        title: "Include Charts",
        description: "Include visual charts in report",
        default: true,
      },
      chartTypes: {
        type: "array",
        title: "Chart Types",
        description: "Types of charts to include",
        items: {
          type: "string",
          enum: ["bar", "line", "pie", "scatter", "funnel", "timeline"],
        },
        default: ["bar", "pie"],
      },
      groupBy: {
        type: "string",
        title: "Group By",
        description: "How to group report data",
        enum: [
          "department",
          "position",
          "recruiter",
          "source",
          "status",
          "date",
        ],
        default: "position",
      },
      filters: {
        type: "array",
        title: "Report Filters",
        description: "Filters to apply to report data",
        items: {
          type: "object",
          properties: {
            field: { type: "string", title: "Field" },
            operator: { type: "string", title: "Operator" },
            value: { type: "string", title: "Value" },
          },
        },
      },
      includeRawData: {
        type: "boolean",
        title: "Include Raw Data",
        description: "Include raw data appendix",
        default: false,
      },
      confidential: {
        type: "boolean",
        title: "Mark as Confidential",
        description: "Mark report as confidential",
        default: true,
      },
      watermark: {
        type: "boolean",
        title: "Add Watermark",
        description: "Add company watermark to report",
        default: true,
      },
      distribution: {
        type: "array",
        title: "Distribution List",
        description: "Email addresses to send report to",
        items: {
          type: "string",
          format: "email",
        },
      },
      scheduleRecurring: {
        type: "boolean",
        title: "Schedule Recurring",
        description: "Generate report on recurring schedule",
        default: false,
      },
      recurringFrequency: {
        type: "string",
        title: "Recurring Frequency",
        description: "How often to generate recurring report",
        enum: ["daily", "weekly", "monthly", "quarterly"],
        default: "monthly",
      },
    },
    required: ["reportType", "reportFormat", "timeRange"],
  },

  // Missing Transform Nodes
  "data-filter": {
    type: "object",
    title: "Data Filter",
    description: "Filter data between workflow steps",
    properties: {
      filterType: {
        type: "string",
        title: "Filter Type",
        description: "Type of filtering to apply",
        enum: ["include", "exclude", "transform", "validate"],
        default: "include",
      },
      filterCriteria: {
        type: "array",
        title: "Filter Criteria",
        description: "Criteria for filtering data",
        items: {
          type: "object",
          properties: {
            field: {
              type: "string",
              title: "Field Name",
              placeholder: "e.g., candidate.experience_years",
            },
            operator: {
              type: "string",
              title: "Operator",
              enum: [
                "equals",
                "not_equals",
                "greater_than",
                "less_than",
                "contains",
                "not_contains",
                "in_list",
                "not_in_list",
                "is_null",
                "is_not_null",
              ],
              default: "equals",
            },
            value: {
              type: "string",
              title: "Value",
              placeholder: "Comparison value",
            },
            logicalOperator: {
              type: "string",
              title: "Logical Operator",
              enum: ["and", "or"],
              default: "and",
            },
          },
          required: ["field", "operator"],
        },
      },
      outputFormat: {
        type: "string",
        title: "Output Format",
        description: "Format for filtered data",
        enum: ["original", "array", "object", "csv", "json"],
        default: "original",
      },
      sortBy: {
        type: "string",
        title: "Sort By",
        description: "Field to sort filtered results by",
        placeholder: "e.g., candidate.name",
      },
      sortOrder: {
        type: "string",
        title: "Sort Order",
        description: "Order for sorting",
        enum: ["asc", "desc"],
        default: "asc",
      },
      limit: {
        type: "number",
        title: "Limit Results",
        description: "Maximum number of results to return",
        minimum: 1,
        maximum: 10000,
      },
      includeMetadata: {
        type: "boolean",
        title: "Include Metadata",
        description: "Include filtering metadata in output",
        default: false,
      },
      errorHandling: {
        type: "string",
        title: "Error Handling",
        description: "How to handle filtering errors",
        enum: ["fail", "skip", "log_and_continue"],
        default: "fail",
      },
    },
    required: ["filterType", "filterCriteria"],
  },

  "data-merge": {
    type: "object",
    title: "Data Merge",
    description: "Merge data from multiple sources",
    properties: {
      mergeStrategy: {
        type: "string",
        title: "Merge Strategy",
        description: "How to merge the data",
        enum: [
          "union",
          "intersection",
          "left_join",
          "right_join",
          "inner_join",
          "outer_join",
          "append",
        ],
        default: "union",
      },
      primaryKey: {
        type: "string",
        title: "Primary Key",
        description: "Field to use as primary key for joins",
        placeholder: "e.g., candidate.id",
      },
      dataSources: {
        type: "array",
        title: "Data Sources",
        description: "Sources of data to merge",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Source Name",
              placeholder: "e.g., candidates, assessments",
            },
            path: {
              type: "string",
              title: "Data Path",
              placeholder: "e.g., workflow.candidates",
            },
            alias: {
              type: "string",
              title: "Alias",
              placeholder: "Short name for this source",
            },
          },
          required: ["name", "path"],
        },
      },
      fieldMapping: {
        type: "array",
        title: "Field Mapping",
        description: "How to map fields between sources",
        items: {
          type: "object",
          properties: {
            sourceField: {
              type: "string",
              title: "Source Field",
              placeholder: "e.g., source1.name",
            },
            targetField: {
              type: "string",
              title: "Target Field",
              placeholder: "e.g., full_name",
            },
            transformation: {
              type: "string",
              title: "Transformation",
              enum: [
                "none",
                "uppercase",
                "lowercase",
                "trim",
                "concat",
                "split",
              ],
              default: "none",
            },
          },
          required: ["sourceField", "targetField"],
        },
      },
      conflictResolution: {
        type: "string",
        title: "Conflict Resolution",
        description: "How to resolve field conflicts",
        enum: [
          "first_wins",
          "last_wins",
          "merge_arrays",
          "prefer_non_null",
          "custom",
        ],
        default: "first_wins",
      },
      customResolution: {
        type: "string",
        title: "Custom Resolution Logic",
        description: "Custom JavaScript for conflict resolution",
        format: "textarea",
        placeholder:
          "function resolve(field1, field2) { return field1 || field2; }",
      },
      validateOutput: {
        type: "boolean",
        title: "Validate Output",
        description: "Validate merged data structure",
        default: true,
      },
      outputSchema: {
        type: "string",
        title: "Output Schema",
        description: "JSON schema for output validation",
        format: "textarea",
      },
    },
    required: ["mergeStrategy", "dataSources"],
  },

  "data-loop": {
    type: "object",
    title: "Loop",
    description: "Loop through a collection of items",
    properties: {
      loopType: {
        type: "string",
        title: "Loop Type",
        description: "Type of loop to execute",
        enum: ["for_each", "while", "do_while", "for_range"],
        default: "for_each",
      },
      dataSource: {
        type: "string",
        title: "Data Source",
        description: "Path to array/collection to loop through",
        placeholder: "e.g., workflow.candidates",
      },
      loopVariable: {
        type: "string",
        title: "Loop Variable",
        description: "Variable name for current item",
        placeholder: "e.g., candidate",
        default: "item",
      },
      indexVariable: {
        type: "string",
        title: "Index Variable",
        description: "Variable name for current index",
        placeholder: "e.g., index",
        default: "index",
      },
      condition: {
        type: "string",
        title: "Loop Condition",
        description: "Condition for while/do-while loops",
        placeholder: "e.g., index < 10",
      },
      startIndex: {
        type: "number",
        title: "Start Index",
        description: "Starting index for for_range loops",
        minimum: 0,
        default: 0,
      },
      endIndex: {
        type: "number",
        title: "End Index",
        description: "Ending index for for_range loops",
        minimum: 0,
        default: 10,
      },
      stepSize: {
        type: "number",
        title: "Step Size",
        description: "Step size for for_range loops",
        minimum: 1,
        default: 1,
      },
      maxIterations: {
        type: "number",
        title: "Max Iterations",
        description: "Maximum number of iterations (safety limit)",
        minimum: 1,
        maximum: 10000,
        default: 1000,
      },
      parallelExecution: {
        type: "boolean",
        title: "Parallel Execution",
        description: "Execute loop iterations in parallel",
        default: false,
      },
      maxConcurrency: {
        type: "number",
        title: "Max Concurrency",
        description: "Maximum concurrent iterations",
        minimum: 1,
        maximum: 100,
        default: 5,
      },
      continueOnError: {
        type: "boolean",
        title: "Continue on Error",
        description: "Continue loop if iteration fails",
        default: false,
      },
      collectResults: {
        type: "boolean",
        title: "Collect Results",
        description: "Collect results from each iteration",
        default: true,
      },
      breakCondition: {
        type: "string",
        title: "Break Condition",
        description: "Condition to break out of loop early",
        placeholder: 'e.g., item.status === "found"',
      },
    },
    required: ["loopType", "dataSource"],
  },

  schedule: {
    type: "object",
    title: "Schedule",
    description: "Schedule a future action",
    properties: {
      scheduleType: {
        type: "string",
        title: "Schedule Type",
        description: "Type of scheduling",
        enum: ["delay", "specific_time", "recurring", "conditional"],
        default: "delay",
      },
      delayAmount: {
        type: "number",
        title: "Delay Amount",
        description: "Amount of delay",
        minimum: 1,
        maximum: 365,
        default: 1,
      },
      delayUnit: {
        type: "string",
        title: "Delay Unit",
        description: "Unit for delay",
        enum: ["minutes", "hours", "days", "weeks", "months"],
        default: "hours",
      },
      specificDateTime: {
        type: "string",
        title: "Specific Date/Time",
        description: "Specific date and time to execute",
        format: "datetime-local",
      },
      recurringPattern: {
        type: "string",
        title: "Recurring Pattern",
        description: "Pattern for recurring execution",
        enum: ["hourly", "daily", "weekly", "monthly", "yearly", "custom_cron"],
        default: "daily",
      },
      cronExpression: {
        type: "string",
        title: "Cron Expression",
        description: "Custom cron expression for scheduling",
        placeholder: "0 9 * * 1-5",
      },
      condition: {
        type: "string",
        title: "Condition",
        description: "Condition that must be met to execute",
        placeholder: 'e.g., candidate.status === "pending"',
      },
      timezone: {
        type: "string",
        title: "Timezone",
        description: "Timezone for scheduling",
        enum: [
          "UTC",
          "America/New_York",
          "America/Chicago",
          "America/Denver",
          "America/Los_Angeles",
          "Europe/London",
          "Europe/Paris",
          "Asia/Tokyo",
        ],
        default: "UTC",
      },
      businessDaysOnly: {
        type: "boolean",
        title: "Business Days Only",
        description: "Only execute on business days",
        default: false,
      },
      skipHolidays: {
        type: "boolean",
        title: "Skip Holidays",
        description: "Skip execution on holidays",
        default: false,
      },
      maxExecutions: {
        type: "number",
        title: "Max Executions",
        description: "Maximum number of executions (for recurring)",
        minimum: 1,
        maximum: 1000,
      },
      endDate: {
        type: "string",
        title: "End Date",
        description: "Date to stop recurring executions",
        format: "date",
      },
      retryOnFailure: {
        type: "boolean",
        title: "Retry on Failure",
        description: "Retry if scheduled execution fails",
        default: true,
      },
      maxRetries: {
        type: "number",
        title: "Max Retries",
        description: "Maximum number of retry attempts",
        minimum: 0,
        maximum: 10,
        default: 3,
      },
      retryDelay: {
        type: "number",
        title: "Retry Delay (minutes)",
        description: "Delay between retry attempts",
        minimum: 1,
        maximum: 1440,
        default: 5,
      },
    },
    required: ["scheduleType"],
  },

  // Missing Integration Nodes
  "linkedin-integration": {
    type: "object",
    title: "LinkedIn Integration",
    description: "Integrate with LinkedIn for candidate sourcing and posting",
    properties: {
      actionType: {
        type: "string",
        title: "Action Type",
        description: "Type of LinkedIn action to perform",
        enum: [
          "post_job",
          "search_candidates",
          "send_message",
          "get_profile",
          "company_update",
          "talent_insights",
        ],
        default: "search_candidates",
      },
      apiCredentials: {
        type: "object",
        title: "API Credentials",
        properties: {
          clientId: {
            type: "string",
            title: "Client ID",
            format: "password",
          },
          clientSecret: {
            type: "string",
            title: "Client Secret",
            format: "password",
          },
          accessToken: {
            type: "string",
            title: "Access Token",
            format: "password",
          },
        },
        required: ["clientId", "clientSecret"],
      },
      jobPostingDetails: {
        type: "object",
        title: "Job Posting Details",
        properties: {
          title: { type: "string", title: "Job Title" },
          description: {
            type: "string",
            title: "Job Description",
            format: "textarea",
          },
          location: { type: "string", title: "Location" },
          employmentType: {
            type: "string",
            title: "Employment Type",
            enum: [
              "full_time",
              "part_time",
              "contract",
              "temporary",
              "internship",
            ],
            default: "full_time",
          },
          experienceLevel: {
            type: "string",
            title: "Experience Level",
            enum: [
              "internship",
              "entry_level",
              "associate",
              "mid_senior",
              "director",
              "executive",
            ],
            default: "mid_senior",
          },
        },
      },
      searchCriteria: {
        type: "object",
        title: "Search Criteria",
        properties: {
          keywords: { type: "string", title: "Keywords" },
          location: { type: "string", title: "Location" },
          industry: { type: "string", title: "Industry" },
          currentCompany: { type: "string", title: "Current Company" },
          pastCompany: { type: "string", title: "Past Company" },
          school: { type: "string", title: "School" },
        },
      },
      maxResults: {
        type: "number",
        title: "Max Results",
        description: "Maximum number of results to return",
        minimum: 1,
        maximum: 1000,
        default: 100,
      },
      useCompanyPage: {
        type: "boolean",
        title: "Use Company Page",
        description: "Post from company page instead of personal profile",
        default: true,
      },
      trackAnalytics: {
        type: "boolean",
        title: "Track Analytics",
        description: "Track engagement analytics",
        default: true,
      },
      rateLimitDelay: {
        type: "number",
        title: "Rate Limit Delay (ms)",
        description: "Delay between API calls",
        minimum: 100,
        maximum: 10000,
        default: 1000,
      },
    },
    required: ["actionType", "apiCredentials"],
  },

  "job-board-integration": {
    type: "object",
    title: "Job Board Integration",
    description: "Post jobs to external job boards",
    properties: {
      jobBoard: {
        type: "string",
        title: "Job Board",
        description: "Target job board for posting",
        enum: [
          "indeed",
          "glassdoor",
          "monster",
          "careerbuilder",
          "ziprecruiter",
          "dice",
          "stackoverflow",
          "custom",
        ],
        default: "indeed",
      },
      customJobBoard: {
        type: "object",
        title: "Custom Job Board",
        properties: {
          name: { type: "string", title: "Board Name" },
          apiUrl: { type: "string", title: "API URL", format: "uri" },
          authMethod: {
            type: "string",
            title: "Authentication Method",
            enum: ["api_key", "oauth", "basic_auth"],
            default: "api_key",
          },
        },
      },
      credentials: {
        type: "object",
        title: "API Credentials",
        properties: {
          apiKey: { type: "string", title: "API Key", format: "password" },
          username: { type: "string", title: "Username" },
          password: { type: "string", title: "Password", format: "password" },
          clientId: { type: "string", title: "Client ID" },
          clientSecret: {
            type: "string",
            title: "Client Secret",
            format: "password",
          },
        },
      },
      jobDetails: {
        type: "object",
        title: "Job Details",
        properties: {
          title: { type: "string", title: "Job Title" },
          description: {
            type: "string",
            title: "Job Description",
            format: "textarea",
          },
          location: { type: "string", title: "Location" },
          salaryRange: { type: "string", title: "Salary Range" },
          employmentType: {
            type: "string",
            title: "Employment Type",
            enum: [
              "full_time",
              "part_time",
              "contract",
              "temporary",
              "internship",
            ],
            default: "full_time",
          },
          category: { type: "string", title: "Job Category" },
          requirements: {
            type: "string",
            title: "Requirements",
            format: "textarea",
          },
          benefits: { type: "string", title: "Benefits", format: "textarea" },
        },
        required: ["title", "description", "location"],
      },
      postingOptions: {
        type: "object",
        title: "Posting Options",
        properties: {
          duration: {
            type: "number",
            title: "Posting Duration (days)",
            minimum: 1,
            maximum: 365,
            default: 30,
          },
          featured: {
            type: "boolean",
            title: "Featured Posting",
            description: "Make this a featured job posting",
            default: false,
          },
          urgentHiring: {
            type: "boolean",
            title: "Urgent Hiring",
            description: "Mark as urgent hiring",
            default: false,
          },
          autoRenew: {
            type: "boolean",
            title: "Auto Renew",
            description: "Automatically renew posting",
            default: false,
          },
        },
      },
      syncSettings: {
        type: "object",
        title: "Sync Settings",
        properties: {
          syncApplications: {
            type: "boolean",
            title: "Sync Applications",
            description: "Sync applications back to RMS",
            default: true,
          },
          syncFrequency: {
            type: "string",
            title: "Sync Frequency",
            enum: ["real_time", "hourly", "daily", "manual"],
            default: "hourly",
          },
          duplicateHandling: {
            type: "string",
            title: "Duplicate Handling",
            enum: ["skip", "update", "create_new"],
            default: "skip",
          },
        },
      },
      trackingSettings: {
        type: "object",
        title: "Tracking Settings",
        properties: {
          trackViews: {
            type: "boolean",
            title: "Track Views",
            description: "Track job posting views",
            default: true,
          },
          trackApplications: {
            type: "boolean",
            title: "Track Applications",
            description: "Track application submissions",
            default: true,
          },
          utmParameters: {
            type: "string",
            title: "UTM Parameters",
            description: "UTM parameters for tracking",
            placeholder: "utm_source=jobboard&utm_medium=posting",
          },
        },
      },
    },
    required: ["jobBoard", "credentials", "jobDetails"],
  },

  "ats-integration": {
    type: "object",
    title: "ATS Integration",
    description: "Integrate with external Applicant Tracking Systems",
    properties: {
      atsSystem: {
        type: "string",
        title: "ATS System",
        description: "Target ATS system",
        enum: [
          "workday",
          "successfactors",
          "taleo",
          "greenhouse",
          "lever",
          "bamboohr",
          "icims",
          "jobvite",
          "custom",
        ],
        default: "greenhouse",
      },
      customATS: {
        type: "object",
        title: "Custom ATS",
        properties: {
          name: { type: "string", title: "ATS Name" },
          apiUrl: { type: "string", title: "API Base URL", format: "uri" },
          version: { type: "string", title: "API Version" },
        },
      },
      connectionSettings: {
        type: "object",
        title: "Connection Settings",
        properties: {
          apiKey: { type: "string", title: "API Key", format: "password" },
          username: { type: "string", title: "Username" },
          password: { type: "string", title: "Password", format: "password" },
          tenantId: { type: "string", title: "Tenant ID" },
          environment: {
            type: "string",
            title: "Environment",
            enum: ["production", "sandbox", "staging"],
            default: "production",
          },
        },
      },
      syncDirection: {
        type: "string",
        title: "Sync Direction",
        description: "Direction of data synchronization",
        enum: ["bidirectional", "to_ats", "from_ats"],
        default: "bidirectional",
      },
      dataMapping: {
        type: "array",
        title: "Field Mapping",
        description: "Mapping between RMS and ATS fields",
        items: {
          type: "object",
          properties: {
            rmsField: { type: "string", title: "RMS Field" },
            atsField: { type: "string", title: "ATS Field" },
            transformation: {
              type: "string",
              title: "Transformation",
              enum: ["none", "uppercase", "lowercase", "date_format", "custom"],
              default: "none",
            },
            required: {
              type: "boolean",
              title: "Required Field",
              default: false,
            },
          },
          required: ["rmsField", "atsField"],
        },
      },
      syncFrequency: {
        type: "string",
        title: "Sync Frequency",
        description: "How often to sync data",
        enum: ["real_time", "every_15_min", "hourly", "daily", "manual"],
        default: "hourly",
      },
      conflictResolution: {
        type: "string",
        title: "Conflict Resolution",
        description: "How to handle data conflicts",
        enum: ["rms_wins", "ats_wins", "newest_wins", "manual_review"],
        default: "newest_wins",
      },
      errorHandling: {
        type: "object",
        title: "Error Handling",
        properties: {
          retryAttempts: {
            type: "number",
            title: "Retry Attempts",
            minimum: 0,
            maximum: 10,
            default: 3,
          },
          retryDelay: {
            type: "number",
            title: "Retry Delay (seconds)",
            minimum: 1,
            maximum: 3600,
            default: 60,
          },
          notifyOnError: {
            type: "boolean",
            title: "Notify on Error",
            description: "Send notifications when sync errors occur",
            default: true,
          },
          errorEmail: {
            type: "string",
            title: "Error Notification Email",
            format: "email",
          },
        },
      },
    },
    required: ["atsSystem", "connectionSettings", "syncDirection"],
  },

  "calendar-integration": {
    type: "object",
    title: "Calendar Integration",
    description: "Integrate with calendar systems for scheduling",
    properties: {
      calendarSystem: {
        type: "string",
        title: "Calendar System",
        description: "Target calendar system",
        enum: [
          "google_calendar",
          "outlook",
          "office365",
          "exchange",
          "caldav",
          "custom",
        ],
        default: "google_calendar",
      },
      authSettings: {
        type: "object",
        title: "Authentication Settings",
        properties: {
          clientId: { type: "string", title: "Client ID" },
          clientSecret: {
            type: "string",
            title: "Client Secret",
            format: "password",
          },
          redirectUri: { type: "string", title: "Redirect URI", format: "uri" },
          scopes: {
            type: "array",
            title: "Required Scopes",
            items: { type: "string" },
            default: ["calendar.read", "calendar.write"],
          },
        },
      },
      defaultCalendar: {
        type: "string",
        title: "Default Calendar",
        description: "Default calendar for creating events",
        placeholder: "primary or calendar ID",
      },
      meetingDefaults: {
        type: "object",
        title: "Meeting Defaults",
        properties: {
          duration: {
            type: "number",
            title: "Default Duration (minutes)",
            minimum: 15,
            maximum: 480,
            default: 60,
          },
          location: {
            type: "string",
            title: "Default Location",
            placeholder: "Conference Room A or Zoom link",
          },
          meetingType: {
            type: "string",
            title: "Default Meeting Type",
            enum: ["in_person", "video_call", "phone_call", "hybrid"],
            default: "video_call",
          },
          bufferTime: {
            type: "number",
            title: "Buffer Time (minutes)",
            description: "Buffer time between meetings",
            minimum: 0,
            maximum: 60,
            default: 15,
          },
        },
      },
      availabilitySettings: {
        type: "object",
        title: "Availability Settings",
        properties: {
          workingHours: {
            type: "object",
            title: "Working Hours",
            properties: {
              start: {
                type: "string",
                title: "Start Time",
                format: "time",
                default: "09:00",
              },
              end: {
                type: "string",
                title: "End Time",
                format: "time",
                default: "17:00",
              },
            },
          },
          workingDays: {
            type: "array",
            title: "Working Days",
            items: {
              type: "string",
              enum: [
                "monday",
                "tuesday",
                "wednesday",
                "thursday",
                "friday",
                "saturday",
                "sunday",
              ],
            },
            default: ["monday", "tuesday", "wednesday", "thursday", "friday"],
          },
          timezone: {
            type: "string",
            title: "Timezone",
            default: "America/New_York",
          },
          lookAheadDays: {
            type: "number",
            title: "Look Ahead Days",
            description: "Days to look ahead for availability",
            minimum: 1,
            maximum: 90,
            default: 30,
          },
        },
      },
      notificationSettings: {
        type: "object",
        title: "Notification Settings",
        properties: {
          sendInvites: {
            type: "boolean",
            title: "Send Calendar Invites",
            default: true,
          },
          reminderMinutes: {
            type: "array",
            title: "Reminder Times (minutes)",
            items: { type: "number" },
            default: [15, 60],
          },
          includeAgenda: {
            type: "boolean",
            title: "Include Agenda",
            description: "Include meeting agenda in invite",
            default: true,
          },
          includeJoinInfo: {
            type: "boolean",
            title: "Include Join Info",
            description: "Include video call join information",
            default: true,
          },
        },
      },
      syncSettings: {
        type: "object",
        title: "Sync Settings",
        properties: {
          syncFrequency: {
            type: "string",
            title: "Sync Frequency",
            enum: ["real_time", "every_5_min", "every_15_min", "hourly"],
            default: "every_15_min",
          },
          syncDirection: {
            type: "string",
            title: "Sync Direction",
            enum: ["bidirectional", "to_calendar", "from_calendar"],
            default: "bidirectional",
          },
          conflictResolution: {
            type: "string",
            title: "Conflict Resolution",
            enum: ["calendar_wins", "rms_wins", "manual_review"],
            default: "calendar_wins",
          },
        },
      },
    },
    required: ["calendarSystem", "authSettings"],
  },

  "human-approval": {
    type: "object",
    title: "Human Approval Gate",
    description: "Requires human approval before proceeding with workflow execution",
    properties: {
      approvalType: {
        type: "string",
        title: "Approval Type",
        enum: ["single", "multi", "consensus"],
        default: "single",
        description: "Single: any one approver, Multi: specified number, Consensus: all must approve",
      },
      approvers: {
        type: "array",
        title: "Approvers",
        items: { type: "string" },
        description: "List of user IDs or email addresses who can approve",
        minItems: 1,
      },
      requiredApprovals: {
        type: "number",
        title: "Required Approvals",
        description: "Number of approvals needed (for multi-approval type)",
        minimum: 1,
      },
      approvalMessage: {
        type: "string",
        title: "Approval Message",
        description: "Message shown to approvers explaining what needs approval",
        maxLength: 500,
      },
      priority: {
        type: "string",
        title: "Priority",
        enum: ["low", "medium", "high", "urgent"],
        default: "medium",
        description: "Priority level affects notification urgency and escalation",
      },
      timeoutHours: {
        type: "number",
        title: "Timeout (Hours)",
        description: "Auto-reject after this many hours",
        minimum: 1,
        maximum: 168, // 1 week
        default: 24,
      },
      escalationHours: {
        type: "number",
        title: "Escalation (Hours)",
        description: "Escalate if no response after this many hours",
        minimum: 1,
        maximum: 72,
      },
      escalationApprovers: {
        type: "array",
        title: "Escalation Approvers",
        items: { type: "string" },
        description: "Additional approvers to notify on escalation",
      },
      allowComments: {
        type: "boolean",
        title: "Allow Comments",
        description: "Allow approvers to add comments with their decision",
        default: true,
      },
    },
    required: ["approvers", "approvalMessage"],
  },
};
