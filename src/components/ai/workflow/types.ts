// Workflow template and node types for autonomous workflows

export interface WorkflowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    id?: string;
    type: string;
    label: string;
    category?: string;
    config?: Record<string, unknown>;
    originalId?: string;
  };
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type: string;
  sourceHandle?: string;
  label?: string;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  version: string;
  tags: string[];
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  metadata: {
    author: string;
    createdAt: string;
    estimatedExecutionTime: string;
    successRate: string;
    humanInterventionRate: string;
    features: string[];
    requirements: {
      aiProviders: string[];
      permissions: string[];
      integrations: string[];
    };
  };
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  currentNode?: string;
  progress: number;
  logs: ExecutionLog[];
  context: Record<string, any>;
}

export interface ExecutionLog {
  id: string;
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  nodeId?: string;
  data?: Record<string, any>;
}

export interface WorkflowMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  successRate: number;
  lastExecution?: Date;
}

export interface WorkflowAlert {
  id: string;
  workflowId: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  resolved: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}
