import React from "react";
import {
  Bell,
  Mail,
  Calendar,
  UserPlus,
  CheckCircle,
  XCircle,
  MessageSquare,
  Send,
  BrainCircuit,
  FileCheck,
  Users,
  Building2,
  GraduationCap,
  Briefcase,
  Timer,
  AlertCircle,
  FileText,
  Webhook,
  Database,
  Zap,
  Workflow,
  Filter,
  ArrowRightLeft,
  ListFilter,
  Layers,
  Repeat,
  Clock,
  CalendarClock,
  Megaphone,
  Pencil,
  Tag,
  Star,
  Trash2,
  Archive,
  Inbox,
  Folder,
  FolderPlus,
  FolderX,
  FolderInput,
  FolderOutput,
  FolderOpen,
  FolderCheck,
  FolderClock,
  FolderEdit,
  FolderSearch,
  FolderTree,
  FolderUp,
  FolderDown,
  FolderMinus,
  FolderPlus as FolderPlus2,
  FolderX as FolderX2,
  FolderInput as FolderInput2,
  FolderOutput as FolderOutput2,
  FolderOpen as FolderOpen2,
  FolderCheck as FolderCheck2,
  FolderClock as FolderClock2,
  FolderEdit as FolderEdit2,
  FolderSearch2,
  FolderTree as <PERSON>older<PERSON>ree2,
  FolderUp as FolderUp2,
  FolderDown as FolderDown2,
  FolderMinus as FolderMinus2,
  UserCheck,
} from "lucide-react";

// Helper function to create icon elements consistently
const createIcon = (Icon: React.ComponentType<any>) => (
  <Icon className="w-4 h-4" />
);

export const workflowNodes = [
  // Triggers
  {
    id: "new-application",
    category: "triggers",
    type: "trigger",
    label: "New Application",
    description: "Triggers when a new candidate applies",
    icon: createIcon(UserPlus),
  },
  {
    id: "application-status",
    category: "triggers",
    type: "trigger",
    label: "Status Change",
    description: "Triggers when application status changes",
    icon: createIcon(AlertCircle),
  },
  {
    id: "scheduled-trigger",
    category: "triggers",
    type: "trigger",
    label: "Schedule",
    description: "Triggers at scheduled times",
    icon: createIcon(Timer),
  },
  {
    id: "document-upload",
    category: "triggers",
    type: "trigger",
    label: "Document Upload",
    description: "Triggers when a document is uploaded",
    icon: createIcon(FileText),
  },
  {
    id: "webhook-trigger",
    category: "triggers",
    type: "trigger",
    label: "Webhook",
    description: "Triggers from external webhook calls",
    icon: createIcon(Webhook),
  },
  {
    id: "database-trigger",
    category: "triggers",
    type: "trigger",
    label: "Database Change",
    description: "Triggers on database record changes",
    icon: createIcon(Database),
  },

  // Actions
  {
    id: "send-email",
    category: "actions",
    type: "action",
    label: "Send Email",
    description: "Send an automated email",
    icon: createIcon(Mail),
  },
  {
    id: "schedule-interview",
    category: "actions",
    type: "action",
    label: "Schedule Interview",
    description: "Automatically schedule interviews",
    icon: createIcon(Calendar),
  },
  {
    id: "ai-screen",
    category: "actions",
    type: "action",
    label: "AI Screening",
    description: "Screen resume with AI",
    icon: createIcon(BrainCircuit),
  },
  {
    id: "send-assessment",
    category: "actions",
    type: "action",
    label: "Send Assessment",
    description: "Send skills assessment test",
    icon: createIcon(FileCheck),
  },
  {
    id: "notify-team",
    category: "actions",
    type: "action",
    label: "Notify Team",
    description: "Send notification to team",
    icon: createIcon(Bell),
  },
  {
    id: "data-enrichment",
    category: "actions",
    type: "action",
    label: "Data Enrichment",
    description: "Enrich candidate data from external sources",
    icon: createIcon(Zap),
  },
  {
    id: "create-task",
    category: "actions",
    type: "action",
    label: "Create Task",
    description: "Create a task for team members",
    icon: createIcon(Pencil),
  },
  {
    id: "add-tag",
    category: "actions",
    type: "action",
    label: "Add Tag",
    description: "Add tags to a candidate",
    icon: createIcon(Tag),
  },
  {
    id: "score-candidate",
    category: "actions",
    type: "action",
    label: "Score Candidate",
    description: "Assign a score to a candidate",
    icon: createIcon(Star),
  },
  {
    id: "human-approval",
    category: "actions",
    type: "action",
    label: "Human Approval",
    description: "Requires human approval before proceeding",
    icon: createIcon(UserCheck),
  },

  // Conditions
  {
    id: "experience-check",
    category: "conditions",
    type: "condition",
    label: "Experience Check",
    description: "Check years of experience",
    icon: createIcon(Briefcase),
  },
  {
    id: "education-check",
    category: "conditions",
    type: "condition",
    label: "Education Check",
    description: "Verify education requirements",
    icon: createIcon(GraduationCap),
  },
  {
    id: "skills-match",
    category: "conditions",
    type: "condition",
    label: "Skills Match",
    description: "Check required skills",
    icon: createIcon(CheckCircle),
  },
  {
    id: "location-check",
    category: "conditions",
    type: "condition",
    label: "Location Check",
    description: "Verify location requirements",
    icon: createIcon(Building2),
  },
  {
    id: "salary-check",
    category: "conditions",
    type: "condition",
    label: "Salary Check",
    description: "Check salary requirements",
    icon: createIcon(Briefcase),
  },
  {
    id: "availability-check",
    category: "conditions",
    type: "condition",
    label: "Availability Check",
    description: "Check candidate availability",
    icon: createIcon(Calendar),
  },
  {
    id: "visa-check",
    category: "conditions",
    type: "condition",
    label: "Visa Check",
    description: "Check visa/work authorization status",
    icon: createIcon(FileCheck),
  },
  {
    id: "filter-condition",
    category: "conditions",
    type: "condition",
    label: "Filter Condition",
    description: "Filter based on custom criteria",
    icon: createIcon(Filter),
  },
  {
    id: "data-comparison",
    category: "conditions",
    type: "condition",
    label: "Data Comparison",
    description: "Compare data values",
    icon: createIcon(ArrowRightLeft),
  },

  // Outputs
  {
    id: "update-status",
    category: "outputs",
    type: "output",
    label: "Update Status",
    description: "Update application status",
    icon: createIcon(FileCheck),
  },
  {
    id: "add-to-pool",
    category: "outputs",
    type: "output",
    label: "Add to Talent Pool",
    description: "Add to specific talent pool",
    icon: createIcon(Users),
  },
  {
    id: "send-message",
    category: "outputs",
    type: "output",
    label: "Send Message",
    description: "Send message to candidate",
    icon: createIcon(MessageSquare),
  },
  {
    id: "export-data",
    category: "outputs",
    type: "output",
    label: "Export Data",
    description: "Export data to external system",
    icon: createIcon(FolderOutput),
  },
  {
    id: "archive-candidate",
    category: "outputs",
    type: "output",
    label: "Archive Candidate",
    description: "Archive a candidate record",
    icon: createIcon(Archive),
  },
  {
    id: "generate-report",
    category: "outputs",
    type: "output",
    label: "Generate Report",
    description: "Generate a report from workflow data",
    icon: createIcon(FileText),
  },

  // Transformations
  {
    id: "data-transform",
    category: "transformations",
    type: "transformation",
    label: "Data Transform",
    description: "Transform data between steps",
    icon: createIcon(Workflow),
  },
  {
    id: "data-filter",
    category: "transformations",
    type: "transformation",
    label: "Data Filter",
    description: "Filter data between steps",
    icon: createIcon(ListFilter),
  },
  {
    id: "data-merge",
    category: "transformations",
    type: "transformation",
    label: "Data Merge",
    description: "Merge data from multiple sources",
    icon: createIcon(Layers),
  },
  {
    id: "data-loop",
    category: "transformations",
    type: "transformation",
    label: "Loop",
    description: "Loop through a collection of items",
    icon: createIcon(Repeat),
  },
  {
    id: "delay",
    category: "transformations",
    type: "transformation",
    label: "Delay",
    description: "Add a delay between steps",
    icon: createIcon(Clock),
  },
  {
    id: "schedule",
    category: "transformations",
    type: "transformation",
    label: "Schedule",
    description: "Schedule a future action",
    icon: createIcon(CalendarClock),
  },

  // Integrations
  {
    id: "linkedin-integration",
    category: "integrations",
    type: "integration",
    label: "LinkedIn",
    description: "Integrate with LinkedIn",
    icon: createIcon(Megaphone),
  },
  {
    id: "job-board-integration",
    category: "integrations",
    type: "integration",
    label: "Job Boards",
    description: "Post to external job boards",
    icon: createIcon(Megaphone),
  },
  {
    id: "ats-integration",
    category: "integrations",
    type: "integration",
    label: "ATS Integration",
    description: "Integrate with external ATS",
    icon: createIcon(Database),
  },
  {
    id: "calendar-integration",
    category: "integrations",
    type: "integration",
    label: "Calendar",
    description: "Integrate with calendar systems",
    icon: createIcon(Calendar),
  },
];
