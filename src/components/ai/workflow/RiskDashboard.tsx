import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Shield, 
  AlertTriangle, 
  TrendingUp, 
  Activity,
  Brain,
  Users,
  Database,
  Lock,
  Zap,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Settings,
  BarChart3
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { RiskAssessmentEngine, RiskAssessment } from "@/services/risk/RiskAssessmentEngine";

interface RiskMetrics {
  totalAssessments: number;
  criticalRisk: number;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
  escalationsTriggered: number;
  averageRiskScore: number;
  averageConfidence: number;
}

export function RiskDashboard() {
  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics>({
    totalAssessments: 1247,
    criticalRisk: 23,
    highRisk: 156,
    mediumRisk: 487,
    lowRisk: 581,
    escalationsTriggered: 89,
    averageRiskScore: 0.34,
    averageConfidence: 0.87,
  });
  const [recentAssessments, setRecentAssessments] = useState<RiskAssessment[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const { toast } = useToast();
  
  const tabs = [
    { value: "overview", label: "Overview", icon: Shield },
    { value: "assessments", label: "Risk Assessments", icon: AlertTriangle },
    { value: "escalations", label: "Escalations", icon: TrendingUp },
    { value: "factors", label: "Risk Factors", icon: BarChart3 },
  ];

  const riskEngine = RiskAssessmentEngine.getInstance();

  useEffect(() => {
    loadRiskData();
  }, []);

  const loadRiskData = async () => {
    try {
      setLoading(true);
      // In a real implementation, this would fetch from the database
      // For now, we'll simulate some recent assessments
      const mockAssessments: RiskAssessment[] = [
        {
          id: "risk-001",
          entityType: "candidate",
          entityId: "candidate-123",
          workflowId: "workflow-456",
          executionId: "exec-789",
          nodeId: "ai-screen-1",
          overallRisk: "medium",
          riskScore: 0.45,
          confidence: 0.89,
          factors: [
            { factorId: "data_completeness", score: 0.2, weight: 0.3, contribution: 0.06 },
            { factorId: "ai_confidence", score: 0.3, weight: 0.4, contribution: 0.12 },
            { factorId: "sensitive_data", score: 0.8, weight: 0.5, contribution: 0.4 },
          ],
          recommendations: [
            { type: "monitor", reason: "Medium risk with sensitive data", priority: "medium" },
          ],
          escalationTriggers: [],
          metadata: {
            assessedAt: new Date(Date.now() - 1000 * 60 * 15),
            assessedBy: "RiskAssessmentEngine",
            version: "1.0",
            processingTime: 234,
          },
        },
        {
          id: "risk-002",
          entityType: "candidate",
          entityId: "candidate-456",
          workflowId: "workflow-789",
          executionId: "exec-012",
          nodeId: "ai-screen-2",
          overallRisk: "high",
          riskScore: 0.72,
          confidence: 0.76,
          factors: [
            { factorId: "data_completeness", score: 0.6, weight: 0.3, contribution: 0.18 },
            { factorId: "ai_confidence", score: 0.8, weight: 0.4, contribution: 0.32 },
            { factorId: "sensitive_data", score: 0.9, weight: 0.5, contribution: 0.45 },
          ],
          recommendations: [
            { type: "escalate", reason: "High risk with low AI confidence", priority: "high" },
          ],
          escalationTriggers: [
            { condition: "overallRisk === 'high'", threshold: 0.7, action: "human_review" },
          ],
          metadata: {
            assessedAt: new Date(Date.now() - 1000 * 60 * 5),
            assessedBy: "RiskAssessmentEngine",
            version: "1.0",
            processingTime: 187,
          },
        },
      ];
      
      setRecentAssessments(mockAssessments);
    } catch (error) {
      console.error('Error loading risk data:', error);
      toast({
        title: "Error",
        description: "Failed to load risk assessment data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const runRiskAssessment = async () => {
    try {
      toast({
        title: "Running Assessment",
        description: "Performing comprehensive risk assessment...",
      });

      // Simulate a risk assessment
      const mockContext = {
        entityType: "candidate",
        entityId: "test-candidate-001",
        workflowId: "test-workflow-001",
        candidate: {
          name: "John Doe",
          email: "<EMAIL>",
          resume: "Software Engineer with 5 years experience...",
          experience: 5,
          ssn: "***********", // Sensitive data
        },
        aiResult: {
          confidence: 75,
          score: 85,
        },
        workflow: {
          nodes: Array(15).fill({}),
          parallelPaths: 2,
        },
      };

      const assessment = await riskEngine.assessRisk(mockContext);
      
      toast({
        title: "Assessment Complete",
        description: `Risk level: ${assessment.overallRisk.toUpperCase()} (Score: ${(assessment.riskScore * 100).toFixed(1)}%)`,
      });

      // Add to recent assessments
      setRecentAssessments(prev => [assessment, ...prev.slice(0, 9)]);
      
    } catch (error) {
      toast({
        title: "Assessment Failed",
        description: "Failed to complete risk assessment",
        variant: "destructive",
      });
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'default';
      default: return 'outline';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'critical': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'medium': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'low': return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'escalate': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'review': return <Eye className="h-4 w-4 text-yellow-500" />;
      case 'monitor': return <Activity className="h-4 w-4 text-blue-500" />;
      case 'approve': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'reject': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6 text-red-600" />
            Risk Assessment & Escalation
          </h2>
          <p className="text-muted-foreground">
            Real-time risk monitoring with intelligent escalation and safety mechanisms
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure Rules
          </Button>
          <Button size="sm" onClick={runRiskAssessment}>
            <Zap className="h-4 w-4 mr-2" />
            Run Assessment
          </Button>
        </div>
      </div>

      <EnhancedTabs
        tabs={tabs}
        value={activeTab}
        onValueChange={setActiveTab}
        variant="navigation"
        indicatorStyle="underline"
        size="md"
      >
        <TabPanel value="overview" className="space-y-4">
          {/* Risk Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Assessments</p>
                    <p className="text-2xl font-bold">{riskMetrics.totalAssessments.toLocaleString()}</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Critical Risk</p>
                    <p className="text-2xl font-bold text-red-600">{riskMetrics.criticalRisk}</p>
                  </div>
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Escalations</p>
                    <p className="text-2xl font-bold text-orange-600">{riskMetrics.escalationsTriggered}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Avg Risk Score</p>
                    <p className="text-2xl font-bold">{(riskMetrics.averageRiskScore * 100).toFixed(1)}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Risk Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Risk Distribution</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Low Risk</span>
                  </div>
                  <span className="font-medium">{riskMetrics.lowRisk}</span>
                </div>
                <Progress value={(riskMetrics.lowRisk / riskMetrics.totalAssessments) * 100} className="h-2" />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span>Medium Risk</span>
                  </div>
                  <span className="font-medium">{riskMetrics.mediumRisk}</span>
                </div>
                <Progress value={(riskMetrics.mediumRisk / riskMetrics.totalAssessments) * 100} className="h-2" />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <span>High Risk</span>
                  </div>
                  <span className="font-medium">{riskMetrics.highRisk}</span>
                </div>
                <Progress value={(riskMetrics.highRisk / riskMetrics.totalAssessments) * 100} className="h-2" />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span>Critical Risk</span>
                  </div>
                  <span className="font-medium">{riskMetrics.criticalRisk}</span>
                </div>
                <Progress value={(riskMetrics.criticalRisk / riskMetrics.totalAssessments) * 100} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Risk assessments are performed automatically for all autonomous operations. 
              Average confidence: {(riskMetrics.averageConfidence * 100).toFixed(1)}% | 
              Escalation rate: {((riskMetrics.escalationsTriggered / riskMetrics.totalAssessments) * 100).toFixed(1)}%
            </AlertDescription>
          </Alert>
        </TabPanel>

        <TabPanel value="assessments" className="space-y-4">
          <div className="space-y-3">
            {loading ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p>Loading risk assessments...</p>
                </CardContent>
              </Card>
            ) : recentAssessments.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No recent assessments</h3>
                  <p className="text-muted-foreground mb-4">
                    Run a risk assessment to see results here.
                  </p>
                  <Button onClick={runRiskAssessment}>
                    <Zap className="h-4 w-4 mr-2" />
                    Run Assessment
                  </Button>
                </CardContent>
              </Card>
            ) : (
              recentAssessments.map((assessment) => (
                <Card key={assessment.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          {getRiskIcon(assessment.overallRisk)}
                          <span className="font-medium">Risk Assessment</span>
                          <Badge variant={getRiskColor(assessment.overallRisk)}>
                            {assessment.overallRisk.toUpperCase()}
                          </Badge>
                          <Badge variant="outline">
                            {(assessment.riskScore * 100).toFixed(1)}% risk
                          </Badge>
                          <Badge variant="outline">
                            {(assessment.confidence * 100).toFixed(1)}% confidence
                          </Badge>
                        </div>
                        
                        <div className="text-sm text-muted-foreground">
                          <span className="font-medium">Entity:</span> {assessment.entityId} | 
                          <span className="font-medium"> Workflow:</span> {assessment.workflowId} |
                          <span className="font-medium"> Processing:</span> {assessment.metadata.processingTime}ms
                        </div>
                      </div>
                      
                      <div className="text-right text-sm text-muted-foreground">
                        <div>{new Date(assessment.metadata.assessedAt).toLocaleString()}</div>
                      </div>
                    </div>

                    {/* Risk Factors */}
                    <div className="space-y-2 mb-4">
                      <h4 className="font-medium text-sm">Risk Factors</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        {assessment.factors.map((factor, index) => (
                          <div key={index} className="bg-muted p-2 rounded text-xs">
                            <div className="flex justify-between">
                              <span>{factor.factorId.replace(/_/g, ' ')}</span>
                              <span>{(factor.score * 100).toFixed(0)}%</span>
                            </div>
                            <Progress value={factor.score * 100} className="h-1 mt-1" />
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Recommendations */}
                    {assessment.recommendations.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Recommendations</h4>
                        <div className="space-y-1">
                          {assessment.recommendations.map((rec, index) => (
                            <div key={index} className="flex items-center gap-2 text-sm">
                              {getRecommendationIcon(rec.type)}
                              <span className="font-medium">{rec.type.toUpperCase()}:</span>
                              <span>{rec.reason}</span>
                              <Badge variant="outline" className="text-xs">
                                {rec.priority}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Escalation Triggers */}
                    {assessment.escalationTriggers.length > 0 && (
                      <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
                        <div className="flex items-center gap-2 text-sm text-red-700">
                          <AlertTriangle className="h-4 w-4" />
                          <span className="font-medium">
                            {assessment.escalationTriggers.length} escalation trigger(s) activated
                          </span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabPanel>

        <TabPanel value="escalations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Escalation Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Escalation Monitoring</h3>
                <p className="text-muted-foreground mb-4">
                  Real-time monitoring of escalation triggers and automated safety responses.
                </p>
                <Button>
                  <Activity className="h-4 w-4 mr-2" />
                  View Active Escalations
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="factors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Risk Factor Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Risk Factor Management</h3>
                <p className="text-muted-foreground mb-4">
                  Configure and manage risk factors, weights, and evaluation criteria.
                </p>
                <Button>
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Risk Factors
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabPanel>
      </EnhancedTabs>
    </div>
  );
}
