import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Mail, Send, Users, Inbox } from "lucide-react";
import { EmailComposer } from "./EmailComposer";
import { EmailTemplateManager } from "./EmailTemplateManager";
import { ContactManagement } from "./ContactManagement";

import { useMessages } from "@/hooks/useMessages";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";

export function MessageCenter() {
  const [activeTab, setActiveTab] = useState("compose");
  const { data: messages = [], isLoading: loading, error } = useMessages();
  const { data: templates = [] } = useMessageTemplates();

  const unreadCount = messages.filter((m) => m.status === "unread").length;
  const templateCount = templates.length;
  
  const tabs = [
    { value: "compose", label: "Compose", icon: Send },
    { 
      value: "templates", 
      label: "Templates", 
      icon: Mail,
      badge: templateCount > 0 ? templateCount : undefined
    },
    { 
      value: "inbox", 
      label: "Inbox", 
      icon: Inbox,
      badge: unreadCount > 0 ? { value: unreadCount, variant: "destructive" as const } : undefined
    },
    { value: "contacts", label: "Contacts", icon: Users },
  ];

  return (
    <div className="space-y-4 sm:space-y-6 min-w-0">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="min-w-0">
          <h2 className="text-xl sm:text-2xl font-bold tracking-tight">
            Message Center
          </h2>
          <p className="text-sm sm:text-base text-muted-foreground">
            Manage your communication with candidates and team members
          </p>
        </div>
        <div className="flex items-center gap-2 sm:gap-4">
          <div className="flex items-center gap-2 min-w-0">
            <MessageSquare className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="text-xs sm:text-sm text-muted-foreground truncate">
              {messages.length} total messages
            </span>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="flex-shrink-0">
                {unreadCount} unread
              </Badge>
            )}
          </div>
        </div>
      </div>

      <EnhancedTabs
        tabs={tabs}
        value={activeTab}
        onValueChange={setActiveTab}
        variant="navigation"
        indicatorStyle="underline"
        size="md"
      >
        <TabPanel value="compose" className="space-y-4 sm:space-y-6">
          <EmailComposer />
        </TabPanel>

        <TabPanel value="templates" className="space-y-4 sm:space-y-6">
          <EmailTemplateManager />
        </TabPanel>

        <TabPanel value="inbox" className="space-y-4 sm:space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Message Inbox
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center text-muted-foreground py-8">
                  Loading messages...
                </div>
              ) : messages.length === 0 ? (
                <div className="text-center py-6 sm:py-8">
                  <MessageSquare className="h-10 w-10 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
                  <h3 className="text-base sm:text-lg font-medium mb-2">
                    No Messages
                  </h3>
                  <p className="text-sm sm:text-base text-muted-foreground">
                    Your message inbox is currently empty.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {messages.slice(0, 10).map((message) => (
                    <div
                      key={message.id}
                      className={`p-3 sm:p-4 border rounded-lg ${
                        message.status === "unread"
                          ? "bg-blue-50 border-blue-200"
                          : ""
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2 gap-2">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1 flex-wrap">
                            <h4 className="font-medium text-sm sm:text-base">
                              {message.sender_name}
                            </h4>
                            <span className="text-xs sm:text-sm text-muted-foreground truncate">
                              {message.sender_email}
                            </span>
                            {message.status === "unread" && (
                              <Badge variant="destructive" className="text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">
                            {message.content.substring(0, 120)}...
                          </p>
                        </div>
                        <span className="text-xs text-muted-foreground flex-shrink-0">
                          {new Date(message.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value="contacts" className="space-y-4 sm:space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <Users className="h-4 w-4 sm:h-5 sm:w-5" />
                Contact Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ContactManagement />
            </CardContent>
          </Card>
        </TabPanel>
      </EnhancedTabs>
    </div>
  );
}
