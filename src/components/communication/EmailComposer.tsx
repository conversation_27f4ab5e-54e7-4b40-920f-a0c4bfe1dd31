import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Mail, Send, FileText, Sparkles, Loader2, Info, Brain, Heart, AlertCircle, CheckCircle, TrendingUp } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCreateMessage } from "@/hooks/useCreateMessage";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";
import { useCommunicationAgent, useSentimentAnalysis } from "@/hooks/useCommunicationAgent";
import { CandidateType } from "@/types/candidate";
import { generateText } from "@/utils/gemini";

interface EmailComposerProps {
  candidate?: CandidateType;
  onSuccess?: () => void;
}

export function EmailComposer({ candidate, onSuccess }: EmailComposerProps) {
  const [to, setTo] = useState(candidate?.email || "");
  const [cc, setCc] = useState("");
  const [subject, setSubject] = useState("");
  const [content, setContent] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [showAIFeatures, setShowAIFeatures] = useState(false);

  const { toast } = useToast();
  const createMessage = useCreateMessage();
  const { data: templates = [] } = useMessageTemplates();

  // Enhanced AI features
  const {
    generateEmail,
    analyzeSentiment,
    isProcessing: isAIGenerating,
    lastResult,
    error: aiError,
  } = useCommunicationAgent({
    candidateId: candidate?.id,
  });

  const {
    formatEmotionAnalysis,
  } = useSentimentAnalysis();

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId);
    if (template) {
      setSubject(template.subject);

      // Replace common placeholders with actual values
      let processedContent = template.content;

      // Replace candidate-specific placeholders
      if (candidate?.name) {
        processedContent = processedContent.replace(
          /\[Name\]/g,
          candidate.name,
        );
        processedContent = processedContent.replace(
          /\{candidateName\}/g,
          candidate.name,
        );
      }

      // Replace common company placeholders with defaults
      processedContent = processedContent.replace(/\[Company\]/g, "HireLogix");
      processedContent = processedContent.replace(
        /\[Your Name\]/g,
        "Hiring Team",
      );

      // Leave position-specific placeholders for manual editing
      // [Position], [Salary], [Benefits] will remain as placeholders

      setContent(processedContent);

      // Show a toast to inform user about placeholder replacement
      toast({
        title: "Template Applied",
        description:
          "Template has been applied. Please review and customize the placeholders as needed.",
      });
    }
  };

  const generateAIContent = async () => {
    if (!candidate) {
      toast({
        title: "Missing Information",
        description: "Please select a candidate to generate AI content.",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingAI(true);
    try {
      // Use enhanced CommunicationAgent if available, fallback to basic generation
      if (generateEmail) {
        const result = await generateEmail({
          content: `Generate a professional recruitment email for ${candidate.name} who is a ${candidate.role}. Include their experience: ${candidate.experience || "Not specified"} and skills: ${candidate.skills?.map((s: any) => (typeof s === "string" ? s : s.name)).join(", ") || "Not specified"}. Make it personalized and engaging.`,
          recipient: candidate,
          customInstructions: 'Create a personalized recruitment email that shows genuine interest in the candidate\'s background and invites them for next steps.',
        });

        if (result.success && result.content) {
          // Parse the generated content to extract subject and body
          const lines = result.content.split('\n');
          const subjectLine = lines
            .find((line) => line.toLowerCase().startsWith('subject:'))
            ?.replace(/subject:/i, '')
            .trim();
          const bodyStart = lines.findIndex((line) => line.toLowerCase().startsWith('subject:')) + 1;
          const bodyContent = lines.slice(bodyStart + 1).join('\n').trim();

          if (subjectLine) setSubject(subjectLine);
          if (bodyContent) setContent(bodyContent);

          toast({
            title: "Enhanced AI Content Generated",
            description: `Personalized email generated using advanced AI communication agent.`,
          });
        } else {
          throw new Error('Failed to generate email content');
        }
      } else {
        // Fallback to basic generation
        const prompt = `
          Write a professional recruitment email for a candidate named ${candidate.name} who is a ${candidate.role}.

          Context:
          - Candidate: ${candidate.name}
          - Position: ${candidate.role}
          - Experience: ${candidate.experience || "Not specified"}
          - Skills: ${candidate.skills?.map((s: any) => (typeof s === "string" ? s : s.name)).join(", ") || "Not specified"}

          Write a personalized, professional email that:
          1. Shows genuine interest in their background
          2. Mentions specific skills or experience that caught your attention
          3. Invites them for next steps (interview or call)
          4. Maintains a professional but friendly tone

          Format the response as:
          Subject: [Email subject line]

          [Email body content]
        `;

        const response = await generateText(
          prompt,
          "You are a professional recruiter. Write personalized, engaging recruitment emails that show genuine interest in candidates.",
        );

        const lines = response.split("\n");
        const subjectLine = lines
          .find((line) => line.startsWith("Subject:"))
          ?.replace("Subject:", "")
          .trim();
        const bodyStart =
          lines.findIndex((line) => line.startsWith("Subject:")) + 1;
        const bodyContent = lines
          .slice(bodyStart + 1)
          .join("\n")
          .trim();

        if (subjectLine) setSubject(subjectLine);
        if (bodyContent) setContent(bodyContent);

        toast({
          title: "AI Content Generated",
          description: "Personalized email content has been generated for this candidate.",
        });
      }
    } catch (error) {
      console.error("Error generating AI content:", error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate AI content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // Enhanced sentiment analysis function
  const handleSentimentAnalysis = async () => {
    if (!content.trim()) {
      toast({
        title: "No Content",
        description: "Please write some content to analyze sentiment.",
        variant: "destructive",
      });
      return;
    }

    try {
      const analysis = await analyzeSentiment(content);
      if (analysis) {
        toast({
          title: "Sentiment Analysis Complete",
          description: "Your message has been analyzed for tone and sentiment.",
        });
      } else {
        throw new Error("Analysis returned no results");
      }
    } catch (error) {
      toast({
        title: "Analysis Failed",
        description: "Failed to analyze sentiment. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Get sentiment analysis from last result
  const sentimentAnalysis = lastResult?.sentimentAnalysis;

  const handleSend = async () => {
    if (!to || !subject || !content) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      await createMessage.mutateAsync({
        sender_name: "Your Name", // This would come from user profile
        sender_email: "<EMAIL>", // This would come from user profile
        content: `Subject: ${subject}\n\nTo: ${to}\n${cc ? `CC: ${cc}\n` : ""}\n\n${content}`,
        status: "read",
      });

      toast({
        title: "Email Sent",
        description: `Email sent successfully to ${to}.`,
      });

      // Reset form
      setTo(candidate?.email || "");
      setCc("");
      setSubject("");
      setContent("");
      setSelectedTemplate("");

      onSuccess?.();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send email. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Compose Email
            {candidate && (
              <span className="text-muted-foreground">to {candidate.name}</span>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAIFeatures(!showAIFeatures)}
            className="flex items-center gap-2"
          >
            <Brain className="h-4 w-4" />
            {showAIFeatures ? 'Hide' : 'Show'} AI Features
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Select
            value={selectedTemplate}
            onValueChange={(value) => {
              setSelectedTemplate(value);
              handleTemplateSelect(value);
            }}
          >
            <SelectTrigger className="w-auto">
              <FileText className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Use Template" />
            </SelectTrigger>
            <SelectContent>
              {templates.map((template) => (
                <SelectItem key={template.id} value={template.id}>
                  {template.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {candidate && (
            <Button
              variant="outline"
              onClick={generateAIContent}
              disabled={isGeneratingAI}
            >
              {isGeneratingAI ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  AI Generate
                </>
              )}
            </Button>
          )}
        </div>

        {/* Enhanced AI Features Section */}
        {showAIFeatures && (
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Brain className="h-4 w-4 text-blue-600" />
                AI-Powered Communication Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Sentiment Analysis */}
              {sentimentAnalysis && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Heart className="h-4 w-4 text-pink-500" />
                    Sentiment Analysis
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Overall Sentiment</span>
                        <Badge variant={sentimentAnalysis.sentiment === 'positive' ? 'default' : sentimentAnalysis.sentiment === 'negative' ? 'destructive' : 'secondary'}>
                          {sentimentAnalysis.sentiment}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Confidence</span>
                        <span className="text-xs font-medium">{Math.round(sentimentAnalysis.confidence * 100)}%</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Urgency</span>
                        <Badge variant={sentimentAnalysis.recommendedResponse === 'immediate' ? 'destructive' : sentimentAnalysis.recommendedResponse === 'escalate' ? 'secondary' : 'outline'}>
                          {sentimentAnalysis.recommendedResponse}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Tone Score</span>
                        <span className="text-xs font-medium">{Math.round(sentimentAnalysis.confidence * 100)}%</span>
                      </div>
                    </div>
                  </div>

                  {sentimentAnalysis.emotions && Object.keys(sentimentAnalysis.emotions).length > 0 && (
                    <div>
                      <span className="text-xs text-muted-foreground">Detected Emotions:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {formatEmotionAnalysis(sentimentAnalysis.emotions).map((emotion, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {emotion.emotion} ({Math.round(emotion.intensity * 100)}%)
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {sentimentAnalysis.concerns && sentimentAnalysis.concerns.length > 0 && (
                    <div>
                      <span className="text-xs text-muted-foreground">AI Suggestions:</span>
                      <ul className="text-xs space-y-1 mt-1">
                        {sentimentAnalysis.concerns.slice(0, 3).map((concern, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                            Address: {concern}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* AI Actions */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSentimentAnalysis}
                  disabled={isAIGenerating || !content.trim()}
                  className="flex items-center gap-2"
                >
                  {isAIGenerating ? (
                    <>
                      <Loader2 className="h-3 w-3 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <TrendingUp className="h-3 w-3" />
                      Analyze Sentiment
                    </>
                  )}
                </Button>
              </div>

              {/* AI Error Display */}
              {aiError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{aiError}</AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        )}

        <div className="space-y-2">
          <Label htmlFor="to">To *</Label>
          <Input
            id="to"
            type="email"
            placeholder="<EMAIL>"
            value={to}
            onChange={(e) => setTo(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="cc">CC</Label>
          <Input
            id="cc"
            type="email"
            placeholder="<EMAIL>"
            value={cc}
            onChange={(e) => setCc(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="subject">Subject *</Label>
          <Input
            id="subject"
            placeholder="Email subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="content">Message *</Label>
          <Textarea
            id="content"
            placeholder="Type your message here..."
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={8}
          />
          {selectedTemplate && (
            <div className="flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="text-blue-900 font-medium mb-1">
                  Available placeholders:
                </p>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary" className="text-xs">
                    [Position]
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    [Salary]
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    [Benefits]
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    [Date]
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    [Time]
                  </Badge>
                </div>
                <p className="text-blue-700 text-xs mt-1">
                  Replace these placeholders with specific information for your
                  message.
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleSend}
            disabled={createMessage.isPending || !to || !subject || !content}
            className="flex-1"
          >
            {createMessage.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Send Email
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
