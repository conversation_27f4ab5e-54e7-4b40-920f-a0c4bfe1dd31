import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Bell, Search, Settings, User } from "lucide-react";
import { UserMenu } from "./UserMenu";
import { NotificationCenter } from "@/components/notifications/NotificationCenter";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { ThemeToggle } from "@/components/ui/theme-toggle";

export function AppHeader() {
  const isMobile = useIsMobile();

  return (
    <header className="glass-md glass-inset border-b sticky top-0 z-50">
      <div className="flex h-14 items-center justify-between px-4">
        <div className="flex items-center gap-4">
          {isMobile && <SidebarTrigger />}
          <h1 className="text-lg font-semibold">HireLogix</h1>
        </div>
        <div className="flex items-center gap-4">
          <ThemeToggle />
          <NotificationCenter />
          <UserMenu />
        </div>
      </div>
    </header>
  );
}
