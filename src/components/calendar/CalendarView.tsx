import React, { useContext } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Plus,
  Calendar as CalendarIcon,
  Filter,
  Settings2,
  Globe2,
  LayoutGrid,
  LayoutList,
  Grid,
  View,
  ChevronLeft,
  ChevronRight,
  BarChart3,
  Zap,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCreateEvent, useEvents } from "@/hooks/useEvents";

import { EventManager } from "@/components/calendar/EventManager";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LocalEventsContext } from "@/components/layout/AppLayout";
import { UpcomingEvents } from "@/components/calendar/UpcomingEvents";

export const CalendarView = ({
  onEventsChange,
  onDateSelect,
}: {
  onEventsChange?: (events: any[]) => void;
  onDateSelect?: (date: Date | null) => void;
}) => {
  const [currentDate, setCurrentDate] = React.useState(new Date());
  const [selectedDate, setSelectedDate] = React.useState<Date | null>(null);
  const [selectedCategory, setSelectedCategory] = React.useState("all");
  const [selectedTimezone, setSelectedTimezone] = React.useState("UTC");
  const [viewMode, setViewMode] = React.useState("month");
  const [isEventDialogOpen, setIsEventDialogOpen] = React.useState(false);
  const [selectedEvent, setSelectedEvent] = React.useState<any>(null);
  const [eventDialogMode, setEventDialogMode] = React.useState<
    "create" | "edit"
  >("create");
  const { toast } = useToast();
  const { localEvents, setLocalEvents } = useContext(LocalEventsContext);

  // Events with internal realtime subscription
  const { data: events = [], isLoading: loading, error } = useEvents();

  // Keep shared localEvents in sync with server events (merge/deduplicate)
  React.useEffect(() => {
    setLocalEvents((prev) => {
      const serverIds = new Set(events.map((e) => e.id));
      const filtered = prev.filter((e) => !e.id || !serverIds.has(e.id));
      return [...events, ...filtered];
    });
  }, [events, setLocalEvents]);

  React.useEffect(() => {
    if (onEventsChange) onEventsChange(localEvents);
  }, [localEvents, onEventsChange]);

  const createEvent = useCreateEvent();

  const handleCreateEvent = () => {
    setEventDialogMode("create");
    setSelectedEvent(null);
    setIsEventDialogOpen(true);
  };

  const handleEditEvent = (event: any) => {
    setEventDialogMode("edit");
    setSelectedEvent(event);
    setIsEventDialogOpen(true);
  };

  const handleBlockFocusTime = () => {
    const now = new Date();
    const startTime = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
    const endTime = new Date(startTime.getTime() + 2 * 60 * 60 * 1000); // 2 hours duration

    const focusEvent = {
      title: "Focus Time - Deep Work",
      description: "Blocked time for focused work without interruptions",
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      event_type: "other" as const,
      priority: "high" as const,
      category: "internal" as const,
      location: "Office/Remote",
    };

    setSelectedEvent(focusEvent);
    setEventDialogMode("create");
    setIsEventDialogOpen(true);
  };

  const handleMeetingTemplates = () => {
    // Create a quick interview template
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0); // 2 PM tomorrow

    const endTime = new Date(tomorrow.getTime() + 60 * 60 * 1000); // 1 hour duration

    const interviewTemplate = {
      title: "Candidate Interview",
      description:
        "Technical interview with candidate\n\nAgenda:\n- Introduction (5 min)\n- Technical questions (40 min)\n- Q&A (10 min)\n- Next steps (5 min)",
      start_time: tomorrow.toISOString(),
      end_time: endTime.toISOString(),
      event_type: "interview" as const,
      priority: "high" as const,
      category: "recruitment" as const,
      location: "Conference Room / Video Call",
    };

    setSelectedEvent(interviewTemplate);
    setEventDialogMode("create");
    setIsEventDialogOpen(true);
  };

  const handleSetAvailability = () => {
    // Create availability blocks for the week
    const today = new Date();
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + ((1 + 7 - today.getDay()) % 7));
    nextMonday.setHours(9, 0, 0, 0);

    const availabilityEnd = new Date(nextMonday);
    availabilityEnd.setHours(17, 0, 0, 0);

    const availabilityEvent = {
      title: "Available for Meetings",
      description:
        "Available for interviews, meetings, and calls during business hours",
      start_time: nextMonday.toISOString(),
      end_time: availabilityEnd.toISOString(),
      event_type: "other" as const,
      priority: "medium" as const,
      category: "general" as const,
      location: "Office",
    };

    setSelectedEvent(availabilityEvent);
    setEventDialogMode("create");
    setIsEventDialogOpen(true);
  };

  const navigateMonth = (direction: "prev" | "next") => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      if (direction === "prev") {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      const prevMonthDay = new Date(year, month, -startingDayOfWeek + i + 1);
      days.push({
        date: prevMonthDay,
        isCurrentMonth: false,
        isToday: false,
        isSelected: false,
      });
    }

    // Add days of the current month
    for (let day = 1; day <= daysInMonth; day++) {
      const dayDate = new Date(year, month, day);
      const isToday = dayDate.toDateString() === new Date().toDateString();
      const isSelected = selectedDate
        ? dayDate.toDateString() === selectedDate.toDateString()
        : false;

      days.push({
        date: dayDate,
        isCurrentMonth: true,
        isToday,
        isSelected,
      });
    }

    // Add days from next month to fill the grid
    const remainingCells = 42 - days.length; // 6 rows × 7 days = 42 cells
    for (let day = 1; day <= remainingCells; day++) {
      const nextMonthDay = new Date(year, month + 1, day);
      days.push({
        date: nextMonthDay,
        isCurrentMonth: false,
        isToday: false,
        isSelected: false,
      });
    }

    return days;
  };

  const getEventsForDate = (date: Date) => {
    return localEvents.filter((event) => {
      const eventDate = new Date(event.start_time);
      return eventDate.toDateString() === date.toDateString();
    });
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    if (onDateSelect) {
      onDateSelect(date);
    }
    // Prefill event with clicked date, default time 09:00–10:00
    const start = new Date(date);
    start.setHours(9, 0, 0, 0);
    const end = new Date(date);
    end.setHours(10, 0, 0, 0);
    setSelectedEvent({
      title: "",
      description: "",
      start_time: start.toISOString(),
      end_time: end.toISOString(),
      location: "",
      meeting_link: "",
      event_type: "meeting",
      priority: "medium",
      category: "general",
    });
    setEventDialogMode("create");
    setIsEventDialogOpen(true);
  };

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const renderMonthView = () => {
    const days = getDaysInMonth(currentDate);

    return (
      <div className="w-full">
        {/* Calendar Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold">
              {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
            </h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth("prev")}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(new Date())}
              className="text-sm"
            >
              Today
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth("next")}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="border rounded-lg overflow-hidden bg-white">
          {/* Day Headers */}
          <div className="grid grid-cols-7 border-b bg-gray-50">
            {dayNames.map((day) => (
              <div
                key={day}
                className="p-4 text-center font-medium text-gray-700 border-r last:border-r-0"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days */}
          <div className="grid grid-cols-7">
            {days.map((day, index) => {
              const dayEvents = getEventsForDate(day.date);
              return (
                <div
                  key={index}
                  className={`
                    h-32 border-r border-b last:border-r-0 p-2 cursor-pointer transition-colors
                    ${day.isCurrentMonth ? "bg-white hover:bg-gray-50" : "bg-gray-50 text-gray-400"}
                    ${day.isToday ? "bg-blue-50" : ""}
                    ${day.isSelected ? "bg-blue-100" : ""}
                  `}
                  onClick={() => handleDateClick(day.date)}
                >
                  <div className="flex flex-col h-full">
                    <div
                      className={`
                      text-sm font-medium mb-1
                      ${day.isToday ? "bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center" : ""}
                    `}
                    >
                      {day.date.getDate()}
                    </div>
                    <div className="flex-1 space-y-1 overflow-hidden">
                      {dayEvents.slice(0, 3).map((event, eventIndex) => (
                        <div
                          key={event.id}
                          className="text-xs bg-blue-100 text-blue-800 rounded px-1 py-0.5 truncate cursor-pointer hover:bg-blue-200"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditEvent(event);
                          }}
                        >
                          {event.title}
                        </div>
                      ))}
                      {dayEvents.length > 3 && (
                        <div className="text-xs text-gray-500">
                          +{dayEvents.length - 3} more
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    return (
      <div className="p-4 space-y-4">
        <h3 className="text-lg font-semibold">Week View</h3>
        <div className="grid grid-cols-7 gap-2">
          {Array.from({ length: 7 }).map((_, index) => (
            <div key={index} className="border rounded-md p-3 min-h-[120px]">
              <div className="text-sm font-medium mb-2">
                {new Date(
                  currentDate.getTime() + index * 24 * 60 * 60 * 1000,
                ).toLocaleDateString("en-US", { weekday: "short" })}
              </div>
              {localEvents
                .filter((event) => {
                  const eventDate = new Date(event.start_time);
                  const dayDate = new Date(
                    currentDate.getTime() + index * 24 * 60 * 60 * 1000,
                  );
                  return eventDate.toDateString() === dayDate.toDateString();
                })
                .map((event) => (
                  <div
                    key={event.id}
                    className="text-xs bg-primary/10 p-1 rounded mb-1 cursor-pointer hover:bg-primary/20"
                    onClick={() => handleEditEvent(event)}
                  >
                    {event.title}
                  </div>
                ))}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderCalendarView = () => {
    switch (viewMode) {
      case "month":
        return renderMonthView();
      case "week":
        return renderWeekView();
      case "agenda":
        return (
          <div className="p-4 space-y-4">
            <h3 className="text-lg font-semibold">Agenda View</h3>
            <div className="space-y-2">
              {localEvents.slice(0, 10).map((event) => (
                <div
                  key={event.id}
                  className="flex items-center space-x-4 p-3 border rounded-md cursor-pointer hover:bg-accent/5"
                  onClick={() => handleEditEvent(event)}
                >
                  <div className="text-sm font-medium w-24">
                    {new Date(event.start_time).toLocaleDateString()}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{event.title}</div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(event.start_time).toLocaleTimeString()} -{" "}
                      {new Date(event.end_time).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      default:
        return renderMonthView();
    }
  };

  if (loading) {
    return <div className="p-4">Loading events...</div>;
  }

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex justify-center items-center flex-wrap gap-4">
        <div className="flex gap-2 flex-wrap justify-center">
          <Button onClick={handleCreateEvent} className="bg-primary">
            <Plus className="mr-2 h-4 w-4" />
            Create Event
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <View className="mr-2 h-4 w-4" />
                Switch View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Calendar Views</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setViewMode("month")}>
                <Grid className="mr-2 h-4 w-4" />
                Month Grid
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setViewMode("week")}>
                <LayoutGrid className="mr-2 h-4 w-4" />
                Week View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setViewMode("agenda")}>
                <LayoutList className="mr-2 h-4 w-4" />
                Agenda View
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filter Events
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Event Categories</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setSelectedCategory("all")}>
                All Events
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedCategory("meetings")}>
                Meetings
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setSelectedCategory("interviews")}
              >
                Interviews
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedCategory("personal")}>
                Personal
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Select
            onValueChange={setSelectedTimezone}
            defaultValue={selectedTimezone}
          >
            <SelectTrigger className="w-[180px]">
              <Globe2 className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Select Timezone" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="UTC">UTC</SelectItem>
              <SelectItem value="EST">Eastern Time</SelectItem>
              <SelectItem value="PST">Pacific Time</SelectItem>
              <SelectItem value="GMT">GMT</SelectItem>
            </SelectContent>
          </Select>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Settings2 className="mr-2 h-4 w-4" />
                Settings
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Calendar Settings</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() =>
                  toast({
                    title: "Export",
                    description: "Exporting calendar as ICS...",
                  })
                }
              >
                Export as ICS
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  toast({
                    title: "Export",
                    description: "Exporting calendar as CSV...",
                  })
                }
              >
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() =>
                  toast({
                    title: "Sync Started",
                    description: "Syncing with external calendars...",
                  })
                }
              >
                Sync Calendars
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid gap-4 sm:gap-6 lg:grid-cols-[1.5fr,0.75fr,0.75fr]">
        {/* Calendar view column */}
        <div className="flex-1 min-w-0">
          <Card className="min-h-[600px]">
            <CardContent className="p-6">{renderCalendarView()}</CardContent>
          </Card>
        </div>

        {/* Meeting Insights + Quick Actions column */}
        <div className="flex-1 min-w-0 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="mr-2 h-4 w-4" />
                Meeting Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                You have {localEvents.length} events scheduled.
              </p>
              <div className="space-y-2">
                <div className="p-3 bg-secondary/10 rounded-md">
                  <p className="text-sm font-medium">Upcoming Events</p>
                  <p className="text-xs text-muted-foreground">
                    {
                      localEvents.filter(
                        (e) => new Date(e.start_time) > new Date(),
                      ).length
                    }{" "}
                    events scheduled for the future.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="mr-2 h-4 w-4" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={handleBlockFocusTime}
              >
                Block Focus Time
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={handleMeetingTemplates}
              >
                Meeting Templates
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={handleSetAvailability}
              >
                Set Availability
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Events column */}
        <div className="flex-1 min-w-0">
          <UpcomingEvents
            events={localEvents}
            onDeleteEvent={(eventId) =>
              setLocalEvents((prev) => prev.filter((e) => e.id !== eventId))
            }
          />
        </div>
      </div>

      {/* Event Manager Dialog */}
      <EventManager
        isOpen={isEventDialogOpen}
        onClose={() => setIsEventDialogOpen(false)}
        mode={eventDialogMode}
        event={selectedEvent}
        onEventCreated={(eventObj) =>
          setLocalEvents((prev) => [...prev, eventObj])
        }
        onEventEdited={(eventObj) =>
          setLocalEvents((prev) =>
            prev.map((e) => (e.id === eventObj.id ? eventObj : e)),
          )
        }
        onEventDeleted={(eventId) =>
          setLocalEvents((prev) => prev.filter((e) => e.id !== eventId))
        }
      />
    </div>
  );
};
