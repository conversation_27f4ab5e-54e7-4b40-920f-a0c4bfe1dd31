import React, { useState } from 'react';
import { EnhancedTabs, TabPanel } from '@/design-system/controls/EnhancedTabs';
import { Home, Users, Settings, BarChart } from 'lucide-react';

export function TabIndicatorTest() {
  const [activeTab, setActiveTab] = useState('home');

  const tabs = [
    { value: 'home', label: 'Home', icon: Home },
    { value: 'users', label: 'Users', icon: Users },
    { value: 'analytics', label: 'Analytics', icon: BarC<PERSON> },
    { value: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className="p-8 space-y-8">
      <h2 className="text-2xl font-bold">Enhanced Tab Indicator Test</h2>
      
      <div className="space-y-4">
        <p className="text-muted-foreground">
          The tab indicator should now have a modern glowing effect with gradient edges and animated pulse.
        </p>
        
        <EnhancedTabs 
          tabs={tabs} 
          value={activeTab} 
          onValueChange={setActiveTab}
          variant="navigation"
        >
          <TabPanel value="home">
            <div className="p-6 bg-card rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Home Content</h3>
              <p>The tab indicator should be a glowing blue line with soft edges and animation.</p>
            </div>
          </TabPanel>
          
          <TabPanel value="users">
            <div className="p-6 bg-card rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Users Content</h3>
              <p>Notice how the indicator smoothly animates between tabs with a glowing effect.</p>
            </div>
          </TabPanel>
          
          <TabPanel value="analytics">
            <div className="p-6 bg-card rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Analytics Content</h3>
              <p>The gradient edges and box shadow create a contemporary, aesthetic look.</p>
            </div>
          </TabPanel>
          
          <TabPanel value="settings">
            <div className="p-6 bg-card rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Settings Content</h3>
              <p>This matches the modern design language of the AI Workflows page.</p>
            </div>
          </TabPanel>
        </EnhancedTabs>
      </div>
    </div>
  );
}
