import React, { useState } from 'react';
import { EnhancedTabs, TabPanel } from '@/design-system/controls/EnhancedTabs';
import { 
  Activity, 
  BarChart, 
  Settings, 
  Users, 
  FileText,
  Bell,
  Shield,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

type IndicatorStyle = 'default' | 'underline' | 'pill' | 'neon' | 'blob' | 'wave' | 'magnetic' | 'laser' | 'orb' | 'circuit';

const indicatorStyles: { value: IndicatorStyle; label: string; description: string }[] = [
  { value: 'default', label: 'Default', description: 'Classic subtle background' },
  { value: 'underline', label: 'Glowing Underline', description: 'Modern underline with glow effect' },
  { value: 'pill', label: 'Floating Pill', description: 'Elevated pill shape with shadow' },
  { value: 'neon', label: 'Neon Outline', description: 'Vibrant neon border effect' },
  { value: 'blob', label: 'Morphing Blob', description: 'Organic animated shape' },
  { value: 'wave', label: 'Gradient Wave', description: 'Flowing gradient animation' },
  { value: 'magnetic', label: 'Magnetic Border', description: 'Magnetic field effect' },
  { value: 'laser', label: 'Laser Focus', description: 'Laser beam with scanning effect' },
  { value: 'orb', label: 'Gradient Orb', description: 'Glowing orb with gradient' },
  { value: 'circuit', label: 'Circuit Board', description: 'Tech-inspired circuit pattern' },
];

export function TabIndicatorDemo() {
  const [activeTab, setActiveTab] = useState('overview');
  const [indicatorStyle, setIndicatorStyle] = useState<IndicatorStyle>('underline');

  const tabs = [
    { value: 'overview', label: 'Overview', icon: Activity, badge: 3 },
    { value: 'analytics', label: 'Analytics', icon: BarChart },
    { value: 'users', label: 'Users', icon: Users, badge: '12' },
    { value: 'settings', label: 'Settings', icon: Settings },
    { value: 'documents', label: 'Documents', icon: FileText },
    { value: 'notifications', label: 'Alerts', icon: Bell, badge: { value: '99+', variant: 'destructive' as const } },
    { value: 'security', label: 'Security', icon: Shield },
    { value: 'automation', label: 'Automation', icon: Zap },
  ];

  const currentStyle = indicatorStyles.find(s => s.value === indicatorStyle);

  return (
    <div className="space-y-8 p-8">
      <Card>
        <CardHeader>
          <CardTitle>Tab Indicator Styles</CardTitle>
          <CardDescription>
            Explore different visual styles for tab selection indicators
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center gap-4">
            <Label htmlFor="indicator-style">Indicator Style:</Label>
            <Select value={indicatorStyle} onValueChange={(value) => setIndicatorStyle(value as IndicatorStyle)}>
              <SelectTrigger id="indicator-style" className="w-[200px]">
                <SelectValue placeholder="Select style" />
              </SelectTrigger>
              <SelectContent>
                {indicatorStyles.map((style) => (
                  <SelectItem key={style.value} value={style.value}>
                    {style.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {currentStyle && (
              <span className="text-sm text-muted-foreground">
                {currentStyle.description}
              </span>
            )}
          </div>

          <div className="border rounded-lg p-6 bg-background">
            <EnhancedTabs
              tabs={tabs}
              value={activeTab}
              onValueChange={setActiveTab}
              indicatorStyle={indicatorStyle}
              variant="navigation"
              size="md"
              fullWidth={false}
            >
              <TabPanel value="overview">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Overview Panel</h3>
                  <p className="text-muted-foreground">
                    This is the overview tab content. The tab indicator style is currently set to: <strong>{indicatorStyle}</strong>
                  </p>
                  <div className="grid grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">1,234</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">89</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Performance</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">98%</div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabPanel>

              <TabPanel value="analytics">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Analytics Dashboard</h3>
                  <p className="text-muted-foreground">
                    View detailed analytics and metrics for your application.
                  </p>
                </div>
              </TabPanel>

              <TabPanel value="users">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">User Management</h3>
                  <p className="text-muted-foreground">
                    Manage users, roles, and permissions from this panel.
                  </p>
                </div>
              </TabPanel>

              <TabPanel value="settings">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Settings</h3>
                  <p className="text-muted-foreground">
                    Configure application settings and preferences.
                  </p>
                </div>
              </TabPanel>

              <TabPanel value="documents">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Document Library</h3>
                  <p className="text-muted-foreground">
                    Browse and manage your documents and files.
                  </p>
                </div>
              </TabPanel>

              <TabPanel value="notifications">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Notifications & Alerts</h3>
                  <p className="text-muted-foreground">
                    View and manage system notifications and alerts.
                  </p>
                </div>
              </TabPanel>

              <TabPanel value="security">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Security Center</h3>
                  <p className="text-muted-foreground">
                    Monitor and configure security settings.
                  </p>
                </div>
              </TabPanel>

              <TabPanel value="automation">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Automation Workflows</h3>
                  <p className="text-muted-foreground">
                    Create and manage automated workflows.
                  </p>
                </div>
              </TabPanel>
            </EnhancedTabs>
          </div>

          <div className="mt-8 p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Style Details</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Current Style:</span>
                <span className="ml-2 font-medium">{currentStyle?.label}</span>
              </div>
              <div>
                <span className="text-muted-foreground">CSS Class:</span>
                <span className="ml-2 font-mono text-xs bg-background px-2 py-1 rounded">
                  .tab-indicator-{indicatorStyle}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
