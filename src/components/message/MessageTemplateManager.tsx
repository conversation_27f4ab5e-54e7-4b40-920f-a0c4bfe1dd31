import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Plus,
  Pencil,
  Trash2,
  Save,
  X,
  Sparkles,
  Loader2,
  Wand2,
  FileText,
  MessageSquare,
  Calendar,
  Send,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";
import { useCreateMessageTemplate } from "@/hooks/useCreateMessageTemplate";
import { useUpdateMessageTemplate } from "@/hooks/useUpdateMessageTemplate";
import { useDeleteMessageTemplate } from "@/hooks/useDeleteMessageTemplate";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

export function MessageTemplateManager() {
  const { toast } = useToast();
  const { data: templates = [], isLoading } = useMessageTemplates();
  const createTemplate = useCreateMessageTemplate();
  const updateTemplate = useUpdateMessageTemplate();
  const deleteTemplate = useDeleteMessageTemplate();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("all");
  const [formData, setFormData] = useState({
    name: "",
    subject: "",
    content: "",
    template_category: "general" as
      | "general"
      | "interview"
      | "follow_up"
      | "rejection"
      | "offer",
  });
  const [isOptimizing, setIsOptimizing] = useState(false);

  // Define tabs with icons
  const tabs = [
    { value: "all", label: "All", icon: FileText },
    { value: "general", label: "General", icon: MessageSquare },
    { value: "interview", label: "Interview", icon: Calendar },
    { value: "follow_up", label: "Follow-up", icon: Send },
    { value: "offer", label: "Offer", icon: CheckCircle },
    { value: "rejection", label: "Rejection", icon: XCircle },
  ];

  // Reset form when dialog closes
  useEffect(() => {
    if (!isCreateDialogOpen) {
      setFormData({
        name: "",
        subject: "",
        content: "",
        template_category: "general",
      });
    }
  }, [isCreateDialogOpen]);

  // Set form data when editing
  useEffect(() => {
    if (selectedTemplate) {
      setFormData({
        name: selectedTemplate.name,
        subject: selectedTemplate.subject,
        content: selectedTemplate.content,
        template_category: selectedTemplate.template_category,
      });
    }
  }, [selectedTemplate]);

  const handleCreateTemplate = async () => {
    try {
      await createTemplate.mutateAsync(formData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Error creating template:", error);
    }
  };

  const handleUpdateTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      await updateTemplate.mutateAsync({
        id: selectedTemplate.id,
        ...formData,
      });
      setIsEditDialogOpen(false);
      setSelectedTemplate(null);
    } catch (error) {
      console.error("Error updating template:", error);
    }
  };

  const handleDeleteTemplate = async (id: string) => {
    try {
      await deleteTemplate.mutateAsync(id);
    } catch (error) {
      console.error("Error deleting template:", error);
    }
  };

  const handleEditClick = (template: any) => {
    setSelectedTemplate(template);
    setIsEditDialogOpen(true);
  };

  const optimizeMessageWithAI = async () => {
    if (isOptimizing || !formData.content || !formData.template_category)
      return;

    setIsOptimizing(true);
    try {
      const prompt = `
Optimize the following message template for better engagement and effectiveness:

Template Category: ${formData.template_category}
Current Subject: ${formData.subject}
Current Content: ${formData.content}

Please improve this template by:
1. Making it more engaging and professional
2. Improving clarity and readability
3. Adding appropriate personalization placeholders (e.g., {{candidate_name}}, {{position_title}})
4. Ensuring the tone matches the template category
5. Optimizing for better response rates

Return the optimized version in JSON format:
{
  "subject": "improved subject line",
  "content": "improved message content"
}
`;

      const systemPrompt =
        "You are an expert communication specialist focusing on recruitment messaging. Create professional, engaging, and effective message templates that improve candidate engagement and response rates.";

      const response = await generateText(prompt, systemPrompt);
      const cleanedResponse = response
        .replace(/```json\s*/, "")
        .replace(/```\s*$/, "")
        .trim();
      const optimized = JSON.parse(cleanedResponse);

      if (optimized.subject && optimized.content) {
        setFormData((prev) => ({
          ...prev,
          subject: optimized.subject,
          content: optimized.content,
        }));

        toast({
          title: "Message Optimized",
          description:
            "Your message template has been improved with AI suggestions.",
        });
      }
    } catch (error) {
      console.error("Error optimizing message:", error);
      toast({
        title: "Optimization Failed",
        description: "Unable to optimize the message at the moment.",
        variant: "destructive",
      });
    } finally {
      setIsOptimizing(false);
    }
  };

  const renderTemplateList = (category: string) => {
    const filteredTemplates = templates.filter(
      (template) =>
        template.template_category === category ||
        (category === "all" && template),
    );

    if (filteredTemplates.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          No templates found in this category
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {filteredTemplates.map((template) => (
          <Card
            key={template.id}
            className="hover:bg-accent/5 transition-colors"
          >
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium">{template.name}</h3>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditClick(template)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteTemplate(template.id)}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                Subject: {template.subject}
              </p>
              <div className="text-sm text-muted-foreground whitespace-pre-line line-clamp-3">
                {template.content}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Message Templates</CardTitle>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </CardHeader>
      <CardContent>
        <EnhancedTabs 
          tabs={tabs}
          value={activeTab}
          onValueChange={setActiveTab}
          variant="navigation"
          indicatorStyle="underline"
          size="md"
        >
          <ScrollArea className="h-[500px] mt-4">
            <TabPanel value="all">
              {isLoading ? (
                <div className="text-center py-8">Loading templates...</div>
              ) : (
                renderTemplateList("all")
              )}
            </TabPanel>
            <TabPanel value="general">
              {renderTemplateList("general")}
            </TabPanel>
            <TabPanel value="interview">
              {renderTemplateList("interview")}
            </TabPanel>
            <TabPanel value="follow_up">
              {renderTemplateList("follow_up")}
            </TabPanel>
            <TabPanel value="offer">
              {renderTemplateList("offer")}
            </TabPanel>
            <TabPanel value="rejection">
              {renderTemplateList("rejection")}
            </TabPanel>
          </ScrollArea>
        </EnhancedTabs>
      </CardContent>

      {/* Create Template Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Message Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder="e.g., Interview Invitation"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.template_category}
                  onValueChange={(value: any) =>
                    setFormData({ ...formData, template_category: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="interview">Interview</SelectItem>
                    <SelectItem value="follow_up">Follow-up</SelectItem>
                    <SelectItem value="offer">Offer</SelectItem>
                    <SelectItem value="rejection">Rejection</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={formData.subject}
                onChange={(e) =>
                  setFormData({ ...formData, subject: e.target.value })
                }
                placeholder="e.g., Interview Invitation for [Position]"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) =>
                  setFormData({ ...formData, content: e.target.value })
                }
                placeholder="Enter template content..."
                className="min-h-[200px]"
              />
              <p className="text-xs text-muted-foreground">
                Use placeholders like [Name], [Position], [Company], etc. that
                will be replaced when using the template.
              </p>
            </div>
          </div>
          <DialogFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              className="gap-2"
              onClick={optimizeMessageWithAI}
              disabled={
                isOptimizing || !formData.content || createTemplate.isPending
              }
            >
              {isOptimizing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4" />
                  Optimize with AI
                </>
              )}
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateTemplate}
                disabled={createTemplate.isPending || isOptimizing}
              >
                {createTemplate.isPending ? "Creating..." : "Create Template"}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Template Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Message Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Template Name</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-category">Category</Label>
                <Select
                  value={formData.template_category}
                  onValueChange={(value: any) =>
                    setFormData({ ...formData, template_category: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="interview">Interview</SelectItem>
                    <SelectItem value="follow_up">Follow-up</SelectItem>
                    <SelectItem value="offer">Offer</SelectItem>
                    <SelectItem value="rejection">Rejection</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-subject">Subject</Label>
              <Input
                id="edit-subject"
                value={formData.subject}
                onChange={(e) =>
                  setFormData({ ...formData, subject: e.target.value })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-content">Content</Label>
              <Textarea
                id="edit-content"
                value={formData.content}
                onChange={(e) =>
                  setFormData({ ...formData, content: e.target.value })
                }
                className="min-h-[200px]"
              />
              <p className="text-xs text-muted-foreground">
                Use placeholders like [Name], [Position], [Company], etc. that
                will be replaced when using the template.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateTemplate}
              disabled={updateTemplate.isPending}
            >
              {updateTemplate.isPending ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
