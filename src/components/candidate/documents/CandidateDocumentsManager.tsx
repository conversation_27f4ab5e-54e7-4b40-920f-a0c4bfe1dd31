import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { DocumentUpload } from "./DocumentUpload";
import { DocumentList } from "./DocumentList";
import { LoadingState } from "./LoadingState";
import { EmptyState } from "./EmptyState";
import { useDocumentManagement } from "./useDocumentManagement";
import { useState } from "react";
import { Upload, FolderOpen } from "lucide-react";

interface CandidateDocumentsManagerProps {
  candidateId: string;
}

export function CandidateDocumentsManager({
  candidateId,
}: CandidateDocumentsManagerProps) {
  const { documents, isLoading, deleteMutation, handleFileUpload } =
    useDocumentManagement(candidateId);
  const [isUploading, setIsUploading] = useState(false);
  const [activeTab, setActiveTab] = useState("upload");
  
  const tabs = [
    { value: "upload", label: "Upload Documents", icon: Upload },
    { 
      value: "manage", 
      label: "Manage Documents", 
      icon: FolderOpen,
      badge: documents?.length || undefined
    },
  ];

  const handleUpload = async (file: File) => {
    setIsUploading(true);
    try {
      await handleFileUpload(file);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownload = async (fileUrl: string, fileName: string) => {
    try {
      const response = await fetch(fileUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  const handleDelete = (documentId: string) => {
    deleteMutation.mutate(documentId);
  };

  if (isLoading) {
    return <LoadingState />;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Document Management</CardTitle>
      </CardHeader>
      <CardContent>
        <EnhancedTabs
          tabs={tabs}
          value={activeTab}
          onValueChange={setActiveTab}
          variant="navigation"
          indicatorStyle="underline"
          size="md"
        >
          <TabPanel value="upload">
            <DocumentUpload
              candidateId={candidateId}
              onUpload={handleUpload}
              isUploading={isUploading}
            />
          </TabPanel>

          <TabPanel value="manage">
            {!documents || documents.length === 0 ? (
              <EmptyState />
            ) : (
              <DocumentList
                documents={documents}
                onDownload={handleDownload}
                onDelete={handleDelete}
                isDeleting={deleteMutation.isPending}
              />
            )}
          </TabPanel>
        </EnhancedTabs>
      </CardContent>
    </Card>
  );
}
