import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>les, Loader2, TrendingUp, AlertTriangle, Plus, X, MessageSquare } from "lucide-react";
import { useState } from "react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";
import { CommunicationAssessment } from "@/types/candidate";
import { AIInsightsModal } from "./AIInsightsModal";

interface EvaluationType {
  category: string;
  score: number;
  notes: string;
}

interface CandidateEvaluationsProps {
  evaluations: EvaluationType[];
  candidate?: {
    name?: string;
    skills?: string[];
    experience?: string;
    education?: string;
    role?: string;
  };
  isEditing?: boolean;
  onChange?: (evaluations: EvaluationType[]) => void;
  communicationAssessment?: CommunicationAssessment;
  onCommunicationChange?: (assessment: CommunicationAssessment) => void;
}

export function CandidateEvaluations({
  evaluations,
  candidate,
  isEditing = false,
  onChange,
  communicationAssessment,
  onCommunicationChange,
}: CandidateEvaluationsProps) {
  const { toast } = useToast();
  const [aiSuggestions, setAiSuggestions] = useState<string>("");
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false);
  const [isInsightsModalOpen, setIsInsightsModalOpen] = useState(false);

  const generateAISuggestions = async () => {
    if (!candidate || isGeneratingSuggestions) return;

    setIsGeneratingSuggestions(true);
    try {
      const evaluationData = evaluations
        .map(
          (evaluation) =>
            `${evaluation.category}: ${evaluation.score}/5 - ${evaluation.notes}`,
        )
        .join("\n");

      const prompt = `
Analyze the following candidate evaluation data and provide strategic insights:

Candidate Profile:
- Name: ${candidate.name || "Not specified"}
- Role: ${candidate.role || "Not specified"}
- Skills: ${candidate.skills?.join(", ") || "Not specified"}
- Experience: ${candidate.experience || "Not specified"}
- Education: ${candidate.education || "Not specified"}

Current Evaluations:
${evaluationData}

Provide analysis including:
1. Overall assessment and strengths/weaknesses
2. Areas for improvement and development
3. Recommendations for interview focus areas
4. Suggestions for additional evaluation criteria
5. Fit assessment for the role

Keep the response actionable and focused on recruitment decision-making.
`;

      const systemPrompt =
        "You are an expert talent assessment consultant specializing in candidate evaluation. Provide strategic insights that help recruitment teams make informed hiring decisions.";

      const suggestions = await generateText(prompt, systemPrompt);
      setAiSuggestions(suggestions);
      setIsInsightsModalOpen(true); // Open modal after generating insights

      toast({
        title: "AI Insights Generated",
        description: "Evaluation insights are ready to view.",
      });
    } catch (error) {
      console.error("Error generating AI suggestions:", error);
      toast({
        title: "Suggestions Unavailable",
        description: "Unable to generate AI suggestions at the moment.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingSuggestions(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 4) return "text-green-600";
    if (score >= 3) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadge = (score: number) => {
    if (score >= 4) return "bg-green-100 text-green-800";
    if (score >= 3) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const averageScore =
    evaluations.length > 0
      ? evaluations.reduce((sum, evaluation) => sum + evaluation.score, 0) /
        evaluations.length
      : 0;

  const handleEvaluationChange = (index: number, field: keyof EvaluationType, value: string | number) => {
    if (onChange) {
      const updatedEvaluations = [...evaluations];
      updatedEvaluations[index] = {
        ...updatedEvaluations[index],
        [field]: value,
      };
      onChange(updatedEvaluations);
    }
  };

  const addEvaluation = () => {
    if (onChange) {
      const newEvaluation: EvaluationType = {
        category: "",
        score: 3,
        notes: "",
      };
      onChange([...evaluations, newEvaluation]);
    }
  };

  const removeEvaluation = (index: number) => {
    if (onChange && evaluations.length > 1) {
      const updatedEvaluations = evaluations.filter((_, i) => i !== index);
      onChange(updatedEvaluations);
    }
  };

  const handleCommunicationChange = (field: keyof CommunicationAssessment, value: any) => {
    if (onCommunicationChange) {
      onCommunicationChange({
        ...communicationAssessment,
        [field]: value,
      });
    }
  };

  const defaultCommunication: CommunicationAssessment = {
    overallLevel: 3,
    grammar: 3,
    articulation: 3,
    attitude: 3,
    energy: 3,
    languages: ["English"],
    ...communicationAssessment,
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            Evaluation Scores
            {averageScore > 0 && (
              <Badge className={getScoreBadge(averageScore)}>
                Avg: {averageScore.toFixed(1)}/5
              </Badge>
            )}
          </CardTitle>
          <div className="flex gap-2">
            {isEditing && (
              <Button
                variant="outline"
                size="sm"
                onClick={addEvaluation}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Evaluation
              </Button>
            )}
            {candidate && !isEditing && (
              <Button
                variant="outline"
                size="sm"
                onClick={aiSuggestions ? () => setIsInsightsModalOpen(true) : generateAISuggestions}
                disabled={isGeneratingSuggestions}
              >
                {isGeneratingSuggestions ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    {aiSuggestions ? "View AI Insights" : "AI Insights"}
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {evaluations.map((evaluation, index) => (
            <div key={index} className="space-y-2">
              {isEditing ? (
                <div className="space-y-3 p-4 border rounded-lg">
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Evaluation category"
                      value={evaluation.category}
                      onChange={(e) => handleEvaluationChange(index, "category", e.target.value)}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      min="1"
                      max="5"
                      value={evaluation.score}
                      onChange={(e) => handleEvaluationChange(index, "score", parseInt(e.target.value) || 1)}
                      className="w-20"
                    />
                    <span className="text-sm text-muted-foreground">/5</span>
                    {evaluations.length > 1 && (
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => removeEvaluation(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  <Textarea
                    placeholder="Evaluation notes"
                    value={evaluation.notes}
                    onChange={(e) => handleEvaluationChange(index, "notes", e.target.value)}
                    className="min-h-[60px]"
                  />
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      {evaluation.category}
                    </span>
                    <div className="flex items-center gap-2">
                      <span
                        className={`text-sm font-medium ${getScoreColor(evaluation.score)}`}
                      >
                        {evaluation.score}/5
                      </span>
                      {evaluation.score >= 4 && (
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      )}
                      {evaluation.score <= 2 && (
                        <AlertTriangle className="h-3 w-3 text-red-600" />
                      )}
                    </div>
                  </div>
                  <div className="h-2 bg-secondary rounded-full overflow-hidden">
                    <div
                      className={`h-full ${
                        evaluation.score >= 4
                          ? "bg-green-500"
                          : evaluation.score >= 3
                            ? "bg-yellow-500"
                            : "bg-red-500"
                      }`}
                      style={{ width: `${(evaluation.score / 5) * 100}%` }}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {evaluation.notes}
                  </p>
                </>
              )}
            </div>
          ))}

          {/* Communication Assessment Section */}
          <Separator className="my-6" />
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Communication Assessment
            </h3>

            {isEditing ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div>
                    <Label htmlFor="overallLevel">Overall (1-5)</Label>
                    <Input
                      id="overallLevel"
                      type="number"
                      min="1"
                      max="5"
                      value={defaultCommunication.overallLevel || 3}
                      onChange={(e) => handleCommunicationChange("overallLevel", parseInt(e.target.value) || 3)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="grammar">Grammar</Label>
                    <Input
                      id="grammar"
                      type="number"
                      min="1"
                      max="5"
                      value={defaultCommunication.grammar || 3}
                      onChange={(e) => handleCommunicationChange("grammar", parseInt(e.target.value) || 3)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="articulation">Articulation</Label>
                    <Input
                      id="articulation"
                      type="number"
                      min="1"
                      max="5"
                      value={defaultCommunication.articulation || 3}
                      onChange={(e) => handleCommunicationChange("articulation", parseInt(e.target.value) || 3)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="attitude">Attitude</Label>
                    <Input
                      id="attitude"
                      type="number"
                      min="1"
                      max="5"
                      value={defaultCommunication.attitude || 3}
                      onChange={(e) => handleCommunicationChange("attitude", parseInt(e.target.value) || 3)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="energy">Energy</Label>
                    <Input
                      id="energy"
                      type="number"
                      min="1"
                      max="5"
                      value={defaultCommunication.energy || 3}
                      onChange={(e) => handleCommunicationChange("energy", parseInt(e.target.value) || 3)}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="languages">Languages (comma-separated)</Label>
                  <Input
                    id="languages"
                    placeholder="English, French, Spanish"
                    value={defaultCommunication.languages?.join(", ") || "English"}
                    onChange={(e) => handleCommunicationChange("languages", e.target.value.split(",").map(l => l.trim()))}
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium">Overall Communication:</span>
                  <Badge className={getScoreBadge(communicationAssessment?.overallLevel || 0)}>
                    {communicationAssessment?.overallLevel || "Not assessed"}/5
                  </Badge>
                </div>

                {communicationAssessment?.overallLevel && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Grammar:</span>
                      <span className="ml-2 font-medium">{communicationAssessment.grammar}/5</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Articulation:</span>
                      <span className="ml-2 font-medium">{communicationAssessment.articulation}/5</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Attitude:</span>
                      <span className="ml-2 font-medium">{communicationAssessment.attitude}/5</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Energy:</span>
                      <span className="ml-2 font-medium">{communicationAssessment.energy}/5</span>
                    </div>
                  </div>
                )}

                {communicationAssessment?.languages && communicationAssessment.languages.length > 0 && (
                  <div>
                    <span className="text-sm text-muted-foreground">Languages:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {communicationAssessment.languages.map((lang, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {lang}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

        </div>
      </CardContent>

      {/* AI Insights Modal */}
      <AIInsightsModal
        isOpen={isInsightsModalOpen}
        onClose={() => setIsInsightsModalOpen(false)}
        insights={aiSuggestions}
        isLoading={isGeneratingSuggestions}
      />
    </Card>
  );
}
