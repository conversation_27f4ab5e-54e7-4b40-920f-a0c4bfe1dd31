import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Clock, User, Plus, X, CheckCircle2, AlertCircle, XCircle } from "lucide-react";
import { InterviewStage } from "@/types/candidate";

interface InterviewStagesProps {
  stages: InterviewStage[];
  isEditing?: boolean;
  onChange?: (stages: InterviewStage[]) => void;
}

export function InterviewStages({
  stages,
  isEditing = false,
  onChange,
}: InterviewStagesProps) {
  const handleStageChange = (index: number, field: keyof InterviewStage, value: any) => {
    if (onChange) {
      const updatedStages = [...stages];
      updatedStages[index] = {
        ...updatedStages[index],
        [field]: value,
      };
      onChange(updatedStages);
    }
  };

  const addStage = () => {
    if (onChange) {
      const newStage: InterviewStage = {
        id: `stage-${Date.now()}`,
        name: "",
        status: "not_started",
      };
      onChange([...stages, newStage]);
    }
  };

  const removeStage = (index: number) => {
    if (onChange && stages.length > 1) {
      const updatedStages = stages.filter((_, i) => i !== index);
      onChange(updatedStages);
    }
  };

  const getStatusIcon = (status: InterviewStage["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />;
      case "scheduled":
        return <Clock className="w-4 h-4 text-blue-600" />;
      case "cancelled":
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: InterviewStage["status"]) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Interview Stages
          </CardTitle>
          {isEditing && (
            <Button
              variant="outline"
              size="sm"
              onClick={addStage}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Stage
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {stages.map((stage, index) => (
            <div key={stage.id} className="space-y-3">
              {isEditing ? (
                <div className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Stage name (e.g., Phone Screen, Technical Interview)"
                      value={stage.name}
                      onChange={(e) => handleStageChange(index, "name", e.target.value)}
                      className="flex-1"
                    />
                    <Select
                      value={stage.status}
                      onValueChange={(value) => handleStageChange(index, "status", value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="not_started">Not Started</SelectItem>
                        <SelectItem value="scheduled">Scheduled</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                    {stages.length > 1 && (
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => removeStage(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="datetime-local"
                      placeholder="Scheduled date"
                      value={stage.scheduledDate || ""}
                      onChange={(e) => handleStageChange(index, "scheduledDate", e.target.value)}
                    />
                    <Input
                      placeholder="Interviewer"
                      value={stage.interviewer || ""}
                      onChange={(e) => handleStageChange(index, "interviewer", e.target.value)}
                    />
                  </div>

                  {stage.status === "completed" && (
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        min="1"
                        max="5"
                        placeholder="Score (1-5)"
                        value={stage.score || ""}
                        onChange={(e) => handleStageChange(index, "score", parseInt(e.target.value) || undefined)}
                      />
                      <Input
                        type="datetime-local"
                        placeholder="Completed date"
                        value={stage.completedDate || ""}
                        onChange={(e) => handleStageChange(index, "completedDate", e.target.value)}
                      />
                    </div>
                  )}

                  <Textarea
                    placeholder="Notes and feedback"
                    value={stage.notes || ""}
                    onChange={(e) => handleStageChange(index, "notes", e.target.value)}
                    className="min-h-[60px]"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(stage.status)}
                    <div>
                      <div className="font-medium">{stage.name}</div>
                      {stage.interviewer && (
                        <div className="text-sm text-muted-foreground flex items-center gap-1">
                          <User className="w-3 h-3" />
                          {stage.interviewer}
                        </div>
                      )}
                      {stage.scheduledDate && (
                        <div className="text-sm text-muted-foreground">
                          Scheduled: {new Date(stage.scheduledDate).toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {stage.score && (
                      <Badge variant="outline">
                        Score: {stage.score}/5
                      </Badge>
                    )}
                    <Badge className={getStatusBadge(stage.status)}>
                      {stage.status.replace("_", " ")}
                    </Badge>
                  </div>
                </div>
              )}
              
              {!isEditing && stage.notes && (
                <div className="ml-7 text-sm text-muted-foreground bg-gray-50 p-2 rounded">
                  {stage.notes}
                </div>
              )}
            </div>
          ))}
          
          {stages.length === 0 && !isEditing && (
            <div className="text-center text-muted-foreground py-4">
              No interview stages defined yet.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
