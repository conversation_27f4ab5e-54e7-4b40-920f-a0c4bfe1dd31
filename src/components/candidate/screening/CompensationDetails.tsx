import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { DollarSign, TrendingUp, Gift, Calendar } from "lucide-react";
import { CompensationDetails as CompensationDetailsType } from "@/types/candidate";

interface CompensationDetailsProps {
  compensation?: CompensationDetailsType;
  isEditing?: boolean;
  onChange?: (compensation: CompensationDetailsType) => void;
}

export function CompensationDetails({
  compensation,
  isEditing = false,
  onChange,
}: CompensationDetailsProps) {
  const handleFieldChange = (field: keyof CompensationDetailsType, value: any) => {
    if (onChange) {
      onChange({
        ...compensation,
        [field]: value,
      });
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return "Not specified";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const defaultCompensation: CompensationDetailsType = {
    currentSalary: 0,
    expectedSalaryMin: 0,
    expectedSalaryMax: 0,
    contractPreference: "either",
    ...compensation,
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="w-5 h-5" />
          Compensation & Benefits
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {isEditing ? (
            <>
              {/* Salary Information */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Salary Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="currentSalary">Current Salary</Label>
                    <Input
                      id="currentSalary"
                      type="number"
                      placeholder="75000"
                      value={defaultCompensation.currentSalary || ""}
                      onChange={(e) => handleFieldChange("currentSalary", parseInt(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="expectedMin">Expected Min</Label>
                    <Input
                      id="expectedMin"
                      type="number"
                      placeholder="80000"
                      value={defaultCompensation.expectedSalaryMin || ""}
                      onChange={(e) => handleFieldChange("expectedSalaryMin", parseInt(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="expectedMax">Expected Max</Label>
                    <Input
                      id="expectedMax"
                      type="number"
                      placeholder="95000"
                      value={defaultCompensation.expectedSalaryMax || ""}
                      onChange={(e) => handleFieldChange("expectedSalaryMax", parseInt(e.target.value) || 0)}
                    />
                  </div>
                </div>
              </div>

              {/* Contract Preference */}
              <div className="space-y-2">
                <Label>Employment Type Preference</Label>
                <Select
                  value={defaultCompensation.contractPreference || "either"}
                  onValueChange={(value) => handleFieldChange("contractPreference", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="permanent">Permanent Only</SelectItem>
                    <SelectItem value="contract">Contract Only</SelectItem>
                    <SelectItem value="either">Either</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Bonus Information */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Gift className="w-4 h-4" />
                  Bonus & Benefits
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="bonusPercentage">Bonus Percentage</Label>
                    <Input
                      id="bonusPercentage"
                      type="number"
                      placeholder="10"
                      value={defaultCompensation.bonusPercentage || ""}
                      onChange={(e) => handleFieldChange("bonusPercentage", parseInt(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="vacationWeeks">Vacation Weeks</Label>
                    <Input
                      id="vacationWeeks"
                      type="number"
                      placeholder="3"
                      value={defaultCompensation.vacationWeeks || ""}
                      onChange={(e) => handleFieldChange("vacationWeeks", parseInt(e.target.value) || 0)}
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="bonusAchieved"
                    checked={defaultCompensation.bonusAchieved || false}
                    onCheckedChange={(checked) => handleFieldChange("bonusAchieved", checked)}
                  />
                  <Label htmlFor="bonusAchieved">Bonus typically achieved</Label>
                </div>
              </div>

              {/* Additional Benefits */}
              <div>
                <Label htmlFor="currentWorkStatus">Current Work Status</Label>
                <Input
                  id="currentWorkStatus"
                  placeholder="Employed, actively looking"
                  value={defaultCompensation.currentWorkStatus || ""}
                  onChange={(e) => handleFieldChange("currentWorkStatus", e.target.value)}
                />
              </div>
            </>
          ) : (
            <>
              {/* View Mode */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Current Salary</span>
                    <p className="text-lg font-semibold">{formatCurrency(compensation?.currentSalary)}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Expected Range</span>
                    <p className="text-lg font-semibold">
                      {compensation?.expectedSalaryMin && compensation?.expectedSalaryMax
                        ? `${formatCurrency(compensation.expectedSalaryMin)} - ${formatCurrency(compensation.expectedSalaryMax)}`
                        : "Not specified"}
                    </p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2">
                  {compensation?.contractPreference && (
                    <Badge variant="outline">
                      {compensation.contractPreference === "either" ? "Contract or Permanent" : 
                       compensation.contractPreference === "contract" ? "Contract Only" : "Permanent Only"}
                    </Badge>
                  )}
                  {compensation?.bonusPercentage && (
                    <Badge variant="outline">
                      {compensation.bonusPercentage}% Bonus {compensation.bonusAchieved ? "(Typically Achieved)" : ""}
                    </Badge>
                  )}
                  {compensation?.vacationWeeks && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {compensation.vacationWeeks} weeks vacation
                    </Badge>
                  )}
                </div>

                {compensation?.currentWorkStatus && (
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Current Status</span>
                    <p>{compensation.currentWorkStatus}</p>
                  </div>
                )}

                {!compensation?.currentSalary && !compensation?.expectedSalaryMin && (
                  <div className="text-center text-muted-foreground py-4">
                    <DollarSign className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>No compensation information provided yet.</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
