import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CheckCircle2, XCircle, Clock, Pause, User, Calendar } from "lucide-react";
import { ScreeningDecision as ScreeningDecisionType } from "@/types/candidate";

interface ScreeningDecisionProps {
  decision?: ScreeningDecisionType;
  isEditing?: boolean;
  onChange?: (decision: ScreeningDecisionType) => void;
}

export function ScreeningDecision({
  decision,
  isEditing = false,
  onChange,
}: ScreeningDecisionProps) {
  const handleDecisionChange = (field: keyof ScreeningDecisionType, value: any) => {
    if (onChange) {
      onChange({
        ...decision,
        [field]: value,
      } as ScreeningDecisionType);
    }
  };

  const getStatusIcon = (status: ScreeningDecisionType["status"]) => {
    switch (status) {
      case "approved":
        return <CheckCircle2 className="w-5 h-5 text-green-600" />;
      case "rejected":
        return <XCircle className="w-5 h-5 text-red-600" />;
      case "on_hold":
        return <Pause className="w-5 h-5 text-yellow-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: ScreeningDecisionType["status"]) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      case "on_hold":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusLabel = (status: ScreeningDecisionType["status"]) => {
    switch (status) {
      case "approved":
        return "Approved";
      case "rejected":
        return "Rejected";
      case "on_hold":
        return "On Hold";
      default:
        return "Pending";
    }
  };

  const defaultDecision: ScreeningDecisionType = {
    status: "pending",
  };

  const currentDecision = decision || defaultDecision;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon(currentDecision.status)}
          Screening Decision
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {isEditing ? (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Decision Status</label>
                <Select
                  value={currentDecision.status}
                  onValueChange={(value) => handleDecisionChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {currentDecision.status !== "pending" && (
                <>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Reason</label>
                    <Input
                      placeholder="Brief reason for the decision"
                      value={currentDecision.reason || ""}
                      onChange={(e) => handleDecisionChange("reason", e.target.value)}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Decision Notes</label>
                    <Textarea
                      placeholder="Detailed notes about the decision"
                      value={currentDecision.notes || ""}
                      onChange={(e) => handleDecisionChange("notes", e.target.value)}
                      className="min-h-[80px]"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Decided By</label>
                    <Input
                      placeholder="Name of decision maker"
                      value={currentDecision.decidedBy || ""}
                      onChange={(e) => handleDecisionChange("decidedBy", e.target.value)}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Decision Date</label>
                    <Input
                      type="datetime-local"
                      value={currentDecision.decidedAt || ""}
                      onChange={(e) => handleDecisionChange("decidedAt", e.target.value)}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Next Steps</label>
                    <Textarea
                      placeholder="What are the next steps for this candidate?"
                      value={currentDecision.nextSteps || ""}
                      onChange={(e) => handleDecisionChange("nextSteps", e.target.value)}
                      className="min-h-[60px]"
                    />
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Status</span>
                <Badge className={getStatusBadge(currentDecision.status)}>
                  {getStatusLabel(currentDecision.status)}
                </Badge>
              </div>

              {currentDecision.reason && (
                <div>
                  <span className="text-sm font-medium block mb-1">Reason</span>
                  <p className="text-sm text-muted-foreground">{currentDecision.reason}</p>
                </div>
              )}

              {currentDecision.notes && (
                <div>
                  <span className="text-sm font-medium block mb-1">Notes</span>
                  <p className="text-sm text-muted-foreground">{currentDecision.notes}</p>
                </div>
              )}

              {currentDecision.decidedBy && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <User className="w-4 h-4" />
                  <span>Decided by {currentDecision.decidedBy}</span>
                </div>
              )}

              {currentDecision.decidedAt && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="w-4 h-4" />
                  <span>On {new Date(currentDecision.decidedAt).toLocaleString()}</span>
                </div>
              )}

              {currentDecision.nextSteps && (
                <div>
                  <span className="text-sm font-medium block mb-1">Next Steps</span>
                  <p className="text-sm text-muted-foreground">{currentDecision.nextSteps}</p>
                </div>
              )}

              {currentDecision.status === "pending" && (
                <div className="text-center text-muted-foreground py-4">
                  No decision has been made yet.
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
