import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CheckCircle2, XCircle, AlertCircle } from "lucide-react";

interface RequirementsType {
  workAuthorization: "verified" | "pending" | "not_required";
  backgroundCheck: "verified" | "pending" | "not_required";
  drugScreening: "verified" | "pending" | "not_required";
  references: "verified" | "pending" | "not_required";
}

interface CandidateRequirementsProps {
  requirements: RequirementsType;
  isEditing?: boolean;
  onChange?: (requirements: RequirementsType) => void;
}

export function CandidateRequirements({
  requirements,
  isEditing = false,
  onChange,
}: CandidateRequirementsProps) {
  const handleRequirementChange = (
    field: keyof RequirementsType,
    value: "verified" | "pending" | "not_required"
  ) => {
    if (onC<PERSON><PERSON>) {
      onChange({
        ...requirements,
        [field]: value,
      });
    }
  };

  const getRequirementStatus = (status: string) => {
    switch (status) {
      case "verified":
        return (
          <Badge className="bg-success text-success-foreground">
            <CheckCircle2 className="w-3 h-3 mr-1" />
            Verified
          </Badge>
        );
      case "pending":
        return (
          <Badge variant="outline" className="text-warning border-warning">
            <AlertCircle className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
      case "not_required":
        return (
          <Badge variant="secondary">
            <XCircle className="w-3 h-3 mr-1" />
            Not Required
          </Badge>
        );
      default:
        return null;
    }
  };

  const renderRequirementField = (
    label: string,
    field: keyof RequirementsType,
    value: string
  ) => {
    if (isEditing) {
      return (
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">{label}</span>
          <Select
            value={value}
            onValueChange={(newValue) =>
              handleRequirementChange(field, newValue as "verified" | "pending" | "not_required")
            }
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="verified">Verified</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="not_required">Not Required</SelectItem>
            </SelectContent>
          </Select>
        </div>
      );
    }

    return (
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium">{label}</span>
        {getRequirementStatus(value)}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Requirements</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {renderRequirementField("Work Authorization", "workAuthorization", requirements.workAuthorization)}
          {renderRequirementField("Background Check", "backgroundCheck", requirements.backgroundCheck)}
          {renderRequirementField("Drug Screening", "drugScreening", requirements.drugScreening)}
          {renderRequirementField("References", "references", requirements.references)}
        </div>
      </CardContent>
    </Card>
  );
}
