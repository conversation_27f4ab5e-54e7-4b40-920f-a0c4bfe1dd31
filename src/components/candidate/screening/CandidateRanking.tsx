import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  TrendingUp, 
  Brain, 
  Target, 
  Users, 
  DollarSign, 
  MapPin, 
  Sparkles,
  Loader2,
  CheckCircle,
  AlertTriangle,
  Lightbulb
} from "lucide-react";
import { useState } from "react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";
import { CandidateRanking as CandidateRankingType } from "@/types/candidate";

interface CandidateRankingProps {
  ranking?: CandidateRankingType;
  candidate?: {
    name?: string;
    role?: string;
    experience?: string;
    skills?: string[];
  };
  evaluations?: any[];
  compensation?: any;
  locationPreferences?: any;
  isEditing?: boolean;
  onChange?: (ranking: CandidateRankingType) => void;
}

export function CandidateRanking({
  ranking,
  candidate,
  evaluations = [],
  compensation,
  locationPreferences,
  isEditing = false,
  onChange,
}: CandidateRankingProps) {
  const { toast } = useToast();
  const [isGeneratingRanking, setIsGeneratingRanking] = useState(false);

  const generateAIRanking = async () => {
    if (!candidate || isGeneratingRanking) return;

    setIsGeneratingRanking(true);
    try {
      // Prepare data for AI analysis
      const evaluationData = evaluations.map(evaluation =>
        `${evaluation.category}: ${evaluation.score}/5 - ${evaluation.notes || 'No notes'}`
      ).join('\n');

      const compensationData = compensation ? 
        `Current: $${compensation.currentSalary || 'Not specified'}, Expected: $${compensation.expectedSalaryMin || 'Not specified'}-$${compensation.expectedSalaryMax || 'Not specified'}` : 
        'Not specified';

      const locationData = locationPreferences ? 
        `Work arrangement: ${locationPreferences.workArrangement || 'Not specified'}, Travel: ${locationPreferences.travelPercentage || 0}%` : 
        'Not specified';

      const prompt = `
Analyze this candidate for an AI-powered ranking system:

**Candidate:** ${candidate.name}
**Role:** ${candidate.role}
**Experience:** ${candidate.experience}
**Skills:** ${candidate.skills?.join(', ') || 'Not specified'}

**Evaluation Scores:**
${evaluationData}

**Compensation Info:** ${compensationData}
**Location Preferences:** ${locationData}

Provide a comprehensive ranking analysis with:
1. Overall score (0-100)
2. Individual criteria scores (0-100 each):
   - Technical Fit
   - Experience Match
   - Cultural Fit
   - Compensation Alignment
   - Location Compatibility
3. Confidence level (0-100)
4. Key strengths (3-5 points)
5. Areas of concern (2-4 points)
6. Specific recommendations (3-5 actionable items)
7. Brief explanation of the overall assessment

Format as JSON with this structure:
{
  "overallScore": number,
  "criteriaScores": {
    "technicalFit": number,
    "experienceMatch": number,
    "culturalFit": number,
    "compensationAlignment": number,
    "locationCompatibility": number
  },
  "confidenceLevel": number,
  "aiExplanation": "string",
  "rankingFactors": {
    "strengths": ["string"],
    "concerns": ["string"],
    "recommendations": ["string"]
  }
}
`;

      const systemPrompt = "You are an expert AI recruitment analyst specializing in candidate ranking and assessment. Provide objective, data-driven analysis with specific scores and actionable insights. Return only valid JSON.";

      const response = await generateText(prompt, systemPrompt);

      try {
        // Clean the response to extract JSON
        let cleanResponse = response.trim();

        // Remove markdown code blocks if present
        if (cleanResponse.startsWith('```json')) {
          cleanResponse = cleanResponse.replace(/```json\s*/, '').replace(/```\s*$/, '');
        } else if (cleanResponse.startsWith('```')) {
          cleanResponse = cleanResponse.replace(/```\s*/, '').replace(/```\s*$/, '');
        }

        // Find JSON object in the response
        const jsonStart = cleanResponse.indexOf('{');
        const jsonEnd = cleanResponse.lastIndexOf('}') + 1;

        if (jsonStart !== -1 && jsonEnd > jsonStart) {
          cleanResponse = cleanResponse.substring(jsonStart, jsonEnd);
        }

        console.log("Attempting to parse AI response:", cleanResponse);
        const rankingData = JSON.parse(cleanResponse);

        // Validate required fields
        if (!rankingData.overallScore || !rankingData.criteriaScores) {
          throw new Error("Invalid ranking data structure");
        }

        const newRanking: CandidateRankingType = {
          ...rankingData,
          lastUpdated: new Date().toISOString(),
        };

        if (onChange) {
          onChange(newRanking);
        }

        toast({
          title: "AI Ranking Generated",
          description: "Candidate ranking analysis is complete.",
        });
      } catch (parseError) {
        console.error("Parse error:", parseError, "Raw response:", response);
        throw new Error("Failed to parse AI ranking response");
      }
    } catch (error) {
      console.error("Error generating AI ranking:", error);
      toast({
        title: "Error",
        description: "Failed to generate AI ranking. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingRanking(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return "bg-green-100 text-green-800";
    if (score >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const criteriaIcons = {
    technicalFit: Target,
    experienceMatch: TrendingUp,
    culturalFit: Users,
    compensationAlignment: DollarSign,
    locationCompatibility: MapPin,
  };

  const criteriaLabels = {
    technicalFit: "Technical Fit",
    experienceMatch: "Experience Match",
    culturalFit: "Cultural Fit",
    compensationAlignment: "Compensation Alignment",
    locationCompatibility: "Location Compatibility",
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            AI Candidate Ranking
            {ranking && (
              <Badge className={getScoreBadgeColor(ranking.overallScore)}>
                {ranking.overallScore}/100
              </Badge>
            )}
          </CardTitle>
          {!isEditing && candidate && (
            <Button
              variant="outline"
              size="sm"
              onClick={ranking ? () => generateAIRanking() : generateAIRanking}
              disabled={isGeneratingRanking}
            >
              {isGeneratingRanking ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  {ranking ? "Refresh Ranking" : "Generate Ranking"}
                </>
              )}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {ranking ? (
          <div className="space-y-6">
            {/* Overall Score */}
            <div className="text-center">
              <div className={`text-3xl font-bold ${getScoreColor(ranking.overallScore)}`}>
                {ranking.overallScore}/100
              </div>
              <p className="text-sm text-muted-foreground">Overall Candidate Score</p>
              <div className="mt-2">
                <Badge variant="outline">
                  Confidence: {ranking.confidenceLevel}%
                </Badge>
              </div>
            </div>

            <Separator />

            {/* Criteria Breakdown */}
            <div className="space-y-4">
              <h4 className="font-medium">Criteria Breakdown</h4>
              {Object.entries(ranking.criteriaScores).map(([key, score]) => {
                const Icon = criteriaIcons[key as keyof typeof criteriaIcons];
                const label = criteriaLabels[key as keyof typeof criteriaLabels];
                
                return (
                  <div key={key} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Icon className="w-4 h-4" />
                        <span className="text-sm font-medium">{label}</span>
                      </div>
                      <span className={`text-sm font-semibold ${getScoreColor(score)}`}>
                        {score}/100
                      </span>
                    </div>
                    <Progress value={score} className="h-2" />
                  </div>
                );
              })}
            </div>

            <Separator />

            {/* AI Explanation */}
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Brain className="w-4 h-4" />
                AI Analysis
              </h4>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {ranking.aiExplanation}
              </p>
            </div>

            {/* Ranking Factors */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Strengths */}
              <div className="space-y-2">
                <h5 className="font-medium text-green-700 flex items-center gap-1">
                  <CheckCircle className="w-4 h-4" />
                  Strengths
                </h5>
                <ul className="space-y-1">
                  {ranking.rankingFactors.strengths.map((strength, index) => (
                    <li key={index} className="text-sm text-green-600 flex items-start gap-1">
                      <span className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                      {strength}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Concerns */}
              <div className="space-y-2">
                <h5 className="font-medium text-orange-700 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  Concerns
                </h5>
                <ul className="space-y-1">
                  {ranking.rankingFactors.concerns.map((concern, index) => (
                    <li key={index} className="text-sm text-orange-600 flex items-start gap-1">
                      <span className="w-1 h-1 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                      {concern}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Recommendations */}
              <div className="space-y-2">
                <h5 className="font-medium text-blue-700 flex items-center gap-1">
                  <Lightbulb className="w-4 h-4" />
                  Recommendations
                </h5>
                <ul className="space-y-1">
                  {ranking.rankingFactors.recommendations.map((recommendation, index) => (
                    <li key={index} className="text-sm text-blue-600 flex items-start gap-1">
                      <span className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                      {recommendation}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Last Updated */}
            <div className="text-xs text-muted-foreground text-center">
              Last updated: {new Date(ranking.lastUpdated).toLocaleString()}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Brain className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <h3 className="font-medium mb-2">AI Candidate Ranking</h3>
            <p className="text-sm mb-4">
              Generate an AI-powered ranking analysis to evaluate this candidate across multiple criteria.
            </p>
            {candidate && !isEditing && (
              <Button onClick={generateAIRanking} disabled={isGeneratingRanking}>
                {isGeneratingRanking ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate AI Ranking
                  </>
                )}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
