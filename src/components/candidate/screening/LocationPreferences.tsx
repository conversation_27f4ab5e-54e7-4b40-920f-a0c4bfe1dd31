import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { MapPin, Home, Car, Plane, Clock } from "lucide-react";
import { LocationPreferences as LocationPreferencesType } from "@/types/candidate";

interface LocationPreferencesProps {
  locationPreferences?: LocationPreferencesType;
  isEditing?: boolean;
  onChange?: (preferences: LocationPreferencesType) => void;
}

export function LocationPreferences({
  locationPreferences,
  isEditing = false,
  onChange,
}: LocationPreferencesProps) {
  const handleFieldChange = (field: keyof LocationPreferencesType, value: any) => {
    if (onChange) {
      onChange({
        ...locationPreferences,
        [field]: value,
      });
    }
  };

  const getWorkArrangementLabel = (arrangement?: string) => {
    switch (arrangement) {
      case "remote": return "Remote Only";
      case "hybrid": return "Hybrid";
      case "in_office": return "In-Office Only";
      case "flexible": return "Flexible";
      default: return "Not specified";
    }
  };

  const getWorkArrangementColor = (arrangement?: string) => {
    switch (arrangement) {
      case "remote": return "bg-green-100 text-green-800";
      case "hybrid": return "bg-blue-100 text-blue-800";
      case "in_office": return "bg-orange-100 text-orange-800";
      case "flexible": return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const defaultPreferences: LocationPreferencesType = {
    workArrangement: "flexible",
    relocateWilling: false,
    travelPercentage: 0,
    ...locationPreferences,
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="w-5 h-5" />
          Location & Work Preferences
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {isEditing ? (
            <>
              {/* Work Arrangement */}
              <div className="space-y-2">
                <Label>Preferred Work Arrangement</Label>
                <Select
                  value={defaultPreferences.workArrangement || "flexible"}
                  onValueChange={(value) => handleFieldChange("workArrangement", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="remote">Remote Only</SelectItem>
                    <SelectItem value="hybrid">Hybrid (Mix of remote/office)</SelectItem>
                    <SelectItem value="in_office">In-Office Only</SelectItem>
                    <SelectItem value="flexible">Flexible/Open to All</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Relocation */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Home className="w-4 h-4" />
                  Relocation Preferences
                </h4>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="relocateWilling"
                    checked={defaultPreferences.relocateWilling || false}
                    onCheckedChange={(checked) => handleFieldChange("relocateWilling", checked)}
                  />
                  <Label htmlFor="relocateWilling">Willing to relocate</Label>
                </div>
                
                {defaultPreferences.relocateWilling && (
                  <div>
                    <Label>Relocation Scope</Label>
                    <Select
                      value={defaultPreferences.relocateScope || "local"}
                      onValueChange={(value) => handleFieldChange("relocateScope", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="local">Local/Regional</SelectItem>
                        <SelectItem value="national">National</SelectItem>
                        <SelectItem value="international">International</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              {/* Travel & Commute */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Plane className="w-4 h-4" />
                  Travel & Commute
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="travelPercentage">Travel Percentage Acceptable</Label>
                    <Select
                      value={defaultPreferences.travelPercentage?.toString() || "0"}
                      onValueChange={(value) => handleFieldChange("travelPercentage", parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">0% - No Travel</SelectItem>
                        <SelectItem value="10">Up to 10%</SelectItem>
                        <SelectItem value="25">Up to 25%</SelectItem>
                        <SelectItem value="50">Up to 50%</SelectItem>
                        <SelectItem value="75">Up to 75%</SelectItem>
                        <SelectItem value="100">100% - Extensive Travel</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="commuteMaxMinutes">Max Commute (minutes)</Label>
                    <Input
                      id="commuteMaxMinutes"
                      type="number"
                      placeholder="30"
                      value={defaultPreferences.commuteMaxMinutes || ""}
                      onChange={(e) => handleFieldChange("commuteMaxMinutes", parseInt(e.target.value) || 0)}
                    />
                  </div>
                </div>
                
                <div>
                  <Label>Transportation Preference</Label>
                  <Select
                    value={defaultPreferences.transportationPreference || "car"}
                    onValueChange={(value) => handleFieldChange("transportationPreference", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="car">Car</SelectItem>
                      <SelectItem value="public_transport">Public Transport</SelectItem>
                      <SelectItem value="walking">Walking</SelectItem>
                      <SelectItem value="cycling">Cycling</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Work Eligibility */}
              <div className="space-y-2">
                <Label htmlFor="workEligibility">Work Eligibility</Label>
                <Input
                  id="workEligibility"
                  placeholder="e.g., Canada, US, Both"
                  value={defaultPreferences.workEligibility || ""}
                  onChange={(e) => handleFieldChange("workEligibility", e.target.value)}
                />
              </div>

              {/* Interview Availability */}
              <div className="space-y-2">
                <Label htmlFor="interviewAvailability">Interview Availability</Label>
                <Input
                  id="interviewAvailability"
                  placeholder="e.g., Weekdays 9-5, Evenings available"
                  value={defaultPreferences.interviewAvailability || ""}
                  onChange={(e) => handleFieldChange("interviewAvailability", e.target.value)}
                />
              </div>
            </>
          ) : (
            <>
              {/* View Mode */}
              <div className="space-y-4">
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Work Arrangement</span>
                  <div className="mt-1">
                    <Badge className={getWorkArrangementColor(locationPreferences?.workArrangement)}>
                      {getWorkArrangementLabel(locationPreferences?.workArrangement)}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Relocation</span>
                    <p className="flex items-center gap-2">
                      <Home className="w-4 h-4" />
                      {locationPreferences?.relocateWilling 
                        ? `Willing (${locationPreferences.relocateScope || "local"})` 
                        : "Not willing to relocate"}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Travel Acceptance</span>
                    <p className="flex items-center gap-2">
                      <Plane className="w-4 h-4" />
                      {locationPreferences?.travelPercentage !== undefined 
                        ? `Up to ${locationPreferences.travelPercentage}%` 
                        : "Not specified"}
                    </p>
                  </div>
                </div>

                {locationPreferences?.commuteMaxMinutes && (
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Max Commute</span>
                    <p className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      {locationPreferences.commuteMaxMinutes} minutes
                    </p>
                  </div>
                )}

                {locationPreferences?.transportationPreference && (
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Transportation</span>
                    <p className="flex items-center gap-2">
                      <Car className="w-4 h-4" />
                      {locationPreferences.transportationPreference.replace("_", " ")}
                    </p>
                  </div>
                )}

                {locationPreferences?.workEligibility && (
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Work Eligibility</span>
                    <p>{locationPreferences.workEligibility}</p>
                  </div>
                )}

                {locationPreferences?.interviewAvailability && (
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Interview Availability</span>
                    <p>{locationPreferences.interviewAvailability}</p>
                  </div>
                )}

                {!locationPreferences?.workArrangement && !locationPreferences?.workEligibility && (
                  <div className="text-center text-muted-foreground py-4">
                    <MapPin className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>No location preferences specified yet.</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
