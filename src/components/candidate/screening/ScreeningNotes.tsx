import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X, MessageSquare, User, Calendar, FileText, Users, Award, Gavel } from "lucide-react";
import { ScreeningNote } from "@/types/candidate";

interface ScreeningNotesProps {
  notes: ScreeningNote[];
  isEditing?: boolean;
  onChange?: (notes: ScreeningNote[]) => void;
  currentUser?: string;
}

export function ScreeningNotes({
  notes,
  isEditing = false,
  onChange,
  currentUser = "Current User",
}: ScreeningNotesProps) {
  const handleNoteChange = (index: number, field: keyof ScreeningNote, value: any) => {
    if (onChange) {
      const updatedNotes = [...notes];
      updatedNotes[index] = {
        ...updatedNotes[index],
        [field]: value,
      };
      onChange(updatedNotes);
    }
  };

  const addNote = () => {
    if (onChange) {
      const newNote: ScreeningNote = {
        id: `note-${Date.now()}`,
        content: "",
        author: currentUser,
        createdAt: new Date().toISOString(),
        type: "general",
      };
      onChange([...notes, newNote]);
    }
  };

  const removeNote = (index: number) => {
    if (onChange) {
      const updatedNotes = notes.filter((_, i) => i !== index);
      onChange(updatedNotes);
    }
  };

  const getTypeIcon = (type: ScreeningNote["type"]) => {
    switch (type) {
      case "interview":
        return <Users className="w-4 h-4" />;
      case "assessment":
        return <Award className="w-4 h-4" />;
      case "decision":
        return <Gavel className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getTypeBadge = (type: ScreeningNote["type"]) => {
    switch (type) {
      case "interview":
        return "bg-blue-100 text-blue-800";
      case "assessment":
        return "bg-purple-100 text-purple-800";
      case "decision":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeLabel = (type: ScreeningNote["type"]) => {
    switch (type) {
      case "interview":
        return "Interview";
      case "assessment":
        return "Assessment";
      case "decision":
        return "Decision";
      default:
        return "General";
    }
  };

  const sortedNotes = [...notes].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            Screening Notes
            {notes.length > 0 && (
              <Badge variant="secondary">{notes.length}</Badge>
            )}
          </CardTitle>
          {isEditing && (
            <Button
              variant="outline"
              size="sm"
              onClick={addNote}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Note
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedNotes.map((note, index) => (
            <div key={note.id} className="space-y-2">
              {isEditing ? (
                <div className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center gap-2">
                    <Select
                      value={note.type}
                      onValueChange={(value) => handleNoteChange(index, "type", value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">General</SelectItem>
                        <SelectItem value="interview">Interview</SelectItem>
                        <SelectItem value="assessment">Assessment</SelectItem>
                        <SelectItem value="decision">Decision</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="flex-1" />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => removeNote(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <Textarea
                    placeholder="Enter your note here..."
                    value={note.content}
                    onChange={(e) => handleNoteChange(index, "content", e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
              ) : (
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getTypeIcon(note.type)}
                      <Badge className={getTypeBadge(note.type)}>
                        {getTypeLabel(note.type)}
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(note.createdAt).toLocaleString()}
                    </div>
                  </div>
                  
                  <div className="text-sm whitespace-pre-wrap">
                    {note.content}
                  </div>
                  
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <User className="w-3 h-3" />
                    <span>{note.author}</span>
                  </div>
                </div>
              )}
            </div>
          ))}
          
          {notes.length === 0 && !isEditing && (
            <div className="text-center text-muted-foreground py-8">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No screening notes yet.</p>
              <p className="text-xs">Notes will appear here as they are added.</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
