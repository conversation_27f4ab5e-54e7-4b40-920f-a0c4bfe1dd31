import { useState } from "react";
import {
  CandidateType,
  ScreeningQuestion,
  Requirements,
} from "@/types/candidate";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CandidateRequirements } from "./screening/CandidateRequirements";
import { CandidateEvaluations } from "./screening/CandidateEvaluations";
import { AIInterviewQuestions } from "./screening/AIInterviewQuestions";
import { InterviewStages } from "./screening/InterviewStages";
import { ScreeningDecision } from "./screening/ScreeningDecision";
import { ScreeningNotes } from "./screening/ScreeningNotes";
import { CompensationDetails } from "./screening/CompensationDetails";
import { LocationPreferences } from "./screening/LocationPreferences";
import { CandidateRanking } from "./screening/CandidateRanking";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateActivityEntry } from "@/hooks/useCreateActivityEntry";

interface CandidateScreeningProps {
  candidate: CandidateType;
}

export function CandidateScreening({ candidate }: CandidateScreeningProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [screening, setScreening] = useState(candidate.screening || {});
  const { toast } = useToast();
  const { user } = useAuth();
  const createActivityEntry = useCreateActivityEntry();

  const updateScreeningMutation = useMutation({
    mutationFn: async (screeningData: any) => {
      if (!user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("candidates")
        .update({
          screening: screeningData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", candidate.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Create activity entry for screening update
      try {
        await createActivityEntry.mutateAsync({
          candidate_id: candidate.id,
          activity_type: "screening",
          title: "Screening Updated",
          description: "Candidate screening information has been updated",
          metadata: {
            status: "completed",
          },
        });
      } catch (activityError) {
        console.warn("Failed to create activity entry for screening update:", activityError);
      }

      toast({
        title: "Screening Updated",
        description: "Candidate screening information has been saved successfully.",
      });
      setIsEditing(false);
    },
    onError: (error) => {
      console.error("Error updating screening:", error);
      toast({
        title: "Error",
        description: "Failed to update screening information. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    updateScreeningMutation.mutate(screening);
  };

  const handleRequirementsChange = (newRequirements: any) => {
    setScreening({
      ...screening,
      requirements: newRequirements,
    });
  };

  const handleEvaluationsChange = (newEvaluations: any) => {
    setScreening({
      ...screening,
      evaluations: newEvaluations,
    });
  };

  const handleInterviewStagesChange = (newStages: any) => {
    setScreening({
      ...screening,
      interviewStages: newStages,
    });
  };

  const handleDecisionChange = (newDecision: any) => {
    setScreening({
      ...screening,
      decision: newDecision,
    });
  };

  const handleNotesChange = (newNotes: any) => {
    setScreening({
      ...screening,
      screeningNotes: newNotes,
    });
  };

  const handleCompensationChange = (newCompensation: any) => {
    setScreening({
      ...screening,
      compensation: newCompensation,
    });
  };

  const handleLocationPreferencesChange = (newLocationPreferences: any) => {
    setScreening({
      ...screening,
      locationPreferences: newLocationPreferences,
    });
  };

  const handleCommunicationAssessmentChange = (newCommunicationAssessment: any) => {
    setScreening({
      ...screening,
      communicationAssessment: newCommunicationAssessment,
    });
  };

  const handleRankingChange = (newRanking: any) => {
    setScreening({
      ...screening,
      candidateRanking: newRanking,
    });
  };

  // Extract requirements and evaluations from screening data with proper typing
  const requirements = (screening as any)?.requirements || {
    workAuthorization: "pending" as const,
    backgroundCheck: "pending" as const,
    drugScreening: "not_required" as const,
    references: "pending" as const,
  };

  const evaluations = (screening as any)?.evaluations || [
    {
      category: "Technical Skills",
      score: 4,
      notes: "Strong frontend development skills",
    },
    {
      category: "Communication",
      score: 5,
      notes: "Excellent verbal and written communication",
    },
    {
      category: "Problem Solving",
      score: 4,
      notes: "Good analytical thinking",
    },
  ];

  const interviewStages = (screening as any)?.interviewStages || [
    {
      id: "phone-screen",
      name: "Phone Screen",
      status: "completed",
      completedDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      interviewer: "HR Team",
      score: 4,
      notes: "Good initial conversation, candidate shows enthusiasm",
    },
    {
      id: "technical",
      name: "Technical Interview",
      status: "scheduled",
      scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      interviewer: "Engineering Team",
    },
  ];

  const decision = (screening as any)?.decision;
  const screeningNotes = (screening as any)?.screeningNotes || [];

  // Enhanced screening data with sample defaults for demonstration
  const compensation = (screening as any)?.compensation || {
    currentSalary: 75000,
    expectedSalaryMin: 80000,
    expectedSalaryMax: 95000,
    bonusPercentage: 10,
    bonusAchieved: true,
    benefits: ["equity", "extended_health", "dental"],
    vacationWeeks: 3,
    contractPreference: "either",
    currentWorkStatus: "Employed, actively looking",
  };

  const locationPreferences = (screening as any)?.locationPreferences || {
    workArrangement: "hybrid",
    relocateWilling: false,
    travelPercentage: 10,
    workEligibility: "Canada",
    transportationPreference: "car",
    commuteMaxMinutes: 30,
    interviewAvailability: "Weekdays 9-5, some evenings available",
  };

  const communicationAssessment = (screening as any)?.communicationAssessment || {
    overallLevel: 4,
    grammar: 4,
    articulation: 4,
    attitude: 5,
    energy: 4,
    languages: ["English", "French"],
  };

  const candidateRanking = (screening as any)?.candidateRanking;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Screening</h2>
          <p className="text-muted-foreground">
            Evaluate candidate skills, experience, and fit
          </p>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                onClick={() => setIsEditing(false)}
                disabled={updateScreeningMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={updateScreeningMutation.isPending}
              >
                {updateScreeningMutation.isPending ? (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Edit Screening
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CandidateRequirements
          requirements={requirements}
          isEditing={isEditing}
          onChange={handleRequirementsChange}
        />
        <CandidateEvaluations
          evaluations={evaluations}
          candidate={{
            name: candidate.name,
            skills: candidate.skills?.map(s => typeof s === 'string' ? s : s.name) || [],
            experience: candidate.experience,
            role: candidate.role,
          }}
          isEditing={isEditing}
          onChange={handleEvaluationsChange}
          communicationAssessment={communicationAssessment}
          onCommunicationChange={handleCommunicationAssessmentChange}
        />

        <CandidateRanking
          ranking={candidateRanking}
          candidate={{
            name: candidate.name,
            role: candidate.role,
            experience: candidate.experience,
            skills: candidate.skills?.map(s => typeof s === 'string' ? s : s.name) || [],
          }}
          evaluations={evaluations}
          compensation={compensation}
          locationPreferences={locationPreferences}
          isEditing={isEditing}
          onChange={handleRankingChange}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CompensationDetails
          compensation={compensation}
          isEditing={isEditing}
          onChange={handleCompensationChange}
        />
        <LocationPreferences
          locationPreferences={locationPreferences}
          isEditing={isEditing}
          onChange={handleLocationPreferencesChange}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <InterviewStages
          stages={interviewStages}
          isEditing={isEditing}
          onChange={handleInterviewStagesChange}
        />
        <ScreeningDecision
          decision={decision}
          isEditing={isEditing}
          onChange={handleDecisionChange}
        />
      </div>

      <ScreeningNotes
        notes={screeningNotes}
        isEditing={isEditing}
        onChange={handleNotesChange}
        currentUser={user?.email || "Current User"}
      />

      <AIInterviewQuestions candidate={candidate} />
    </div>
  );
}
