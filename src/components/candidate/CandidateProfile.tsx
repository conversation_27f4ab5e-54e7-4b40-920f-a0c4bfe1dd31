import { useState, useEffect } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CandidateOverview } from "./CandidateOverview";
import { CandidateScreening } from "./CandidateScreening";
import { CandidateActivity } from "./CandidateActivity";
import { CandidateDocuments } from "./CandidateDocuments";
import { CandidateInterviews } from "./CandidateInterviews";
import { CandidateNotes } from "./CandidateNotes";
import { CandidateComparison } from "./CandidateComparison";
import { CandidateJobs } from "./CandidateJobs";
import { CandidateType } from "@/types/candidate";
import { User, Shield, Briefcase, Activity, FileText, Calendar, StickyNote, Users } from "lucide-react";

interface CandidateProfileProps {
  candidate: CandidateType;
}

export function CandidateProfile({ candidate }: CandidateProfileProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { id } = useParams();
  
  // Parse the tab from the URL path
  const getTabFromUrl = () => {
    const pathSegments = location.pathname.split('/');
    const tabIndex = pathSegments.findIndex(segment => segment === id) + 1;
    const tab = pathSegments[tabIndex];
    
    const validTabs = ["profile", "screening", "jobs", "activity", "documents", "interviews", "notes", "compare"];
    
    // Return the tab if it's valid, otherwise default to "profile"
    return validTabs.includes(tab) ? tab : "profile";
  };
  
  const [activeTab, setActiveTab] = useState(getTabFromUrl());
  
  // Update activeTab when URL changes
  useEffect(() => {
    const tabFromUrl = getTabFromUrl();
    setActiveTab(tabFromUrl);
  }, [location.pathname]);
  
  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    // Special handling for activity tab which has sub-tabs
    if (value === "activity") {
      // Check if we're already on an activity sub-tab
      const pathSegments = location.pathname.split('/');
      const activityIndex = pathSegments.findIndex(segment => segment === 'activity');
      const currentSubTab = pathSegments[activityIndex + 1];
      
      // If already on a valid sub-tab, stay there
      const validSubTabs = ["all", "communications", "meetings", "documents"];
      if (validSubTabs.includes(currentSubTab)) {
        navigate(`/candidates/${id}/activity/${currentSubTab}`, { replace: true });
      } else {
        // Otherwise, navigate to the default sub-tab
        navigate(`/candidates/${id}/activity/all`, { replace: true });
      }
    } else {
      // Navigate to the new tab URL
      navigate(`/candidates/${id}/${value}`, { replace: true });
    }
  };
  
  const tabs = [
    { value: "profile", label: "Profile", icon: User },
    { value: "screening", label: "Screening", icon: Shield },
    { value: "jobs", label: "Jobs", icon: Briefcase },
    { value: "activity", label: "Activity", icon: Activity },
    { value: "documents", label: "Documents", icon: FileText },
    { value: "interviews", label: "Interviews", icon: Calendar },
    { value: "notes", label: "Notes", icon: StickyNote },
    { value: "compare", label: "Compare", icon: Users },
  ];

  return (
    <Card className="border-none shadow-none">
      <CardHeader>
        <CardTitle>Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <EnhancedTabs 
          tabs={tabs} 
          value={activeTab} 
          onValueChange={handleTabChange}
          variant="navigation"
          indicatorStyle="underline"
        >
          <TabPanel value="profile">
            <CandidateOverview candidate={candidate} />
          </TabPanel>

          <TabPanel value="screening">
            <CandidateScreening candidate={candidate} />
          </TabPanel>

          <TabPanel value="jobs">
            <CandidateJobs candidate={candidate} />
          </TabPanel>

          <TabPanel value="activity">
            <CandidateActivity candidate={candidate} />
          </TabPanel>

          <TabPanel value="documents">
            <CandidateDocuments candidate={candidate} />
          </TabPanel>

          <TabPanel value="interviews">
            <CandidateInterviews candidate={candidate} />
          </TabPanel>

          <TabPanel value="notes">
            <CandidateNotes candidate={candidate} />
          </TabPanel>

          <TabPanel value="compare">
            <CandidateComparison mainCandidate={candidate} />
          </TabPanel>
        </EnhancedTabs>
      </CardContent>
    </Card>
  );
}
