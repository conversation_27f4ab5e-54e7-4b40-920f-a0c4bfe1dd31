import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  MessageSquare,
  Mail,
  Phone,
  Calendar,
  FileText,
  User,
  ExternalLink,
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useNavigate } from "react-router-dom";

interface RecentActivityProps {
  candidateId: string;
  showAll?: boolean;
}

interface ActivityItem {
  id: string;
  type: string;
  description: string;
  date: string;
  icon: any;
  messageId?: string; // For linking to messages
  clickable?: boolean;
}

export function RecentActivity({ candidateId, showAll = false }: RecentActivityProps) {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch activity data
  const fetchActivities = async () => {
    if (!user) return [];

    const { data, error } = await supabase
      .from("candidate_activities")
      .select("*")
      .eq("candidate_id", candidateId)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })
      .limit(showAll ? 100 : 20);

    if (error) throw error;
    return data || [];
  };

  // Fetch messages for this candidate
  const fetchMessages = async () => {
    if (!user) return [];

    const { data, error } = await supabase
      .from("messages")
      .select("*")
      .eq("candidate_id", candidateId)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })
      .limit(showAll ? 50 : 10);

    if (error) throw error;
    return data || [];
  };

  // Use realtime subscription for activities
  const { records: activityRecords = [] } = useRealtimeCollection(
    "candidate_activities",
    fetchActivities,
    "public",
    `candidate_id=eq.${candidateId}`,
  );

  // Use realtime subscription for messages
  const { records: messageRecords = [] } = useRealtimeCollection(
    "messages",
    fetchMessages,
    "public",
    `candidate_id=eq.${candidateId}`,
  );

  // Transform activities to ActivityItem format
  const activityItems: ActivityItem[] = activityRecords
    .map((activity) => {
      let icon = Calendar; // default
      let description = activity.title;

      // Set icon based on activity type
      switch (activity.activity_type) {
        case "interview":
          icon = Calendar;
          break;
        case "note":
          icon = MessageSquare;
          break;
        case "document":
          icon = FileText;
          break;
        case "status_change":
        case "pipeline_move":
          icon = User;
          break;
        case "screening":
          icon = User;
          break;
        case "profile_update":
          icon = User;
          break;
        case "tag_added":
        case "tag_removed":
          icon = User;
          break;
        default:
          icon = Calendar;
      }

      return {
        id: activity.id,
        type: activity.activity_type,
        description: activity.description || activity.title,
        date: new Date(activity.created_at).toLocaleString(),
        icon,
        clickable: false,
      };
    });

  // Transform messages to ActivityItem format
  const messageItems: ActivityItem[] = messageRecords.map((message) => ({
    id: `msg-${message.id}`,
    type: "message",
    description: `Message from ${message.sender_name}: ${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}`,
    date: new Date(message.created_at).toLocaleString(),
    icon: Mail,
    messageId: message.id,
    clickable: true,
  }));

  // Combine and sort all activities by date
  const allActivities = [...activityItems, ...messageItems]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, showAll ? 100 : 10);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{showAll ? "All Activity" : "Recent Activity"}</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {allActivities.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="text-4xl mb-4">📋</div>
              <p className="text-muted-foreground">
                No recent activity found. Activity will appear here as you
                interact with this candidate.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {allActivities.map((activity) => (
                <div
                  key={activity.id}
                  className={`flex items-start gap-4 pb-4 border-b last:border-0 ${
                    activity.clickable ? 'cursor-pointer hover:bg-accent/50 -mx-2 px-2 rounded transition-colors group' : ''
                  }`}
                  onClick={() => {
                    if (activity.clickable && activity.messageId) {
                      navigate(`/communications?message=${activity.messageId}`);
                    }
                  }}
                >
                  <div className="p-2 bg-muted rounded-full">
                    <activity.icon className="w-4 h-4" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm">{activity.description}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {activity.date}
                    </p>
                  </div>
                  {activity.clickable && (
                    <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity mt-2" />
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
