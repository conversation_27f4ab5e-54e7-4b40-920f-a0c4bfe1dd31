import { CandidateType } from "@/types/candidate";
import { MatchedJobsList } from "./overview/MatchedJobsList";
import { DroppableApplicationPipeline } from "./overview/DroppableApplicationPipeline";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

interface CandidateJobsProps {
  candidate: CandidateType;
}

export function CandidateJobs({ candidate }: CandidateJobsProps) {
  const [candidateData, setCandidateData] = useState<any>(null);
  const [pipelineUpdateTrigger, setPipelineUpdateTrigger] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchCandidateData = async () => {
      try {
        setError(null);
        const { data, error } = await supabase
          .from("candidates_with_normalized_data")
          .select("*")
          .eq("id", candidate.id)
          .single();

        if (error) {
          console.error("Error fetching candidate data:", error);
          setError("Failed to load candidate data");
          return;
        }

        setCandidateData(data);
      } catch (error) {
        console.error("Error in fetchCandidateData:", error);
        setError("An unexpected error occurred");
      }
    };

    if (candidate.id) {
      fetchCandidateData();
    }
  }, [candidate.id]);

  // Error boundary
  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-red-500 mb-2">Error loading Jobs tab</p>
        <p className="text-sm text-muted-foreground">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Reload Page
        </button>
      </div>
    );
  }

  // Loading state
  if (!candidateData) {
    return (
      <div className="p-4 text-center">
        <p className="text-muted-foreground">Loading jobs...</p>
      </div>
    );
  }

  const handleApplicationUpdate = () => {
    setPipelineUpdateTrigger(prev => prev + 1);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || !user) return;

    // Check if dropping a job onto the application pipeline
    if (over.id === 'application-pipeline' && active.data.current?.type === 'job') {
      const jobData = active.data.current.job;

      try {
        // Check if application already exists
        const { data: existingApp, error: checkError } = await supabase
          .from("pipeline_candidates")
          .select("id")
          .eq("candidate_id", candidate.id)
          .eq("job_id", jobData.id)
          .eq("user_id", user.id)
          .single();

        if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
          throw checkError;
        }

        if (existingApp) {
          toast({
            title: "Already Applied",
            description: "This candidate has already been submitted for this position.",
            variant: "destructive",
          });
          return;
        }

        // Create new application
        // Convert match percentage (0-100) to rating (0-5)
        const rating = Math.min(5, Math.max(0, Math.round((jobData.match || 0) / 20)));

        const { error: insertError } = await supabase
          .from("pipeline_candidates")
          .insert({
            candidate_id: candidate.id,
            job_id: jobData.id,
            user_id: user.id,
            stage: "Applied",
            applied_at: new Date().toISOString(),
            source: "drag_drop",
            rating: rating
          });

        if (insertError) {
          throw insertError;
        }

        toast({
          title: "Application Submitted",
          description: `${candidate.name} has been submitted for ${jobData.title}.`,
        });

        // Trigger pipeline update
        handleApplicationUpdate();
      } catch (error) {
        console.error("Error submitting application via drag and drop:", error);
        toast({
          title: "Error",
          description: "Failed to submit application. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  return (
    <DndContext onDragEnd={handleDragEnd}>
      <div className="space-y-6">
        {/* Two-panel layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Panel - Matched Jobs */}
          <div className="space-y-4">
            <MatchedJobsList
              candidate={candidateData}
              onJobSubmitted={handleApplicationUpdate}
              enableDragAndDrop={true}
            />
          </div>

          {/* Right Panel - Application Pipeline */}
          <div className="space-y-4">
            <DroppableApplicationPipeline
              candidateId={candidate.id}
              key={pipelineUpdateTrigger} // Force re-render when applications update
              onApplicationUpdate={handleApplicationUpdate}
            />
          </div>
        </div>
      </div>
    </DndContext>
  );
}
