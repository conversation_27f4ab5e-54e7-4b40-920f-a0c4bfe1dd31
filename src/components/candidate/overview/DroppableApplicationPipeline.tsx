import { useDroppable } from '@dnd-kit/core';
import { ApplicationPipeline } from './ApplicationPipeline';

interface DroppableApplicationPipelineProps {
  candidateId: string;
  onApplicationUpdate?: () => void;
  onJobDropped?: (jobId: string, jobData: any) => void;
}

export function DroppableApplicationPipeline({ 
  candidateId, 
  onApplicationUpdate, 
  onJobDropped 
}: DroppableApplicationPipelineProps) {
  const { isOver, setNodeRef } = useDroppable({
    id: 'application-pipeline',
    data: {
      type: 'pipeline',
      accepts: ['job'],
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={`relative transition-colors duration-200 ${
        isOver ? 'bg-blue-50 border-2 border-blue-300 border-dashed rounded-lg' : ''
      }`}
    >
      {isOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-blue-50 bg-opacity-90 rounded-lg z-10 pointer-events-none">
          <div className="text-blue-600 font-medium text-center">
            <div className="text-lg mb-1">Drop to Submit Application</div>
            <div className="text-sm">The candidate will be submitted for this position</div>
          </div>
        </div>
      )}
      
      <ApplicationPipeline 
        candidateId={candidateId}
        onApplicationUpdate={onApplicationUpdate}
      />
    </div>
  );
}
