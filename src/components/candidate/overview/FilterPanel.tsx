import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ChevronDown, ChevronUp, Search, Filter, X, Calendar as CalendarIcon } from "lucide-react";
import { format, subDays, startOfDay, endOfDay } from "date-fns";
import { cn } from "@/lib/utils";
import { useDebounce } from "@/hooks/useDebounce";

interface FilterState {
  search: string;
  statuses: string[];
  dateFrom: Date | null;
  dateTo: Date | null;
  departments: string[];
  salaryMin: string;
  salaryMax: string;
}

interface FilterPanelProps {
  onFiltersChange: (filters: FilterState) => void;
  availableDepartments: string[];
  applicationCount: number;
  filteredCount: number;
}

const STAGE_OPTIONS = [
  "Applied",
  "Phone Screen", 
  "Interview",
  "Final Round",
  "Offer",
  "Rejected",
  "Withdrawn"
];

const DATE_PRESETS = [
  { label: "Last 7 days", days: 7 },
  { label: "Last 30 days", days: 30 },
  { label: "Last 90 days", days: 90 },
];

export function FilterPanel({
  onFiltersChange,
  availableDepartments,
  applicationCount,
  filteredCount
}: FilterPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    statuses: [],
    dateFrom: null,
    dateTo: null,
    departments: [],
    salaryMin: "",
    salaryMax: "",
  });

  // Debounce search input
  const debouncedSearch = useDebounce(searchInput, 300);

  // Simple update function
  const updateFilters = (updates: Partial<FilterState>) => {
    const newFilters = { ...filters, ...updates };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // Load filters from URL on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlFilters: FilterState = {
      search: urlParams.get("search") || "",
      statuses: urlParams.get("status")?.split(",").filter(Boolean) || [],
      dateFrom: urlParams.get("dateFrom") ? new Date(urlParams.get("dateFrom")!) : null,
      dateTo: urlParams.get("dateTo") ? new Date(urlParams.get("dateTo")!) : null,
      departments: urlParams.get("department")?.split(",").filter(Boolean) || [],
      salaryMin: urlParams.get("salaryMin") || "",
      salaryMax: urlParams.get("salaryMax") || "",
    };

    setFilters(urlFilters);
    setSearchInput(urlFilters.search);
    // Only call onFiltersChange on initial load
    if (JSON.stringify(urlFilters) !== JSON.stringify(filters)) {
      onFiltersChange(urlFilters);
    }
  }, []); // Empty dependency array

  // Update filters when debounced search changes
  useEffect(() => {
    if (debouncedSearch !== filters.search) {
      updateFilters({ search: debouncedSearch });
    }
  }, [debouncedSearch]); // Only depend on debouncedSearch

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    
    if (filters.search) params.set("search", filters.search);
    if (filters.statuses.length) params.set("status", filters.statuses.join(","));
    if (filters.dateFrom) params.set("dateFrom", filters.dateFrom.toISOString().split("T")[0]);
    if (filters.dateTo) params.set("dateTo", filters.dateTo.toISOString().split("T")[0]);
    if (filters.departments.length) params.set("department", filters.departments.join(","));
    if (filters.salaryMin) params.set("salaryMin", filters.salaryMin);
    if (filters.salaryMax) params.set("salaryMax", filters.salaryMax);

    const newUrl = params.toString() ? `${window.location.pathname}?${params.toString()}` : window.location.pathname;
    window.history.replaceState({}, "", newUrl);
  }, [filters]);



  const clearAllFilters = () => {
    const emptyFilters: FilterState = {
      search: "",
      statuses: [],
      dateFrom: null,
      dateTo: null,
      departments: [],
      salaryMin: "",
      salaryMax: "",
    };
    setFilters(emptyFilters);
    setSearchInput("");
    onFiltersChange(emptyFilters);
    window.history.replaceState({}, "", window.location.pathname);
  };

  const applyDatePreset = (days: number) => {
    const dateTo = endOfDay(new Date());
    const dateFrom = startOfDay(subDays(new Date(), days));
    updateFilters({ dateFrom, dateTo });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.statuses.length) count++;
    if (filters.dateFrom || filters.dateTo) count++;
    if (filters.departments.length) count++;
    if (filters.salaryMin || filters.salaryMax) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Card className="mb-4">
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Filter className="w-4 h-4" />
                <CardTitle className="text-sm">Filters</CardTitle>
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {activeFilterCount} active
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">
                  {filteredCount} of {applicationCount} applications
                </span>
                {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-3 w-3 text-muted-foreground" />
                <Input
                  placeholder="Search job titles, notes, departments..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="pl-7 text-xs"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Status</Label>
              <div className="grid grid-cols-2 gap-2">
                {STAGE_OPTIONS.map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={status}
                      checked={filters.statuses.includes(status)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFilters({ statuses: [...filters.statuses, status] });
                        } else {
                          updateFilters({ statuses: filters.statuses.filter(s => s !== status) });
                        }
                      }}
                    />
                    <Label htmlFor={status} className="text-xs">{status}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Application Date</Label>
              <div className="flex gap-2 mb-2">
                {DATE_PRESETS.map((preset) => (
                  <Button
                    key={preset.days}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => applyDatePreset(preset.days)}
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-start text-left font-normal text-xs",
                        !filters.dateFrom && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-3 w-3" />
                      {filters.dateFrom ? format(filters.dateFrom, "MMM dd, yyyy") : "From date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.dateFrom || undefined}
                      onSelect={(date) => updateFilters({ dateFrom: date || null })}
                    />
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-start text-left font-normal text-xs",
                        !filters.dateTo && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-3 w-3" />
                      {filters.dateTo ? format(filters.dateTo, "MMM dd, yyyy") : "To date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.dateTo || undefined}
                      onSelect={(date) => updateFilters({ dateTo: date || null })}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Department Filter */}
            {availableDepartments.length > 0 && (
              <div className="space-y-2">
                <Label className="text-xs font-medium">Department</Label>
                <div className="space-y-1">
                  {availableDepartments.map((dept) => (
                    <div key={dept} className="flex items-center space-x-2">
                      <Checkbox
                        id={dept}
                        checked={filters.departments.includes(dept)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            updateFilters({ departments: [...filters.departments, dept] });
                          } else {
                            updateFilters({ departments: filters.departments.filter(d => d !== dept) });
                          }
                        }}
                      />
                      <Label htmlFor={dept} className="text-xs">{dept}</Label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Salary Range */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Salary Range</Label>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  placeholder="Min salary"
                  value={filters.salaryMin}
                  onChange={(e) => updateFilters({ salaryMin: e.target.value })}
                  className="text-xs"
                  type="number"
                />
                <Input
                  placeholder="Max salary"
                  value={filters.salaryMax}
                  onChange={(e) => updateFilters({ salaryMax: e.target.value })}
                  className="text-xs"
                  type="number"
                />
              </div>
            </div>

            {/* Clear Filters */}
            {activeFilterCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                className="w-full text-xs"
              >
                <X className="w-3 h-3 mr-1" />
                Clear All Filters
              </Button>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
