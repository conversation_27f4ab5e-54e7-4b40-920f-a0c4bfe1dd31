import { useDraggable } from '@dnd-kit/core';
import { JobMatchCard } from './JobMatchCard';

interface DraggableJobCardProps {
  job: {
    id: string;
    title: string;
    company: string;
    location: string;
    match: number;
    status: string;
  };
  onClick: () => void;
}

export function DraggableJobCard({ job, onClick }: DraggableJobCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: job.id,
    data: {
      type: 'job',
      job: job,
    },
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 'auto',
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`cursor-grab active:cursor-grabbing ${isDragging ? 'shadow-lg' : ''}`}
    >
      <JobMatchCard
        job={job}
        onClick={onClick}
      />
    </div>
  );
}
