import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useState, useEffect, useMemo } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow, isAfter, isBefore, startOfDay, endOfDay } from "date-fns";
import { Clock, MapPin, DollarSign, Briefcase, ChevronDown, ArrowRight, Edit3, Save, X, MessageSquare } from "lucide-react";
import { FilterPanel } from "./FilterPanel";

interface PipelineApplication {
  id: string;
  job_id: string;
  job_title: string;
  job_department?: string;
  job_location?: string;
  job_salary_range?: string;
  job_type?: string;
  stage: string;
  applied_at: string;
  created_at: string;
  updated_at: string;
  rating?: number;
  notes?: string;
}

interface ApplicationPipelineProps {
  candidateId: string;
  onApplicationUpdate?: () => void;
}

const STAGE_COLORS = {
  "Applied": "bg-blue-500",
  "Phone Screen": "bg-yellow-500",
  "Interview": "bg-orange-500",
  "Final Round": "bg-purple-500",
  "Offer": "bg-green-500",
  "Rejected": "bg-red-500",
  "Withdrawn": "bg-gray-500"
};

const STAGE_ORDER = ["Applied", "Phone Screen", "Interview", "Final Round", "Offer"];
const TERMINAL_STAGES = ["Rejected", "Withdrawn"];
const ALL_STAGES = [...STAGE_ORDER, ...TERMINAL_STAGES];

interface FilterState {
  search: string;
  statuses: string[];
  dateFrom: Date | null;
  dateTo: Date | null;
  departments: string[];
  salaryMin: string;
  salaryMax: string;
}

export function ApplicationPipeline({ candidateId, onApplicationUpdate }: ApplicationPipelineProps) {
  const [applications, setApplications] = useState<PipelineApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingNotes, setEditingNotes] = useState<string | null>(null);
  const [noteText, setNoteText] = useState<string>("");
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    statuses: [],
    dateFrom: null,
    dateTo: null,
    departments: [],
    salaryMin: "",
    salaryMax: "",
  });
  const { user } = useAuth();
  const { toast } = useToast();

  // Bulk operations state
  const [selectedApplications, setSelectedApplications] = useState<Set<string>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);

  useEffect(() => {
    fetchApplications();
  }, [candidateId, user]);

  const fetchApplications = async () => {
    if (!user || !candidateId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("pipeline_candidates")
        .select(`
          id,
          job_id,
          stage,
          applied_at,
          created_at,
          updated_at,
          rating,
          notes,
          jobs (
            title,
            salary_range,
            departments (
              name
            ),
            locations (
              name
            ),
            job_types (
              name
            )
          )
        `)
        .eq("candidate_id", candidateId)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching applications:", error);
        return;
      }

      // Transform the data to flatten job information
      const transformedData = (data || []).map((app: any) => ({
        id: app.id,
        job_id: app.job_id,
        job_title: app.jobs?.title || "Unknown Job",
        job_department: app.jobs?.departments?.[0]?.name,
        job_location: app.jobs?.locations?.[0]?.name,
        job_salary_range: app.jobs?.salary_range,
        job_type: app.jobs?.job_types?.[0]?.name,
        stage: app.stage,
        applied_at: app.applied_at,
        created_at: app.created_at,
        updated_at: app.updated_at,
        rating: app.rating,
        notes: app.notes
      }));

      setApplications(transformedData);
    } catch (error) {
      console.error("Error in fetchApplications:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStageColor = (stage: string) => {
    return STAGE_COLORS[stage as keyof typeof STAGE_COLORS] || "bg-gray-500";
  };

  const getStageProgress = (stage: string) => {
    const index = STAGE_ORDER.indexOf(stage);
    return index >= 0 ? ((index + 1) / STAGE_ORDER.length) * 100 : 0;
  };

  const updateApplicationStage = async (applicationId: string, newStage: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from("pipeline_candidates")
        .update({
          stage: newStage,
          updated_at: new Date().toISOString()
        })
        .eq("id", applicationId)
        .eq("user_id", user.id);

      if (error) {
        throw error;
      }

      // Update local state
      setApplications(prev => prev.map(app =>
        app.id === applicationId
          ? { ...app, stage: newStage, updated_at: new Date().toISOString() }
          : app
      ));

      toast({
        title: "Status Updated",
        description: `Application status changed to ${newStage}.`,
      });

      // Notify parent component
      if (onApplicationUpdate) {
        onApplicationUpdate();
      }
    } catch (error) {
      console.error("Error updating application stage:", error);
      toast({
        title: "Error",
        description: "Failed to update application status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getNextStage = (currentStage: string) => {
    const currentIndex = STAGE_ORDER.indexOf(currentStage);
    if (currentIndex >= 0 && currentIndex < STAGE_ORDER.length - 1) {
      return STAGE_ORDER[currentIndex + 1];
    }
    return null;
  };

  const getAvailableStages = (currentStage: string) => {
    // Always allow moving to terminal stages
    const availableStages = [...TERMINAL_STAGES];

    // Add progression stages
    const currentIndex = STAGE_ORDER.indexOf(currentStage);
    if (currentIndex >= 0) {
      // Can move to next stages in the pipeline
      for (let i = currentIndex + 1; i < STAGE_ORDER.length; i++) {
        availableStages.push(STAGE_ORDER[i]);
      }
      // Can also move back to previous stages
      for (let i = 0; i < currentIndex; i++) {
        availableStages.push(STAGE_ORDER[i]);
      }
    }

    return availableStages.filter(stage => stage !== currentStage);
  };

  const startEditingNotes = (applicationId: string, currentNotes: string) => {
    setEditingNotes(applicationId);
    setNoteText(currentNotes || "");
  };

  const cancelEditingNotes = () => {
    setEditingNotes(null);
    setNoteText("");
  };

  const updateApplicationNotes = async (applicationId: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from("pipeline_candidates")
        .update({
          notes: noteText,
          updated_at: new Date().toISOString()
        })
        .eq("id", applicationId)
        .eq("user_id", user.id);

      if (error) {
        throw error;
      }

      // Update local state
      setApplications(prev => prev.map(app =>
        app.id === applicationId
          ? { ...app, notes: noteText, updated_at: new Date().toISOString() }
          : app
      ));

      setEditingNotes(null);
      setNoteText("");

      toast({
        title: "Notes Updated",
        description: "Application notes have been saved successfully.",
      });

      // Notify parent component
      if (onApplicationUpdate) {
        onApplicationUpdate();
      }
    } catch (error) {
      console.error("Error updating application notes:", error);
      toast({
        title: "Error",
        description: "Failed to update notes. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Bulk operations functions
  const handleSelectApplication = (applicationId: string, checked: boolean) => {
    const newSelected = new Set(selectedApplications);
    if (checked) {
      newSelected.add(applicationId);
    } else {
      newSelected.delete(applicationId);
    }
    setSelectedApplications(newSelected);
    setShowBulkActions(newSelected.size > 0);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(filteredApplications.map(app => app.id));
      setSelectedApplications(allIds);
      setShowBulkActions(true);
    } else {
      setSelectedApplications(new Set());
      setShowBulkActions(false);
    }
  };

  const handleBulkStatusUpdate = async (newStage: string) => {
    if (selectedApplications.size === 0) return;

    try {
      // Update each application individually to avoid constraint issues
      const updatePromises = Array.from(selectedApplications).map(async (id) => {
        const { error } = await supabase
          .from("pipeline_candidates")
          .update({
            stage: newStage,
            updated_at: new Date().toISOString()
          })
          .eq('id', id);

        if (error) throw error;
        return id;
      });

      await Promise.all(updatePromises);

      toast({
        title: "Bulk Update Successful",
        description: `Updated ${selectedApplications.size} applications to ${newStage}.`,
      });

      // Clear selections and refresh
      setSelectedApplications(new Set());
      setShowBulkActions(false);
      fetchApplications();
    } catch (error) {
      console.error("Error updating applications:", error);
      toast({
        title: "Update Failed",
        description: "Failed to update applications. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleBulkNotesUpdate = async (notes: string, mode: 'append' | 'replace') => {
    if (selectedApplications.size === 0) return;

    try {
      // Update each application individually to avoid constraint issues
      const updatePromises = Array.from(selectedApplications).map(async (id) => {
        const app = applications.find(a => a.id === id);
        if (!app) return;

        let newNotes = notes;
        if (mode === 'append' && app.notes) {
          newNotes = `${app.notes}\n\n${notes}`;
        }

        const { error } = await supabase
          .from("pipeline_candidates")
          .update({
            notes: newNotes,
            updated_at: new Date().toISOString()
          })
          .eq('id', id);

        if (error) throw error;
        return id;
      });

      await Promise.all(updatePromises);

      toast({
        title: "Bulk Notes Update Successful",
        description: `Updated notes for ${selectedApplications.size} applications.`,
      });

      // Clear selections and refresh
      setSelectedApplications(new Set());
      setShowBulkActions(false);
      fetchApplications();
    } catch (error) {
      console.error("Error updating notes:", error);
      toast({
        title: "Update Failed",
        description: "Failed to update notes. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Filter applications based on current filters
  const filteredApplications = useMemo(() => {
    return applications.filter(app => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const matchesSearch =
          app.job_title.toLowerCase().includes(searchTerm) ||
          (app.notes && app.notes.toLowerCase().includes(searchTerm)) ||
          (app.job_department && app.job_department.toLowerCase().includes(searchTerm));
        if (!matchesSearch) return false;
      }

      // Status filter
      if (filters.statuses.length > 0 && !filters.statuses.includes(app.stage)) {
        return false;
      }

      // Date range filter
      if (filters.dateFrom || filters.dateTo) {
        const appDate = new Date(app.applied_at);
        if (filters.dateFrom && isBefore(appDate, startOfDay(filters.dateFrom))) {
          return false;
        }
        if (filters.dateTo && isAfter(appDate, endOfDay(filters.dateTo))) {
          return false;
        }
      }

      // Department filter
      if (filters.departments.length > 0 && app.job_department) {
        if (!filters.departments.includes(app.job_department)) {
          return false;
        }
      }

      // Salary filter
      if (filters.salaryMin || filters.salaryMax) {
        if (app.job_salary_range) {
          // Extract salary numbers from range like "$120,000 - $150,000"
          const salaryMatch = app.job_salary_range.match(/\$?([\d,]+)\s*-\s*\$?([\d,]+)/);
          if (salaryMatch) {
            const minSalary = parseInt(salaryMatch[1].replace(/,/g, ''));
            const maxSalary = parseInt(salaryMatch[2].replace(/,/g, ''));

            if (filters.salaryMin && maxSalary < parseInt(filters.salaryMin)) {
              return false;
            }
            if (filters.salaryMax && minSalary > parseInt(filters.salaryMax)) {
              return false;
            }
          }
        }
      }

      return true;
    });
  }, [applications, filters]);

  // Get available departments for filter
  const availableDepartments = useMemo(() => {
    const departments = applications
      .map(app => app.job_department)
      .filter((dept): dept is string => Boolean(dept));
    return Array.from(new Set(departments)).sort();
  }, [applications]);

  const handleFiltersChange = (newFilters: FilterState) => {
    setFilters(newFilters);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Application Pipeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filter Panel */}
      <FilterPanel
        onFiltersChange={handleFiltersChange}
        availableDepartments={availableDepartments}
        applicationCount={applications.length}
        filteredCount={filteredApplications.length}
      />

      {/* Application Pipeline */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Application Pipeline</CardTitle>
            <Badge variant="outline" className="font-normal">
              {filteredApplications.length} of {applications.length} applications
            </Badge>
          </div>
        </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {/* Bulk Actions Header */}
            {filteredApplications.length > 0 && (
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={selectedApplications.size === filteredApplications.length && filteredApplications.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm font-medium">
                    {selectedApplications.size > 0
                      ? `${selectedApplications.size} of ${filteredApplications.length} selected`
                      : "Select all"
                    }
                  </span>
                </div>

                {showBulkActions && (
                  <div className="flex items-center gap-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          Bulk Actions <ChevronDown className="ml-1 h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleBulkStatusUpdate("Applied")}>
                          Move to Applied
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkStatusUpdate("Phone Screen")}>
                          Move to Phone Screen
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkStatusUpdate("Interview")}>
                          Move to Interview
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkStatusUpdate("Final Round")}>
                          Move to Final Round
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkStatusUpdate("Offer")}>
                          Move to Offer
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkStatusUpdate("Rejected")}>
                          Move to Rejected
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkStatusUpdate("Withdrawn")}>
                          Move to Withdrawn
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const notes = prompt("Enter notes to add to selected applications:");
                        if (notes) {
                          const mode = confirm("Append to existing notes? (Cancel to replace)") ? 'append' : 'replace';
                          handleBulkNotesUpdate(notes, mode);
                        }
                      }}
                    >
                      Add Notes
                    </Button>
                  </div>
                )}
              </div>
            )}

            {filteredApplications.length === 0 ? (
              <div className="text-center py-8">
                {applications.length === 0 ? (
                  <>
                    <p className="text-muted-foreground mb-2">No applications yet</p>
                    <p className="text-sm text-muted-foreground">
                      Drag jobs from the left panel or use "Submit Candidate" to add applications
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-muted-foreground mb-2">No applications match your filters</p>
                    <p className="text-sm text-muted-foreground">
                      Try adjusting your filters to see more results
                    </p>
                  </>
                )}
              </div>
            ) : (
              filteredApplications.map((app) => (
                <div
                  key={app.id}
                  className="border rounded-lg p-4 hover:bg-accent/50 transition-colors relative"
                >
                  {/* Selection checkbox positioned in top-left corner */}
                  <Checkbox
                    checked={selectedApplications.has(app.id)}
                    onCheckedChange={(checked) => handleSelectApplication(app.id, checked as boolean)}
                    className="absolute top-2 left-2 z-10"
                  />

                  <div className="flex items-start justify-between mb-3 ml-6">
                    <div>
                      <h4 className="font-medium text-sm">{app.job_title}</h4>
                      {app.job_department && (
                        <p className="text-xs text-muted-foreground">
                          {app.job_department}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        className={`text-white text-xs ${getStageColor(app.stage)}`}
                      >
                        {app.stage}
                      </Badge>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <ChevronDown className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {getAvailableStages(app.stage).map((stage) => (
                            <DropdownMenuItem
                              key={stage}
                              onClick={() => updateApplicationStage(app.id, stage)}
                              className="text-xs"
                            >
                              <ArrowRight className="w-3 h-3 mr-2" />
                              Move to {stage}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 text-xs text-muted-foreground mb-3">
                    {app.job_location && (
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span>{app.job_location}</span>
                      </div>
                    )}
                    {app.job_salary_range && (
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-3 h-3" />
                        <span>{app.job_salary_range}</span>
                      </div>
                    )}
                    {app.job_type && (
                      <div className="flex items-center gap-1">
                        <Briefcase className="w-3 h-3" />
                        <span>{app.job_type}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      <span>
                        Applied {formatDistanceToNow(new Date(app.created_at), { addSuffix: true })}
                      </span>
                    </div>
                    <div className="w-16 bg-gray-200 rounded-full h-1.5">
                      <div 
                        className={`h-1.5 rounded-full ${getStageColor(app.stage)}`}
                        style={{ width: `${getStageProgress(app.stage)}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Enhanced Notes Section */}
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-1">
                        <MessageSquare className="w-3 h-3 text-muted-foreground" />
                        <span className="text-xs font-medium text-muted-foreground">Notes</span>
                      </div>
                      {editingNotes !== app.id && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => startEditingNotes(app.id, app.notes || "")}
                        >
                          <Edit3 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>

                    {editingNotes === app.id ? (
                      <div className="space-y-2">
                        <Textarea
                          value={noteText}
                          onChange={(e) => setNoteText(e.target.value)}
                          placeholder="Add notes about this application..."
                          className="text-xs min-h-[60px] resize-none"
                        />
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            className="h-6 text-xs"
                            onClick={() => updateApplicationNotes(app.id)}
                          >
                            <Save className="w-3 h-3 mr-1" />
                            Save
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 text-xs"
                            onClick={cancelEditingNotes}
                          >
                            <X className="w-3 h-3 mr-1" />
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-xs text-muted-foreground">
                        {app.notes ? (
                          <p className="whitespace-pre-wrap">{app.notes}</p>
                        ) : (
                          <p className="italic">No notes added yet</p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
    </div>
  );
}
