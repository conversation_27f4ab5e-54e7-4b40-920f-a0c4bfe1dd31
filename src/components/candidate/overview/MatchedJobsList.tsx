import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState, useEffect } from "react";
import { JobMatchCard } from "./JobMatchCard";
import { DraggableJobCard } from "./DraggableJobCard";
import { JobDetailsDialog } from "./JobDetailsDialog";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface JobMatch {
  id: string;
  title: string;
  company?: string;
  location: string;
  salary_range?: string;
  match: number;
  description: string;
  requirements?: string[];
  benefits?: string[];
  department: string;
  job_type: string;
  experience_required?: string;
  is_urgent?: boolean;
  status: string;
}

interface MatchedJobsListProps {
  jobs?: JobMatch[];
  candidate?: any; // Candidate data for matching
  onJobSubmitted?: () => void; // Callback when a job is submitted
  enableDragAndDrop?: boolean; // Enable drag and drop functionality
}

// Enhanced job matching algorithm
function calculateJobMatch(job: any, candidate: any): number {
  if (!candidate) {
    // Fallback to basic scoring if no candidate data
    return Math.floor(Math.random() * 30) + 40; // 40-70 range for unknown candidates
  }

  let score = 0;
  const maxScore = 100;

  // 1. Role/Title Matching (30 points max)
  const roleScore = calculateRoleMatch(job.title, candidate.role);
  score += roleScore * 0.3;

  // 2. Skills Matching (40 points max)
  const skillsScore = calculateSkillsMatch(job, candidate.normalized_skills || []);
  score += skillsScore * 0.4;

  // 3. Experience Level Matching (20 points max)
  const experienceScore = calculateExperienceMatch(job.experience_required, candidate.experience);
  score += experienceScore * 0.2;

  // 4. Test Job Penalty (10 points deduction max)
  const testJobPenalty = calculateTestJobPenalty(job.title, job.description);
  score -= testJobPenalty;

  // 5. Urgency Bonus (5 points max)
  if (job.is_urgent) {
    score += 5;
  }

  // Ensure score is between 0-100
  return Math.min(maxScore, Math.max(0, Math.round(score)));
}

// Calculate role/title matching score (0-100)
function calculateRoleMatch(jobTitle: string, candidateRole: string): number {
  if (!jobTitle || !candidateRole) return 20; // Default score

  const jobTitleLower = jobTitle.toLowerCase();
  const candidateRoleLower = candidateRole.toLowerCase();

  // Exact match
  if (jobTitleLower === candidateRoleLower) return 100;

  // High relevance matches
  const frontendKeywords = ['frontend', 'front-end', 'react', 'javascript', 'ui', 'web'];
  const backendKeywords = ['backend', 'back-end', 'api', 'server', 'node', 'python'];
  const fullstackKeywords = ['fullstack', 'full-stack', 'full stack'];
  const seniorKeywords = ['senior', 'lead', 'principal', 'staff'];

  let roleScore = 20; // Base score

  // Check for role type alignment
  const candidateIsFrontend = frontendKeywords.some(keyword => candidateRoleLower.includes(keyword));
  const jobIsFrontend = frontendKeywords.some(keyword => jobTitleLower.includes(keyword));
  const candidateIsBackend = backendKeywords.some(keyword => candidateRoleLower.includes(keyword));
  const jobIsBackend = backendKeywords.some(keyword => jobTitleLower.includes(keyword));
  const jobIsFullstack = fullstackKeywords.some(keyword => jobTitleLower.includes(keyword));

  if (candidateIsFrontend && jobIsFrontend) roleScore += 70; // 90 total
  else if (candidateIsBackend && jobIsBackend) roleScore += 70; // 90 total
  else if (candidateIsFrontend && jobIsFullstack) roleScore += 50; // 70 total
  else if (candidateIsBackend && jobIsFullstack) roleScore += 50; // 70 total
  else if ((candidateIsFrontend || candidateIsBackend) && jobTitleLower.includes('developer')) roleScore += 30; // 50 total

  // Check for seniority level alignment
  const candidateIsSenior = seniorKeywords.some(keyword => candidateRoleLower.includes(keyword));
  const jobIsSenior = seniorKeywords.some(keyword => jobTitleLower.includes(keyword));

  if (candidateIsSenior && jobIsSenior) roleScore += 15;
  else if (!candidateIsSenior && !jobIsSenior) roleScore += 10;

  return Math.min(100, roleScore);
}

// Calculate skills matching score (0-100)
function calculateSkillsMatch(job: any, candidateSkills: any[]): number {
  if (!candidateSkills || candidateSkills.length === 0) return 30; // Default score

  const jobRequirements = job.requirements || [];
  const jobDescription = (job.description || '').toLowerCase();
  const jobTitle = (job.title || '').toLowerCase();

  // Extract skills from candidate data
  const candidateSkillNames = candidateSkills.map(skill => skill.name.toLowerCase());
  const candidateSkillLevels = candidateSkills.reduce((acc, skill) => {
    acc[skill.name.toLowerCase()] = skill.level;
    return acc;
  }, {} as Record<string, string>);

  let skillScore = 0;
  let totalPossibleScore = 0;

  // Check explicit requirements
  if (jobRequirements.length > 0) {
    jobRequirements.forEach((requirement: string) => {
      const reqLower = requirement.toLowerCase();
      totalPossibleScore += 10;

      const matchingSkill = candidateSkillNames.find(skill =>
        skill.includes(reqLower) || reqLower.includes(skill)
      );

      if (matchingSkill) {
        const skillLevel = candidateSkillLevels[matchingSkill];
        if (skillLevel === 'expert') skillScore += 10;
        else if (skillLevel === 'intermediate') skillScore += 8;
        else skillScore += 6;
      }
    });
  }

  // Check for common tech skills mentioned in job title/description
  const commonTechSkills = ['react', 'typescript', 'javascript', 'node', 'python', 'java', 'angular', 'vue', 'next', 'tailwind'];
  commonTechSkills.forEach(techSkill => {
    if (jobTitle.includes(techSkill) || jobDescription.includes(techSkill)) {
      // Give higher weight to React and TypeScript for frontend developers
      const skillWeight = (techSkill === 'react' || techSkill === 'typescript') ? 12 : 8;
      totalPossibleScore += skillWeight;

      const matchingSkill = candidateSkillNames.find(skill =>
        skill.includes(techSkill) || techSkill.includes(skill)
      );

      if (matchingSkill) {
        const skillLevel = candidateSkillLevels[matchingSkill];
        if (skillLevel === 'expert') skillScore += skillWeight;
        else if (skillLevel === 'intermediate') skillScore += skillWeight * 0.75;
        else skillScore += skillWeight * 0.5;
      }
    }
  });

  // If no specific requirements found, give base score for relevant skills
  if (totalPossibleScore === 0) {
    totalPossibleScore = 50;
    skillScore = 30; // Base score for having skills
  }

  return Math.min(100, (skillScore / totalPossibleScore) * 100);
}

// Calculate experience level matching (0-100)
function calculateExperienceMatch(jobExperience: string, candidateExperience: string): number {
  if (!jobExperience || !candidateExperience) return 60; // Default score

  const jobExpLower = jobExperience.toLowerCase();
  const candidateExpLower = candidateExperience.toLowerCase();

  // Extract years from strings
  const jobYears = extractYears(jobExpLower);
  const candidateYears = extractYears(candidateExpLower);

  if (jobYears === null || candidateYears === null) return 60;

  // Perfect match
  if (candidateYears >= jobYears && candidateYears <= jobYears + 2) return 100;

  // Good match (within range)
  if (candidateYears >= jobYears - 1 && candidateYears <= jobYears + 4) return 80;

  // Acceptable match
  if (candidateYears >= jobYears - 2 && candidateYears <= jobYears + 6) return 60;

  // Under-qualified
  if (candidateYears < jobYears - 2) return 30;

  // Over-qualified (might be less interested)
  return 40;
}

// Extract years from experience string
function extractYears(experienceStr: string): number | null {
  const yearMatch = experienceStr.match(/(\d+)[\s-]*(?:years?|yrs?)/i);
  return yearMatch ? parseInt(yearMatch[1]) : null;
}

// Calculate penalty for test jobs (0-30)
function calculateTestJobPenalty(jobTitle: string, jobDescription: string): number {
  const titleLower = (jobTitle || '').toLowerCase();
  const descLower = (jobDescription || '').toLowerCase();

  const testKeywords = ['test', 'testing', 'lol', 'temp', 'sample', 'demo', 'placeholder'];

  let penalty = 0;
  testKeywords.forEach(keyword => {
    if (titleLower.includes(keyword)) penalty += 15;
    if (descLower.includes(keyword)) penalty += 5;
  });

  return Math.min(30, penalty);
}

export function MatchedJobsList({ jobs, candidate, onJobSubmitted, enableDragAndDrop = false }: MatchedJobsListProps) {
  const [selectedJob, setSelectedJob] = useState<JobMatch | null>(null);
  const [matchedJobs, setMatchedJobs] = useState<JobMatch[]>([]);
  const { user } = useAuth();

  useEffect(() => {
    const fetchJobs = async () => {
      if (!user) return;

      try {
        // Fetch jobs from database
        const { data: jobsData, error } = await supabase
          .from("jobs")
          .select("*")
          .eq("user_id", user.id)
          .eq("is_active", true);

        if (error) {
          console.error("Error fetching jobs:", error);
          return;
        }

        if (jobsData) {
          // Transform jobs to include intelligent match scores
          const jobsWithMatches: JobMatch[] = jobsData.map((job) => ({
            id: job.id,
            title: job.title,
            company: "Your Company", // Could be made configurable
            location: job.location,
            salary_range: job.salary_range,
            match: calculateJobMatch(job, candidate),
            description: job.description,
            requirements: job.requirements,
            benefits: job.benefits,
            department: job.department,
            job_type: job.job_type,
            experience_required: job.experience_required,
            is_urgent: job.is_urgent,
            status: job.is_urgent ? "Urgent" : "Active",
          }));

          // Sort by match score (highest first)
          setMatchedJobs(jobsWithMatches.sort((a, b) => b.match - a.match));
        }
      } catch (error) {
        console.error("Error in fetchJobs:", error);
      }
    };

    fetchJobs();
  }, [user, candidate]);

  const jobsToShow = jobs || matchedJobs;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Matched Jobs</CardTitle>
          <Badge variant="outline" className="font-normal">
            {jobsToShow.length} matches
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {jobsToShow.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">
                No matching jobs found
              </p>
            ) : (
              jobsToShow.map((job) => {
                const jobCardProps = {
                  id: job.id,
                  title: job.title,
                  company: job.company || "Your Company",
                  location: job.location,
                  match: job.match,
                  status: job.status,
                };

                return (
                  <Dialog
                    key={job.id}
                    open={selectedJob?.id === job.id}
                    onOpenChange={() => setSelectedJob(null)}
                  >
                    {enableDragAndDrop ? (
                      <DraggableJobCard
                        job={jobCardProps}
                        onClick={() => setSelectedJob(job)}
                      />
                    ) : (
                      <JobMatchCard
                        job={jobCardProps}
                        onClick={() => setSelectedJob(job)}
                      />
                    )}
                    {selectedJob?.id === job.id && (
                      <JobDetailsDialog
                        job={job}
                        onClose={() => setSelectedJob(null)}
                        onJobSubmitted={onJobSubmitted}
                      />
                    )}
                  </Dialog>
                );
              })
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
