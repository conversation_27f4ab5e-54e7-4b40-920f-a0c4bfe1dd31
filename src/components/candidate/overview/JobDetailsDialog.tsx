import { Button } from "@/components/ui/button";
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Building,
  MapPin,
  Briefcase,
  Calendar,
  DollarSign,
  CheckCircle2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useState } from "react";

interface JobDetailsDialogProps {
  job: any;
  onClose: () => void;
  onJobSubmitted?: () => void;
}

export function JobDetailsDialog({ job, onClose, onJobSubmitted }: JobDetailsDialogProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleApply = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to submit applications.",
        variant: "destructive",
      });
      return;
    }

    // We need the candidate ID - this should be passed as a prop
    // For now, let's get it from the URL or context
    const candidateId = window.location.pathname.split('/').pop();

    if (!candidateId) {
      toast({
        title: "Error",
        description: "Could not identify candidate.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if application already exists
      const { data: existingApp, error: checkError } = await supabase
        .from("pipeline_candidates")
        .select("id")
        .eq("candidate_id", candidateId)
        .eq("job_id", job.id)
        .eq("user_id", user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw checkError;
      }

      if (existingApp) {
        toast({
          title: "Already Applied",
          description: "This candidate has already been submitted for this position.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      // Create new application
      // Convert match percentage (0-100) to rating (0-5)
      const rating = Math.min(5, Math.max(0, Math.round((job.match || 0) / 20)));

      const { error: insertError } = await supabase
        .from("pipeline_candidates")
        .insert({
          candidate_id: candidateId,
          job_id: job.id,
          user_id: user.id,
          stage: "Applied",
          applied_at: new Date().toISOString(),
          source: "manual",
          rating: rating
        });

      if (insertError) {
        throw insertError;
      }

      toast({
        title: "Application Submitted",
        description: "The candidate has been submitted for this position.",
      });

      // Notify parent components to refresh
      if (onJobSubmitted) {
        onJobSubmitted();
      }

      onClose();
    } catch (error) {
      console.error("Error submitting application:", error);
      toast({
        title: "Error",
        description: "Failed to submit application. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getMatchColor = (match: number) => {
    if (match >= 90) return "text-green-500";
    if (match >= 70) return "text-yellow-500";
    return "text-orange-500";
  };

  return (
    <DialogContent className="max-w-2xl">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          {job.title}
          <Badge variant="secondary" className={getMatchColor(job.match)}>
            {job.match}% Match
          </Badge>
        </DialogTitle>
        <DialogDescription>
          Detailed job information and requirements
        </DialogDescription>
      </DialogHeader>
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium">Company Details</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <Building className="w-4 h-4" />
                <span>{job.company}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                <span>{job.location_name || job.location}</span>
              </div>
              <div className="flex items-center gap-2">
                <Briefcase className="h-4 w-4" />
                <span>{job.department_name || job.department}</span>
              </div>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Job Overview</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4" />
                <span>{job.salary_range || "Competitive salary"}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>Experience: {job.experience_required || "Not specified"}</span>
              </div>
            </div>
          </div>
        </div>

        {job.description && (
          <div className="space-y-2">
            <h4 className="font-medium">Job Description</h4>
            <p className="text-sm text-muted-foreground">{job.description}</p>
          </div>
        )}

        <div className="space-y-2">
          <h4 className="font-medium">Key Requirements</h4>
          <ul className="grid grid-cols-2 gap-2">
            {(job.requirements || ["Strong communication skills", "Team collaboration", "Problem solving abilities"]).map((req: string, index: number) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <CheckCircle2 className="w-4 h-4 text-green-500" />
                {req}
              </li>
            ))}
          </ul>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Benefits</h4>
          <ul className="grid grid-cols-2 gap-2">
            {(job.benefits || ["Health insurance", "Flexible work hours", "Professional development", "Competitive salary"]).map((benefit: string, index: number) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <CheckCircle2 className="w-4 h-4 text-green-500" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Close
          </Button>
          <Button onClick={handleApply} disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : "Submit Candidate"}
          </Button>
        </div>
      </div>
    </DialogContent>
  );
}
