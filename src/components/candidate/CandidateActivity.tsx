import { useState, useEffect } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { CandidateType } from "@/types/candidate";
import { EnhancedTabs, TabPanel } from "@/design-system";
import { RecentActivity } from "./activity/RecentActivity";
import { Communications } from "./activity/Communications";
import { Meetings } from "./activity/Meetings";
import { Documents } from "./activity/Documents";
import { Activity, MessageSquare, Calendar, FileText } from "lucide-react";

interface CandidateActivityProps {
  candidate: CandidateType;
}

export function CandidateActivity({ candidate }: CandidateActivityProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { id } = useParams();
  
  // Parse the sub-tab from the URL path
  const getSubTabFromUrl = () => {
    const pathSegments = location.pathname.split('/');
    const activityIndex = pathSegments.findIndex(segment => segment === 'activity');
    const subTab = pathSegments[activityIndex + 1];
    
    const validSubTabs = ["all", "communications", "meetings", "documents"];
    
    // Return the sub-tab if it's valid, otherwise default to "all"
    return validSubTabs.includes(subTab) ? subTab : "all";
  };
  
  const [activeTab, setActiveTab] = useState(getSubTabFromUrl());
  
  // Update activeTab when URL changes
  useEffect(() => {
    const subTabFromUrl = getSubTabFromUrl();
    setActiveTab(subTabFromUrl);
  }, [location.pathname]);
  
  // Handle sub-tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Navigate to the new sub-tab URL
    navigate(`/candidates/${id}/activity/${value}`, { replace: true });
  };
  
  const tabs = [
    { value: "all", label: "All Activity", icon: Activity },
    { value: "communications", label: "Communications", icon: MessageSquare },
    { value: "meetings", label: "Meetings", icon: Calendar },
    { value: "documents", label: "Documents", icon: FileText },
  ];

  return (
    <div className="space-y-6">
      <EnhancedTabs 
        tabs={tabs} 
        value={activeTab} 
        onValueChange={handleTabChange}
        variant="navigation"
        indicatorStyle="underline"
        size="md"
      >
        <TabPanel value="all">
          <RecentActivity candidateId={candidate.id} showAll={true} />
        </TabPanel>

        <TabPanel value="communications">
          <Communications candidateId={candidate.id} />
        </TabPanel>

        <TabPanel value="meetings">
          <Meetings candidateId={candidate.id} />
        </TabPanel>

        <TabPanel value="documents">
          <Documents candidateId={candidate.id} />
        </TabPanel>
      </EnhancedTabs>
    </div>
  );
}
