import React, { Suspense, lazy, ComponentType, useMemo } from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LazyTabPanelProps {
  /**
   * Function that returns a dynamic import promise
   * Example: () => import('./MyComponent')
   */
  loader: () => Promise<{ default: ComponentType<any> }>;
  /**
   * Props to pass to the lazy loaded component
   */
  componentProps?: Record<string, any>;
  /**
   * Custom loading component
   */
  fallback?: React.ReactNode;
  /**
   * Additional class names for the wrapper
   */
  className?: string;
  /**
   * Whether to show a full-page loader or inline
   */
  fullHeight?: boolean;
}

/**
 * Default loading fallback component
 */
const DefaultFallback: React.FC<{ fullHeight?: boolean; className?: string }> = ({ 
  fullHeight = false, 
  className 
}) => (
  <div 
    className={cn(
      "flex items-center justify-center",
      fullHeight ? "min-h-[400px]" : "py-8",
      className
    )}
  >
    <div className="flex flex-col items-center gap-3">
      <Loader2 className="h-8 w-8 animate-spin text-primary/60" />
      <p className="text-sm text-muted-foreground">Loading content...</p>
    </div>
  </div>
);

/**
 * LazyTabPanel - A wrapper component for lazy loading tab content
 * 
 * Usage:
 * ```tsx
 * <TabPanel value="analytics">
 *   <LazyTabPanel 
 *     loader={() => import('./WorkflowAnalytics')} 
 *     componentProps={{ someData: data }}
 *   />
 * </TabPanel>
 * ```
 */
export const LazyTabPanel: React.FC<LazyTabPanelProps> = ({
  loader,
  componentProps = {},
  fallback,
  className,
  fullHeight = true,
}) => {
  // Memoize the lazy component to prevent re-creating it on every render
  const LazyComponent = useMemo(() => lazy(loader), []);

  return (
    <Suspense 
      fallback={
        fallback || 
        <DefaultFallback fullHeight={fullHeight} className={className} />
      }
    >
      <div className={className}>
        <LazyComponent {...componentProps} />
      </div>
    </Suspense>
  );
};

/**
 * Hook to preload a lazy component
 * Useful for preloading tabs when hovering over tab triggers
 */
export const usePreloadTab = (
  loader: () => Promise<{ default: ComponentType<any> }>
) => {
  const preload = React.useCallback(() => {
    loader();
  }, [loader]);

  return preload;
};

/**
 * Higher-order component to wrap a component with lazy loading
 */
export const withLazyLoading = <P extends object>(
  loader: () => Promise<{ default: ComponentType<P> }>
): React.FC<P & { fallback?: React.ReactNode }> => {
  const LazyComponent = lazy(loader);
  
  return ({ fallback, ...props }) => (
    <Suspense fallback={fallback || <DefaultFallback />}>
      <LazyComponent {...(props as P)} />
    </Suspense>
  );
};
