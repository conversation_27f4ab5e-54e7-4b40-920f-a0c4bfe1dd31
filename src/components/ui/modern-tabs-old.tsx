import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

// Context for managing tab state and measurements
interface ModernTabsContextValue {
  registerTrigger: (value: string, el: HTMLElement | null) => void;
  activeValue?: string;
  getTriggerRect: (value: string) => DOMRect | null;
  direction: "forward" | "backward";
  listRect?: DOMRect;
  setListRect: (rect: DOMRect) => void;
}

const ModernTabsContext = React.createContext<ModernTabsContextValue | undefined>(undefined);

const useModernTabs = () => {
  const context = React.useContext(ModernTabsContext);
  if (!context) {
    throw new Error("useModernTabs must be used within ModernTabs");
  }
  return context;
};

// Root component that provides context
interface ModernTabsProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Root> {
  children: React.ReactNode;
}

const ModernTabs = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Root>,
  ModernTabsProps
>(({ children, value, defaultValue, onValueChange, ...props }, ref) => {
  const [triggerRects, setTriggerRects] = React.useState<Map<string, DOMRect>>(new Map());
  const [listRect, setListRect] = React.useState<DOMRect>();
  const [activeValue, setActiveValue] = React.useState(value || defaultValue);
  const [direction, setDirection] = React.useState<"forward" | "backward">("forward");
  const previousValue = React.useRef(activeValue);

  // Track value changes to determine direction
  React.useEffect(() => {
    setActiveValue(value || defaultValue);
    
    // Determine direction based on DOM position (more generic approach)
    if (previousValue.current && value && previousValue.current !== value) {
      const prevRect = triggerRects.get(previousValue.current);
      const currentRect = triggerRects.get(value);
      if (prevRect && currentRect) {
        setDirection(currentRect.left > prevRect.left ? "forward" : "backward");
      }
    }
    previousValue.current = value || defaultValue;
  }, [value, defaultValue, triggerRects]);

  const registerTrigger = React.useCallback((value: string, el: HTMLElement | null) => {
    if (el) {
      setTriggerRects(prev => new Map(prev).set(value, el));
    } else {
      setTriggerRects(prev => {
        const next = new Map(prev);
        next.delete(value);
        return next;
      });
    }
  }, []);

  const getTriggerElement = React.useCallback((value: string) => {
    return triggerRects.get(value) || null;
  }, [triggerRects]);

  const contextValue = React.useMemo(
    () => ({
      registerTrigger,
      activeValue,
      getTriggerElement,
      direction,
      listRect,
      setListRect,
    }),
    [registerTrigger, activeValue, getTriggerElement, direction, listRect]
  );

  return (
    <ModernTabsContext.Provider value={contextValue}>
      <TabsPrimitive.Root
        ref={ref}
        value={value}
        defaultValue={defaultValue}
        onValueChange={onValueChange}
        {...props}
      >
        {children}
      </TabsPrimitive.Root>
    </ModernTabsContext.Provider>
  );
});
ModernTabs.displayName = "ModernTabs";

// List component with sliding indicator
const ModernTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, children, ...props }, ref) => {
  const { activeValue, getTriggerElement, setListRect } = useModernTabs();
  const listRef = React.useRef<HTMLDivElement>(null);
  const [indicatorStyle, setIndicatorStyle] = React.useState<React.CSSProperties>({});
  const [scrollLeft, setScrollLeft] = React.useState(0);

  // Update list rect on mount and resize
  React.useEffect(() => {
    const updateListRect = () => {
      if (listRef.current) {
        setListRect(listRef.current.getBoundingClientRect());
        setScrollLeft(listRef.current.scrollLeft || 0);
      }
    };

    // Initial update with delays to ensure layout is settled
    updateListRect();
    // Multiple timeouts to catch different stages of layout
    setTimeout(updateListRect, 0);
    setTimeout(updateListRect, 50);
    setTimeout(updateListRect, 100);
    setTimeout(updateListRect, 200);
    setTimeout(updateListRect, 300);
    
    window.addEventListener("resize", updateListRect);
    
    // Listen for scroll events
    const handleScroll = () => {
      if (listRef.current) {
        setScrollLeft(listRef.current.scrollLeft || 0);
      }
    };
    
    const list = listRef.current;
    list?.addEventListener("scroll", handleScroll);

    // Use ResizeObserver if available
    const resizeObserver = typeof ResizeObserver !== "undefined" 
      ? new ResizeObserver(updateListRect) 
      : null;
    
    if (resizeObserver && list) {
      resizeObserver.observe(list);
    }

    // Also observe document body for layout changes (sidebar toggle)
    if (resizeObserver && document.body) {
      resizeObserver.observe(document.body);
    }

    // Listen for transition events which might indicate sidebar changes
    const handleTransitionEnd = () => {
      setTimeout(updateListRect, 50);
    };
    document.addEventListener("transitionend", handleTransitionEnd);
    
    // Watch for sidebar state changes via mutation observer
    const mutationObserver = new MutationObserver((mutations) => {
      // Check if any mutations affected the sidebar state
      const hasSidebarChange = mutations.some(mutation => {
        if (mutation.type === 'attributes') {
          const target = mutation.target as HTMLElement;
          // Check for data-state attribute changes (sidebar uses this)
          if (mutation.attributeName === 'data-state' && 
              (target.getAttribute('data-state') === 'expanded' || 
               target.getAttribute('data-state') === 'collapsed')) {
            return true;
          }
          // Check for other sidebar-related attributes
          return mutation.attributeName === 'data-collapsible' ||
                 mutation.attributeName === 'data-sidebar-open';
        }
        return false;
      });
      
      if (hasSidebarChange) {
        // Multiple updates to ensure we catch the right moment
        updateListRect();
        setTimeout(updateListRect, 100);
        setTimeout(updateListRect, 200);
        setTimeout(updateListRect, 300);
      }
    });
    
    // Observe the body and main layout elements for sidebar changes
    mutationObserver.observe(document.body, {
      attributes: true,
      attributeFilter: ['class', 'data-state', 'data-collapsible', 'data-sidebar-open'],
      subtree: true
    });

    return () => {
      window.removeEventListener("resize", updateListRect);
      list?.removeEventListener("scroll", handleScroll);
      document.removeEventListener("transitionend", handleTransitionEnd);
      mutationObserver.disconnect();
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [setListRect]);

  // Update indicator position
  React.useEffect(() => {
    if (activeValue && listRef.current) {
      const triggerEl = getTriggerElement(activeValue);
      if (triggerEl) {
        // Use offset properties which are relative to the parent, not viewport
        const left = triggerEl.offsetLeft;
        const top = triggerEl.offsetTop;
        const width = triggerEl.offsetWidth;
        const height = triggerEl.offsetHeight;
        
        setIndicatorStyle({
          transform: `translate(${left}px, ${top}px)`,
          width: `${width}px`,
          height: `${height}px`,
        });
      }
    }
  }, [activeValue, getTriggerElement]);

  return (
    <TabsPrimitive.List
      ref={(node) => {
        if (ref) {
          if (typeof ref === "function") ref(node);
          else ref.current = node;
        }
        if (node) {
          listRef.current = node;
        }
      }}
      className={cn(
        "relative bg-transparent overflow-visible rounded-xl items-center",
        className
      )}
      {...props}
    >
      {/* Sliding gradient indicator */}
      <div
        className="tab-indicator absolute rounded-full bg-gradient-to-r from-primary/20 to-primary/5 backdrop-blur-sm pointer-events-none"
        style={{
          ...indicatorStyle,
          left: 0,
          top: 0,
        }}
        aria-hidden="true"
      />
      {children}
    </TabsPrimitive.List>
  );
});
ModernTabsList.displayName = "ModernTabsList";

// Trigger component with icon support and animations
interface ModernTabsTriggerProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> {
  icon?: LucideIcon;
  label?: string;
  staggerIndex?: number;
}

const ModernTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  ModernTabsTriggerProps
>(({ className, icon: Icon, label, staggerIndex = 0, children, value, ...props }, ref) => {
  const { registerTrigger } = useModernTabs();
  const triggerRef = React.useRef<HTMLButtonElement>(null);

  React.useEffect(() => {
    if (value && triggerRef.current) {
      // Initial registration with delays to ensure layout is settled
      registerTrigger(value, triggerRef.current);
      setTimeout(() => {
        if (triggerRef.current) {
          registerTrigger(value, triggerRef.current);
        }
      }, 0);
      setTimeout(() => {
        if (triggerRef.current) {
          registerTrigger(value, triggerRef.current);
        }
      }, 50);
      setTimeout(() => {
        if (triggerRef.current) {
          registerTrigger(value, triggerRef.current);
        }
      }, 100);
      setTimeout(() => {
        if (triggerRef.current) {
          registerTrigger(value, triggerRef.current);
        }
      }, 200);
      setTimeout(() => {
        if (triggerRef.current) {
          registerTrigger(value, triggerRef.current);
        }
      }, 300);
      
      // Re-register on window resize or layout changes
      const handleResize = () => {
        if (triggerRef.current) {
          registerTrigger(value, triggerRef.current);
        }
      };
      
      window.addEventListener("resize", handleResize);
      
      // Use ResizeObserver to detect layout changes
      const resizeObserver = typeof ResizeObserver !== "undefined"
        ? new ResizeObserver(handleResize)
        : null;
        
      if (resizeObserver && triggerRef.current) {
        resizeObserver.observe(triggerRef.current);
      }
      
      // Watch for sidebar state changes
      const mutationObserver = new MutationObserver((mutations) => {
        const hasSidebarChange = mutations.some(mutation => {
          if (mutation.type === 'attributes') {
            const target = mutation.target as HTMLElement;
            // Check for data-state attribute changes (sidebar uses this)
            if (mutation.attributeName === 'data-state' && 
                (target.getAttribute('data-state') === 'expanded' || 
                 target.getAttribute('data-state') === 'collapsed')) {
              return true;
            }
            // Check for other sidebar-related attributes
            return mutation.attributeName === 'data-collapsible' ||
                   mutation.attributeName === 'data-sidebar-open';
          }
          return false;
        });
        
        if (hasSidebarChange) {
          // Multiple re-registers to ensure we catch the right moment
          if (triggerRef.current) {
            registerTrigger(value, triggerRef.current);
          }
          setTimeout(() => {
            if (triggerRef.current) {
              registerTrigger(value, triggerRef.current);
            }
          }, 100);
          setTimeout(() => {
            if (triggerRef.current) {
              registerTrigger(value, triggerRef.current);
            }
          }, 200);
          setTimeout(() => {
            if (triggerRef.current) {
              registerTrigger(value, triggerRef.current);
            }
          }, 300);
        }
      });
      
      mutationObserver.observe(document.body, {
        attributes: true,
        attributeFilter: ['class', 'data-state', 'data-collapsible', 'data-sidebar-open'],
        subtree: true
      });
      
      return () => {
        window.removeEventListener("resize", handleResize);
        mutationObserver.disconnect();
        if (resizeObserver) {
          resizeObserver.disconnect();
        }
        if (value) {
          registerTrigger(value, null);
        }
      };
    }
    return () => {
      if (value) {
        registerTrigger(value, null);
      }
    };
  }, [value, registerTrigger]);

  return (
    <TabsPrimitive.Trigger
      ref={(node) => {
        if (ref) {
          if (typeof ref === "function") ref(node);
          else ref.current = node;
        }
        if (node) {
          triggerRef.current = node;
        }
      }}
      value={value}
      className={cn(
        "trigger-stagger relative z-10 inline-flex items-center justify-center gap-2 px-4 py-2 rounded-full",
        "bg-transparent text-sm font-medium text-muted-foreground",
        "transition-all duration-300",
        "hover:scale-[1.02] hover:shadow-glow hover:text-foreground/80",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:pointer-events-none disabled:opacity-50",
        "data-[state=active]:text-foreground data-[state=active]:font-semibold",
        className
      )}
      style={{
        animationDelay: `${staggerIndex * 60}ms`,
      }}
      {...props}
    >
      {Icon && (
        <Icon className="w-4 h-4 transition-all duration-300 motion-safe:group-hover:-translate-y-[1px] group-hover:brightness-110 group-data-[state=active]:brightness-125" />
      )}
      {label && <span>{label}</span>}
      {!Icon && !label && children}
    </TabsPrimitive.Trigger>
  );
});
ModernTabsTrigger.displayName = "ModernTabsTrigger";

// Content component with fade/slide animations
const ModernTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, children, value, ...props }, ref) => {
  const { direction } = useModernTabs();

  return (
    <TabsPrimitive.Content
      ref={ref}
      value={value}
      forceMount
      className={cn(
        "relative transition-all duration-340 ease-out",
        "data-[state=active]:opacity-100 data-[state=active]:translate-x-0 data-[state=active]:translate-y-0",
        "data-[state=inactive]:opacity-0 data-[state=inactive]:pointer-events-none data-[state=inactive]:absolute data-[state=inactive]:inset-0",
        direction === "forward" 
          ? "data-[state=inactive]:translate-x-2" 
          : "data-[state=inactive]:-translate-x-2",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        className
      )}
      {...props}
    >
      {children}
    </TabsPrimitive.Content>
  );
});
ModernTabsContent.displayName = "ModernTabsContent";

// Export compound component
export const ModernTabsComponent = Object.assign(ModernTabs, {
  List: ModernTabsList,
  Trigger: ModernTabsTrigger,
  Content: ModernTabsContent,
});

export { ModernTabs, ModernTabsList, ModernTabsTrigger, ModernTabsContent };
