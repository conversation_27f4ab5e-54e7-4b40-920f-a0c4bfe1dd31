import React from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

/**
 * Skeleton for table/list tab content
 */
export const TabListSkeleton: React.FC<{ rows?: number; className?: string }> = ({ 
  rows = 5, 
  className 
}) => (
  <Card className={cn("glass-card ring-subtle soft-shadow rounded-xl", className)}>
    <CardHeader>
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-9 w-24" />
      </div>
    </CardHeader>
    <CardContent className="space-y-3">
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
          <Skeleton className="h-8 w-20" />
        </div>
      ))}
    </CardContent>
  </Card>
);

/**
 * Skeleton for form/settings tab content
 */
export const TabFormSkeleton: React.FC<{ fields?: number; className?: string }> = ({ 
  fields = 4, 
  className 
}) => (
  <Card className={cn("glass-card ring-subtle soft-shadow rounded-xl", className)}>
    <CardHeader>
      <Skeleton className="h-6 w-40" />
      <Skeleton className="h-4 w-64 mt-2" />
    </CardHeader>
    <CardContent className="space-y-6">
      {Array.from({ length: fields }).map((_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-10 w-full" />
        </div>
      ))}
      <div className="flex gap-3 pt-4">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
      </div>
    </CardContent>
  </Card>
);

/**
 * Skeleton for grid/card layout tab content
 */
export const TabGridSkeleton: React.FC<{ 
  cards?: number; 
  columns?: number;
  className?: string;
}> = ({ 
  cards = 6, 
  columns = 3,
  className 
}) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  }[columns] || 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';

  return (
    <div className={cn(`grid gap-4 ${gridCols}`, className)}>
      {Array.from({ length: cards }).map((_, i) => (
        <Card key={i} className="glass-card ring-subtle soft-shadow rounded-xl">
          <CardHeader>
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-lg" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <div className="flex gap-2 pt-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-16" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

/**
 * Skeleton for analytics/chart tab content
 */
export const TabChartSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("space-y-6", className)}>
    {/* KPI Cards */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {[1, 2, 3].map((i) => (
        <Card key={i} className="glass-card ring-subtle soft-shadow rounded-xl p-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-3 w-16" />
          </div>
        </Card>
      ))}
    </div>
    
    {/* Chart Area */}
    <Card className="glass-card ring-subtle soft-shadow rounded-xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <div className="flex gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Skeleton className="h-64 w-full rounded-lg" />
      </CardContent>
    </Card>
  </div>
);

/**
 * Skeleton for detail/profile tab content
 */
export const TabDetailSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn("glass-card ring-subtle soft-shadow rounded-xl", className)}>
    <CardHeader>
      <div className="flex items-center gap-4">
        <Skeleton className="h-20 w-20 rounded-full" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
          <div className="flex gap-2 pt-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-16" />
          </div>
        </div>
      </div>
    </CardHeader>
    <CardContent className="space-y-6">
      {[1, 2, 3].map((section) => (
        <div key={section} className="space-y-3">
          <Skeleton className="h-5 w-32" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/6" />
          </div>
        </div>
      ))}
    </CardContent>
  </Card>
);

/**
 * Generic tab skeleton with customizable layout
 */
export const TabSkeleton: React.FC<{
  variant?: 'list' | 'form' | 'grid' | 'chart' | 'detail';
  className?: string;
  [key: string]: any;
}> = ({ variant = 'list', className, ...props }) => {
  const skeletons = {
    list: TabListSkeleton,
    form: TabFormSkeleton,
    grid: TabGridSkeleton,
    chart: TabChartSkeleton,
    detail: TabDetailSkeleton,
  };

  const SkeletonComponent = skeletons[variant];
  return <SkeletonComponent className={className} {...props} />;
};

/**
 * Animated placeholder for empty tab content
 */
export const TabEmptyState: React.FC<{
  icon?: React.ElementType;
  title?: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}> = ({ 
  icon: Icon, 
  title = "No content yet", 
  description = "This tab doesn't have any content to display.",
  action,
  className 
}) => (
  <Card className={cn("glass-card ring-subtle soft-shadow rounded-xl", className)}>
    <CardContent className="flex flex-col items-center justify-center py-12 text-center">
      {Icon && (
        <div className="mb-4 p-3 rounded-full bg-muted/50">
          <Icon className="h-8 w-8 text-muted-foreground" />
        </div>
      )}
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-sm text-muted-foreground max-w-sm mb-6">{description}</p>
      {action}
    </CardContent>
  </Card>
);
