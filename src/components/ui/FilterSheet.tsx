import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";

interface FilterSheetProps {
  title: string;
  triggerLabel?: string;
  triggerIcon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  triggerClassName?: string;
  contentClassName?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

/**
 * Reusable FilterSheet component with unified glassmorphism design
 * Ensures consistent height handling and visual styling across all filter flyouts
 */
export function FilterSheet({
  title,
  triggerLabel = "Filters",
  triggerIcon,
  children,
  className,
  triggerClassName,
  contentClassName,
  open,
  onOpenChange,
}: FilterSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "hover-lift transition-all duration-300 border-white/10",
            triggerClassName
          )}
        >
          {triggerIcon && <span className="mr-2">{triggerIcon}</span>}
          {triggerLabel}
        </Button>
      </SheetTrigger>
      <SheetContent
        className={cn(
          // Glassmorphism styling - using individual classes for better specificity
          "bg-background/80 backdrop-blur-xl backdrop-saturate-150",
          "border-l border-white/10",
          "shadow-2xl",
          // Proper flex layout for height handling
          "flex flex-col",
          // Default width and padding
          "w-[400px] sm:w-[540px] p-0",
          // Dark mode adjustments
          "dark:bg-background/60 dark:border-white/5",
          contentClassName
        )}
      >
        <SheetHeader className="px-6 py-4 border-b border-white/10 dark:border-white/5">
          <SheetTitle className="text-lg font-semibold tracking-tight">
            {title}
          </SheetTitle>
        </SheetHeader>
        {/* Scrollable content container - NO top padding here, handled by children */}
        <div className={cn("flex-1 overflow-hidden", className)}>
          <div className="h-full overflow-y-auto px-6">
            {children}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
