import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

// Context for managing tab state and measurements
interface ModernTabsContextValue {
  registerTrigger: (value: string, el: HTMLElement | null) => void;
  activeValue?: string;
  getTriggerElement: (value: string) => HTMLElement | null;
  direction: "forward" | "backward";
}

const ModernTabsContext = React.createContext<ModernTabsContextValue | undefined>(undefined);

const useModernTabs = () => {
  const context = React.useContext(ModernTabsContext);
  if (!context) {
    throw new Error("useModernTabs must be used within ModernTabs");
  }
  return context;
};

// Root component that provides context
interface ModernTabsProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Root> {
  children: React.ReactNode;
}

const ModernTabs = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Root>,
  ModernTabsProps
>(({ children, value, defaultValue, onValueChange, ...props }, ref) => {
  const [triggerElements, setTriggerElements] = React.useState<Map<string, HTMLElement>>(new Map());
  const [activeValue, setActiveValue] = React.useState(value || defaultValue);
  const [direction, setDirection] = React.useState<"forward" | "backward">("forward");
  const previousValue = React.useRef(activeValue);

  // Track value changes to determine direction
  React.useEffect(() => {
    setActiveValue(value || defaultValue);
    
    // Determine direction based on DOM position (more generic approach)
    if (previousValue.current && value && previousValue.current !== value) {
      const prevEl = triggerElements.get(previousValue.current);
      const currentEl = triggerElements.get(value);
      if (prevEl && currentEl) {
        setDirection(currentEl.offsetLeft > prevEl.offsetLeft ? "forward" : "backward");
      }
    }
    previousValue.current = value || defaultValue;
  }, [value, defaultValue, triggerElements]);

  const registerTrigger = React.useCallback((value: string, el: HTMLElement | null) => {
    if (el) {
      setTriggerElements(prev => new Map(prev).set(value, el));
    } else {
      setTriggerElements(prev => {
        const next = new Map(prev);
        next.delete(value);
        return next;
      });
    }
  }, []);

  const getTriggerElement = React.useCallback((value: string) => {
    return triggerElements.get(value) || null;
  }, [triggerElements]);

  const contextValue = React.useMemo(
    () => ({
      registerTrigger,
      activeValue,
      getTriggerElement,
      direction,
    }),
    [registerTrigger, activeValue, getTriggerElement, direction]
  );

  return (
    <ModernTabsContext.Provider value={contextValue}>
      <TabsPrimitive.Root
        ref={ref}
        value={value}
        defaultValue={defaultValue}
        onValueChange={onValueChange}
        {...props}
      >
        {children}
      </TabsPrimitive.Root>
    </ModernTabsContext.Provider>
  );
});
ModernTabs.displayName = "ModernTabs";

// List component with sliding indicator
interface ModernTabsListProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> {
  indicatorStyle?: 'default' | 'underline' | 'pill' | 'neon' | 'blob' | 'wave' | 'magnetic' | 'laser' | 'orb' | 'circuit';
}

const ModernTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  ModernTabsListProps
>(({ className, children, indicatorStyle = 'underline', ...props }, ref) => {
  const { activeValue, getTriggerElement } = useModernTabs();
  const listRef = React.useRef<HTMLDivElement>(null);
  const [indicatorPosition, setIndicatorPosition] = React.useState<React.CSSProperties>({});

  // Update indicator position
  React.useEffect(() => {
    if (activeValue && listRef.current) {
      const triggerEl = getTriggerElement(activeValue);
      if (triggerEl) {
        // Get all child elements (icon + text)
        const children = triggerEl.children;
        let contentWidth = 0;
        
        // Measure the actual content width including icon and text
        if (children.length > 0) {
          const firstChild = children[0] as HTMLElement;
          const lastChild = children[children.length - 1] as HTMLElement;
          const firstRect = firstChild.getBoundingClientRect();
          const lastRect = lastChild.getBoundingClientRect();
          contentWidth = (lastRect.left + lastRect.width) - firstRect.left;
        } else {
          // Fallback to text content
          contentWidth = triggerEl.textContent ? triggerEl.textContent.length * 8 : 60;
        }
        
        // Add padding to make it wider than the content (40px total: 20px each side)
        const triggerWidth = triggerEl.offsetWidth;
        const indicatorWidth = Math.min(contentWidth + 40, triggerWidth * 0.85); // 85% max width
        const leftOffset = triggerEl.offsetLeft + (triggerWidth - indicatorWidth) / 2;
        
        const top = triggerEl.offsetTop;
        const height = triggerEl.offsetHeight;
        
        setIndicatorPosition({
          transform: `translate(${leftOffset}px, ${top}px)`,
          width: `${indicatorWidth}px`,
          height: `${height}px`,
        });
      }
    }
  }, [activeValue, getTriggerElement]);

  // Re-calculate indicator position on resize and sidebar changes
  React.useEffect(() => {
    const updateIndicatorPosition = () => {
      if (activeValue) {
        const triggerEl = getTriggerElement(activeValue);
        if (triggerEl) {
          // Get all child elements (icon + text)
          const children = triggerEl.children;
          let contentWidth = 0;
          
          // Measure the actual content width including icon and text
          if (children.length > 0) {
            const firstChild = children[0] as HTMLElement;
            const lastChild = children[children.length - 1] as HTMLElement;
            const firstRect = firstChild.getBoundingClientRect();
            const lastRect = lastChild.getBoundingClientRect();
            contentWidth = (lastRect.left + lastRect.width) - firstRect.left;
          } else {
            // Fallback to text content
            contentWidth = triggerEl.textContent ? triggerEl.textContent.length * 8 : 60;
          }
          
          // Add padding to make it wider than the content (40px total: 20px each side)
          const triggerWidth = triggerEl.offsetWidth;
          const indicatorWidth = Math.min(contentWidth + 40, triggerWidth * 0.85); // 85% max width
          const leftOffset = triggerEl.offsetLeft + (triggerWidth - indicatorWidth) / 2;
          
          const top = triggerEl.offsetTop;
          const height = triggerEl.offsetHeight;
          
          setIndicatorPosition({
            transform: `translate(${leftOffset}px, ${top}px)`,
            width: `${indicatorWidth}px`,
            height: `${height}px`,
          });
        }
      }
    };

    window.addEventListener("resize", updateIndicatorPosition);
    
    // Listen for transition events which might indicate sidebar changes
    const handleTransitionEnd = () => {
      setTimeout(updateIndicatorPosition, 50);
    };
    document.addEventListener("transitionend", handleTransitionEnd);
    
    // Watch for sidebar state changes via mutation observer
    const mutationObserver = new MutationObserver((mutations) => {
      // Check if any mutations affected the sidebar state
      const hasSidebarChange = mutations.some(mutation => {
        if (mutation.type === 'attributes') {
          const target = mutation.target as HTMLElement;
          // Check for data-state attribute changes (sidebar uses this)
          if (mutation.attributeName === 'data-state' && 
              (target.getAttribute('data-state') === 'expanded' || 
               target.getAttribute('data-state') === 'collapsed')) {
            return true;
          }
          // Check for other sidebar-related attributes
          return mutation.attributeName === 'data-collapsible' ||
                 mutation.attributeName === 'data-sidebar-open' ||
                 mutation.attributeName === 'class';
        }
        return false;
      });
      
      if (hasSidebarChange) {
        // Multiple updates to ensure we catch the right moment
        updateIndicatorPosition();
        setTimeout(updateIndicatorPosition, 100);
        setTimeout(updateIndicatorPosition, 200);
        setTimeout(updateIndicatorPosition, 300);
      }
    });
    
    // Observe the body and main layout elements for sidebar changes
    mutationObserver.observe(document.body, {
      attributes: true,
      attributeFilter: ['class', 'data-state', 'data-collapsible', 'data-sidebar-open'],
      subtree: true
    });

    return () => {
      window.removeEventListener("resize", updateIndicatorPosition);
      document.removeEventListener("transitionend", handleTransitionEnd);
      mutationObserver.disconnect();
    };
  }, [activeValue, getTriggerElement]);

  return (
    <TabsPrimitive.List
      ref={(node) => {
        if (ref) {
          if (typeof ref === "function") ref(node);
          else ref.current = node;
        }
        if (node) {
          listRef.current = node;
        }
      }}
      className={cn(
        "relative bg-transparent overflow-visible rounded-xl items-center",
        className
      )}
      {...props}
    >
      {children}
      {/* Sliding gradient indicator - positioned after children for proper z-index */}
      <div
        className={cn(
          "tab-indicator absolute pointer-events-none",
          indicatorStyle === 'default' && "rounded-full bg-gradient-to-r from-primary/20 to-primary/5 backdrop-blur-sm",
          indicatorStyle === 'underline' && "tab-indicator-underline",
          indicatorStyle === 'pill' && "tab-indicator-pill",
          indicatorStyle === 'neon' && "tab-indicator-neon",
          indicatorStyle === 'blob' && "tab-indicator-blob",
          indicatorStyle === 'wave' && "tab-indicator-wave",
          indicatorStyle === 'magnetic' && "tab-indicator-magnetic",
          indicatorStyle === 'laser' && "tab-indicator-laser",
          indicatorStyle === 'orb' && "tab-indicator-orb",
          indicatorStyle === 'circuit' && "tab-indicator-circuit"
        )}
        style={{
          ...indicatorPosition,
          left: 0,
          top: 0,
        }}
        aria-hidden="true"
      />
    </TabsPrimitive.List>
  );
});
ModernTabsList.displayName = "ModernTabsList";

// Trigger component with icon support and animations
interface ModernTabsTriggerProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> {
  icon?: LucideIcon;
  label?: string;
  staggerIndex?: number;
  className?: string;
}

const ModernTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  ModernTabsTriggerProps
>(({ className, icon: Icon, label, staggerIndex = 0, children, value, ...props }, ref) => {
  const { registerTrigger } = useModernTabs();
  const triggerRef = React.useRef<HTMLButtonElement>(null);

  React.useEffect(() => {
    if (value && triggerRef.current) {
      registerTrigger(value, triggerRef.current);
      
      return () => {
        registerTrigger(value, null);
      };
    }
  }, [value, registerTrigger]);

  return (
    <TabsPrimitive.Trigger
      ref={(node) => {
        if (ref) {
          if (typeof ref === "function") ref(node);
          else ref.current = node;
        }
        if (node) {
          triggerRef.current = node;
        }
      }}
      value={value}
      className={cn(
        "trigger-stagger relative z-10 inline-flex items-center justify-center gap-2 px-4 py-2 rounded-full",
        "bg-transparent text-sm font-medium text-muted-foreground",
        "transition-all duration-300",
        "hover:scale-[1.02] hover:shadow-glow hover:text-foreground/80",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:pointer-events-none disabled:opacity-50",
        "data-[state=active]:text-foreground data-[state=active]:font-semibold",
        "whitespace-nowrap", // Prevent text wrapping
        className
      )}
      style={{
        animationDelay: `${staggerIndex * 60}ms`,
      }}
      {...props}
    >
      {Icon && (
        <Icon className="w-4 h-4 transition-all duration-300 motion-safe:group-hover:-translate-y-[1px] group-hover:brightness-110 group-data-[state=active]:brightness-125" />
      )}
      {label && <span>{label}</span>}
      {!Icon && !label && children}
    </TabsPrimitive.Trigger>
  );
});
ModernTabsTrigger.displayName = "ModernTabsTrigger";

// Content component with fade/slide animations
const ModernTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, children, value, ...props }, ref) => {
  const { direction } = useModernTabs();

  return (
    <TabsPrimitive.Content
      ref={ref}
      value={value}
      className={cn(
        "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "data-[state=active]:animate-in data-[state=active]:fade-in-0 data-[state=active]:slide-in-from-bottom-1",
        "data-[state=inactive]:animate-out data-[state=inactive]:fade-out-0",
        className
      )}
      {...props}
    >
      {children}
    </TabsPrimitive.Content>
  );
});
ModernTabsContent.displayName = "ModernTabsContent";

// Export compound component
export const ModernTabsComponent = Object.assign(ModernTabs, {
  List: ModernTabsList,
  Trigger: ModernTabsTrigger,
  Content: ModernTabsContent,
});

export { ModernTabs, ModernTabsList, ModernTabsTrigger, ModernTabsContent };
