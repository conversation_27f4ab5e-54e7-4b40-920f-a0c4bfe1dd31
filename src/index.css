@import './styles/tab-indicators.css';
@import './styles/glass-morphism.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    
    /* Semantic colors */
    --info: 217 91% 60%;
    --info-foreground: 210 40% 98%;
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --error: 0 84% 60%;
    --error-foreground: 0 0% 100%;
    
    /* Surface elevation */
    --surface: 0 0% 100%;
    --surface-1: 0 0% 98%;
    --surface-2: 0 0% 96%;
    --surface-3: 0 0% 94%;
    --surface-4: 0 0% 92%;
    
    /* Chart colors */
    --chart-1: 217 91% 60%;
    --chart-2: 142 71% 45%;
    --chart-3: 38 92% 50%;
    --chart-4: 280 65% 60%;
    --chart-5: 0 84% 60%;
    
    /* Border radius tokens */
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius: 0.5rem; /* Default */
    
    /* Motion tokens */
    --ease-standard: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-spring: cubic-bezier(0.22, 1, 0.36, 1);
    --ease-snap: cubic-bezier(0.34, 1.56, 0.64, 1);
    
    /* Duration tokens */
    --duration-instant: 120ms;
    --duration-fast: 200ms;
    --duration-moderate: 320ms;
    --duration-slow: 420ms;

    /* Sidebar variables */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Core colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    
    /* Semantic colors for dark mode */
    --info: 217 91% 60%;
    --info-foreground: 222.2 47.4% 11.2%;
    --success: 142 71% 45%;
    --success-foreground: 222.2 47.4% 11.2%;
    --warning: 38 92% 50%;
    --warning-foreground: 222.2 47.4% 11.2%;
    --error: 0 72% 50%;
    --error-foreground: 210 40% 98%;
    
    /* Surface elevation for dark mode */
    --surface: 222.2 84% 4.9%;
    --surface-1: 222.2 84% 7%;
    --surface-2: 222.2 84% 9%;
    --surface-3: 222.2 84% 11%;
    --surface-4: 222.2 84% 13%;
    
    /* Chart colors for dark mode */
    --chart-1: 217 91% 60%;
    --chart-2: 142 71% 45%;
    --chart-3: 38 92% 50%;
    --chart-4: 280 65% 60%;
    --chart-5: 0 72% 50%;

    /* Sidebar variables for dark mode */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }
}

/* Ensure sidebar background is properly applied */
[data-sidebar="sidebar"] {
  background-color: hsl(var(--sidebar-background)) !important;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar utilities */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.5);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* React Day Picker Calendar Fixes */
.rdp {
  --rdp-accent-color: hsl(var(--primary));
  --rdp-background-color: hsl(var(--background));
  --rdp-accent-color-dark: hsl(var(--primary));
  --rdp-background-color-dark: hsl(var(--background));
  --rdp-outline: 2px solid hsl(var(--ring));
  --rdp-outline-selected: 2px solid hsl(var(--ring));
  --rdp-selected-color: hsl(var(--primary-foreground));
}

.rdp-month_grid {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

.rdp-weekdays {
  display: table-header-group;
}

.rdp-weekday {
  display: table-cell;
  width: 14.285714%;
  text-align: center;
  vertical-align: middle;
  padding: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.rdp-week {
  display: table-row;
}

.rdp-day {
  display: table-cell;
  width: 14.285714%;
  text-align: center;
  vertical-align: middle;
  padding: 0;
  position: relative;
}

.rdp-day_button {
  width: 2.25rem;
  height: 2.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 400;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  color: hsl(var(--foreground));
}

.rdp-day_button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rdp-day_button:focus {
  outline: var(--rdp-outline);
  outline-offset: 2px;
}

.rdp-selected .rdp-day_button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-selected .rdp-day_button:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-today .rdp-day_button {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  font-weight: 500;
}

.rdp-outside .rdp-day_button {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
}

/* Modern UI Utilities */
@layer utilities {
  /* Background patterns */
  .bg-spotlight {
    background-image: theme("backgroundImage.spotlight");
  }
  .bg-grid {
    background-image: theme("backgroundImage.grid");
    background-size: theme("backgroundSize.grid");
    background-position: center;
  }
  
  /* Glass morphism */
  .glass-card {
    background-color: color-mix(in oklab, hsl(var(--card)) 95%, transparent);
    @apply border border-border/50 dark:border-white/10;
  }
  
  /* Surface elevation */
  .surface-0 {
    @apply bg-surface;
  }
  .surface-1 {
    @apply bg-surface-1 border border-border/50;
  }
  .surface-2 {
    @apply bg-surface-2 border border-border/60;
  }
  .surface-3 {
    @apply bg-surface-3 border border-border/70;
  }
  .surface-4 {
    @apply bg-surface-4 border border-border/80;
  }
  
  /* Motion utilities */
  .hover-lift {
    @apply transition-all duration-200 ease-spring will-change-transform;
    @apply hover:-translate-y-0.5 hover:shadow-lg;
  }
  
  .hover-scale {
    @apply transition-transform duration-120 ease-spring;
    @apply hover:scale-[1.02] active:scale-[0.98];
  }
  
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }
  
  /* Shadow utilities */
  .ring-subtle {
    @apply ring-1 ring-black/5 dark:ring-white/5;
  }
  .soft-shadow {
    @apply shadow-soft;
  }
  .elevated-shadow {
    @apply shadow-elevated;
  }
  .glow-shadow {
    @apply shadow-glow;
  }
  
  /* Animation utilities */
  .fade-up {
    @apply animate-fade-up motion-reduce:animate-none;
  }
  .fade-in {
    @apply animate-fade-in motion-reduce:animate-none;
  }
  .scale-in {
    @apply animate-scale-in motion-reduce:animate-none;
  }
  
  /* Skeleton loading */
  .skeleton {
    @apply relative overflow-hidden bg-muted/50;
  }
  .skeleton::after {
    content: '';
    @apply absolute inset-0 -translate-x-full animate-shimmer;
    background: linear-gradient(
      90deg,
      transparent,
      hsl(var(--muted-foreground) / 0.1),
      transparent
    );
  }
  
  /* Motion safe utilities */
  @media (prefers-reduced-motion: no-preference) {
    .motion-safe\:hover-lift {
      @apply hover-lift;
    }
    .motion-safe\:hover-scale {
      @apply hover-scale;
    }
    .motion-safe\:fade-up {
      @apply fade-up;
    }
    .motion-safe\:fade-in {
      @apply fade-in;
    }
  }
}

.rdp-disabled .rdp-day_button {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-disabled .rdp-day_button:hover {
  background: transparent;
}

.rdp-range_start .rdp-day_button,
.rdp-range_end .rdp-day_button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-range_middle .rdp-day_button {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Dark mode adjustments */
.dark .rdp-day_button {
  color: hsl(var(--foreground));
}

/* Modern Tabs Animations and Variables */
:root {
  --ease-spring: cubic-bezier(0.22, 1, 0.36, 1);
  --ease-snap: cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Tab Indicator Styles */
.tab-indicator {
  will-change: transform, width;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1), width 300ms cubic-bezier(0.4, 0, 0.2, 1);
  /* Default to glowing underline style if no specific class is applied */
  height: 3px;
  bottom: -1px;
  background: linear-gradient(90deg, 
    transparent 0%,
    hsl(var(--primary) / 0.5) 10%,
    hsl(var(--primary)) 50%,
    hsl(var(--primary) / 0.5) 90%,
    transparent 100%
  );
  border-radius: 2px;
  z-index: 20;
  opacity: 1;
  box-shadow: 
    0 0 15px hsl(var(--primary) / 0.6),
    0 0 30px hsl(var(--primary) / 0.3),
    0 0 45px hsl(var(--primary) / 0.2);
  animation: tab-glow-pulse 2s ease-in-out infinite;
}

@keyframes tab-glow-pulse {
  0%, 100% {
    box-shadow: 
      0 0 15px hsl(var(--primary) / 0.6),
      0 0 30px hsl(var(--primary) / 0.3),
      0 0 45px hsl(var(--primary) / 0.2);
  }
  50% {
    box-shadow: 
      0 0 20px hsl(var(--primary) / 0.8),
      0 0 40px hsl(var(--primary) / 0.4),
      0 0 60px hsl(var(--primary) / 0.3);
  }
}

/* Keyframes for Tab Animations */
@keyframes tab-indicator-bounce {
  0% {
    transform: scaleX(0.96);
  }
  50% {
    transform: scaleX(1.02);
  }
  100% {
    transform: scaleX(1);
  }
}

@keyframes tab-content-in-left {
  from {
    opacity: 0;
    transform: translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes tab-content-in-right {
  from {
    opacity: 0;
    transform: translateX(8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes tab-content-out-left {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-8px);
  }
}

@keyframes tab-content-out-right {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(8px);
  }
}

@keyframes hover-press {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3), 0 0 40px hsl(var(--primary) / 0.1);
  }
  50% {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.4), 0 0 60px hsl(var(--primary) / 0.2);
  }
}

@keyframes stagger-fade-up {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Blob animation for background orbs */
@keyframes blob {
  0%, 100% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Tab Animation Utilities */
.animate-tab-in-left {
  animation: tab-content-in-left 320ms var(--ease-spring);
}

.animate-tab-in-right {
  animation: tab-content-in-right 320ms var(--ease-spring);
}

.animate-tab-out-left {
  animation: tab-content-out-left 280ms var(--ease-spring);
}

.animate-tab-out-right {
  animation: tab-content-out-right 280ms var(--ease-spring);
}

.trigger-stagger {
  opacity: 0;
  animation: stagger-fade-up 400ms var(--ease-spring) forwards;
}

/* Parallax Hover Effect */
@media (hover: hover) {
  .parallax-hover {
    transform: translate(calc(var(--px, 0) * 1px), calc(var(--py, 0) * 1px));
    transition: transform 200ms var(--ease-spring);
  }
}

/* Modern Tabs List Responsive Styles */
.modern-tabs-list {
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.modern-tabs-list::-webkit-scrollbar {
  display: none;
}

@media (max-width: 640px) {
  .modern-tabs-list {
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }
  
  .modern-tabs-list > * {
    scroll-snap-align: center;
  }
}

.dark .rdp-weekday {
  color: hsl(var(--muted-foreground));
}
