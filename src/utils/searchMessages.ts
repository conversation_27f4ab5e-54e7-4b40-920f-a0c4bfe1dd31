import { supabase } from "@/integrations/supabase/client";
import { Message } from "@/hooks/useMessages";
import { calculateRelevanceScore } from "./searchUtils";

interface SearchFilters {
  status?: "unread" | "read" | "archived";
  isStarred?: boolean;
  followUp?: boolean;
  reminder?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export const searchMessages = async (
  query: string,
  filters: SearchFilters = {},
  userId?: string,
): Promise<Message[]> => {
  try {
    let supabaseQuery = supabase.from("messages").select("*");

    // Always scope results to the current user when available
    if (userId) {
      supabaseQuery = supabaseQuery.eq("user_id", userId);
    }

    // Apply full-text search if query is provided
    if (query.trim()) {
      // Clean and prepare query for full-text search
      const cleanQuery = query
        .trim()
        .replace(/[^\w\s]/g, "")
        .split(/\s+/)
        .join(" & ");

      // Use full-text search
      supabaseQuery = supabaseQuery.filter("search_vector", "fts", cleanQuery);
    }

    // Apply status filter
    if (filters.status) {
      supabaseQuery = supabaseQuery.eq("status", filters.status);
    }

    // Apply starred filter
    if (filters.isStarred !== undefined) {
      supabaseQuery = supabaseQuery.eq("is_starred", filters.isStarred);
    }

    // Apply follow-up filter
    if (filters.followUp !== undefined) {
      supabaseQuery = supabaseQuery.eq("follow_up", filters.followUp);
    }

    // Apply reminder filter
    if (filters.reminder !== undefined) {
      supabaseQuery = supabaseQuery.eq("reminder", filters.reminder);
    }

    // Apply date range filter
    if (filters.dateRange) {
      supabaseQuery = supabaseQuery
        .gte("created_at", filters.dateRange.start.toISOString())
        .lte("created_at", filters.dateRange.end.toISOString());
    }

    const { data, error } = await supabaseQuery.limit(50);

    if (error) {
      console.error("Message search error:", error);
      throw error;
    }

    // Sort results by relevance to the search query
    const results = (data || []) as Message[];

    // Deduplicate defensively by composite key and by id
    const byKey = new Map<string, Message>();
    for (const row of results) {
      const key = `${(row as any).id ?? ""}|${(row as any).sender_email ?? ""}|${(row as any).content?.trim() ?? ""}`;
      // Keep the most recent one per key
      const existing = byKey.get(key);
      if (!existing || new Date(row.created_at) > new Date(existing.created_at)) {
        byKey.set(key, row);
      }
    }
    const deduped = Array.from(byKey.values());
    if (query.trim()) {
      deduped.sort((a, b) => {
        const scoreA = calculateRelevanceScore(
          `${a.sender_name} ${a.sender_email} ${a.sender_role || ""} ${a.content}`,
          query,
        );
        const scoreB = calculateRelevanceScore(
          `${b.sender_name} ${b.sender_email} ${b.sender_role || ""} ${b.content}`,
          query,
        );
        return scoreB - scoreA;
      });
    } else {
      // If no query, sort by created_at (newest first)
      deduped.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      );
    }

    return deduped;
  } catch (error) {
    console.error("Error searching messages:", error);
    return [];
  }
};
