import { CandidateType } from "@/types/candidate";
import { SearchFilters } from "@/types/search";

// Optimized search utility that minimizes array operations and improves performance

/**
 * Pre-compiled search patterns for better performance
 */
interface SearchPattern {
  regex?: RegExp;
  toLowerCase: string;
  terms: string[];
}

/**
 * Cache for compiled search patterns to avoid repeated regex compilation
 */
const searchPatternCache = new Map<string, SearchPattern>();

/**
 * Compile search query into optimized pattern
 */
function compileSearchPattern(query: string): SearchPattern {
  if (searchPatternCache.has(query)) {
    return searchPatternCache.get(query)!;
  }

  const toLowerCase = query.toLowerCase();
  const terms = toLowerCase.split(/\s+/).filter((term) => term.length > 0);

  const pattern: SearchPattern = {
    toLowerCase,
    terms,
  };

  // Cache the pattern for reuse
  searchPatternCache.set(query, pattern);

  // Keep cache size reasonable
  if (searchPatternCache.size > 100) {
    const firstKey = searchPatternCache.keys().next().value;
    searchPatternCache.delete(firstKey);
  }

  return pattern;
}

/**
 * Optimized candidate search with minimal array operations
 */
export function searchCandidatesOptimized(
  candidates: CandidateType[],
  query: string,
  filters: SearchFilters = {},
): CandidateType[] {
  if (!query || query.length < 3) return [];
  if (!candidates || candidates.length === 0) return [];

  const pattern = compileSearchPattern(query);
  const results: CandidateType[] = [];

  // Single-pass filtering with early returns
  for (const candidate of candidates) {
    // Text matching with optimized string operations
    let textMatch = false;

    // Check each search term against candidate fields
    // Extract skills from all possible sources
    const normalizedSkillNames = candidate.normalized_skills?.map((s) => 
      typeof s === "string" ? s : s.name
    ) ?? [];
    const skillNames = candidate.skills?.map((s) => 
      typeof s === "string" ? s : s.name
    ) ?? [];
    const directSkillNames = candidate.skill_names ?? [];
    
    // Combine all skill sources
    const allSkills = [
      ...normalizedSkillNames,
      ...skillNames,
      ...directSkillNames
    ];
    
    // Extract tags from normalized_tags if tags array is empty
    const candidateTags = candidate.tags?.length > 0 
      ? candidate.tags 
      : (candidate.normalized_tags?.map((t: any) => 
          typeof t === "string" ? t : t.name
        ) ?? []);
    
    const candidateText = [
      candidate.name,
      candidate.role,
      candidate.email,
      candidate.location,
      candidate.experience || "",
      candidate.industry || "",
      ...candidateTags,
      ...allSkills,
    ]
      .join(" ")
      .toLowerCase();

    // Use single indexOf check for better performance than multiple includes
    textMatch = pattern.terms.every(
      (term) => candidateText.indexOf(term) !== -1,
    );

    if (!textMatch) continue;

    // Early exit filters (fail fast)
    if (
      filters.location?.address &&
      candidate.location
        .toLowerCase()
        .indexOf(filters.location.address.toLowerCase()) === -1
    ) {
      continue;
    }

    if (
      filters.experience &&
      (!candidate.experience ||
        candidate.experience
          .toLowerCase()
          .indexOf(filters.experience.toLowerCase()) === -1)
    ) {
      continue;
    }

    if (
      filters.remoteOnly &&
      (!candidate.remotePreference ||
        candidate.remotePreference.toLowerCase().indexOf("remote") === -1)
    ) {
      continue;
    }

    // Skills filter (optimized)
    if (filters.skills?.length) {
      // Check all skill sources: normalized_skills, skills, skill_names, and tags
      const normalizedSkills = candidate.normalized_skills || [];
      const regularSkills = candidate.skills || [];
      const skillNamesArray = candidate.skill_names || [];
      const candidateTags = candidate.tags?.length > 0 
        ? candidate.tags 
        : (candidate.normalized_tags?.map((t: any) => 
            typeof t === "string" ? t : t.name
          ) ?? []);
      
      const hasSkillMatch = filters.skills.some((skill) => {
        const skillNameLower = skill.name.toLowerCase();
        return (
          // Check normalized_skills
          normalizedSkills.some(
            (candidateSkill) =>
              (typeof candidateSkill === "string"
                ? candidateSkill
                : candidateSkill.name
              )
                .toLowerCase()
                .indexOf(skillNameLower) !== -1,
          ) ||
          // Check regular skills
          regularSkills.some(
            (candidateSkill) =>
              (typeof candidateSkill === "string"
                ? candidateSkill
                : candidateSkill.name
              )
                .toLowerCase()
                .indexOf(skillNameLower) !== -1,
          ) ||
          // Check skill_names array
          skillNamesArray.some(
            (skillName) => skillName.toLowerCase().indexOf(skillNameLower) !== -1
          ) ||
          // Check tags as fallback
          candidateTags.some(
            (tag) => tag.toLowerCase().indexOf(skillNameLower) !== -1
          )
        );
      });
      if (!hasSkillMatch) continue;
    }

    results.push(candidate);
  }

  return results;
}

/**
 * Optimized job search with minimal array operations
 */
export function searchJobsOptimized(
  jobs: any[],
  query: string,
  filters: SearchFilters = {},
): any[] {
  if (!query || query.length < 3) return [];
  if (!jobs || jobs.length === 0) return [];

  const pattern = compileSearchPattern(query);
  const results: any[] = [];

  // Single-pass filtering
  for (const job of jobs) {
    // Text matching
    const jobText = [
      job.title,
      job.department,
      job.location,
      job.job_type,
      job.description || "",
      job.experience_required || "",
    ]
      .join(" ")
      .toLowerCase();

    const textMatch = pattern.terms.every(
      (term) => jobText.indexOf(term) !== -1,
    );
    if (!textMatch) continue;

    // Filter checks with early exit
    if (
      filters.location?.address &&
      job.location
        .toLowerCase()
        .indexOf(filters.location.address.toLowerCase()) === -1
    ) {
      continue;
    }

    if (filters.jobType && job.job_type !== filters.jobType) {
      continue;
    }

    if (
      filters.salaryRange &&
      job.salary_range &&
      job.salary_range.indexOf(filters.salaryRange) === -1
    ) {
      continue;
    }

    results.push(job);
  }

  return results;
}

/**
 * Optimized combined search results with memoization
 */
export function getCombinedSearchResults(
  candidates: CandidateType[],
  jobs: any[],
  query: string,
  filters: SearchFilters = {},
) {
  const candidateResults = searchCandidatesOptimized(
    candidates,
    query,
    filters,
  );
  const jobResults = searchJobsOptimized(jobs, query, filters);

  return {
    candidates: candidateResults,
    jobs: jobResults,
    total: candidateResults.length + jobResults.length,
  };
}

/**
 * Debounced search for real-time search input
 */
export function createDebouncedSearch<T>(
  searchFn: (items: T[], query: string, filters: SearchFilters) => T[],
  delay: number = 300,
) {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastResults: T[] = [];

  return function debouncedSearch(
    items: T[],
    query: string,
    filters: SearchFilters,
    callback: (results: T[]) => void,
  ) {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // Return cached results immediately for same query
    timeoutId = setTimeout(() => {
      const results = searchFn(items, query, filters);
      lastResults = results;
      callback(results);
    }, delay);
  };
}

/**
 * Clear search pattern cache (useful for memory management)
 */
export function clearSearchCache() {
  searchPatternCache.clear();
}
