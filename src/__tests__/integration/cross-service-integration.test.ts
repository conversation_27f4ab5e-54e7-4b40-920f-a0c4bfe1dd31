import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { RealtimeEventPublisher } from "@/engine/RealtimeEventPublisher";
import { ExecutionEvent } from "@/engine/types";

// Mock all services for comprehensive integration testing
vi.mock("@/integrations/supabase/client", () => {
  const mockChannel = {
    subscribe: vi.fn(),
    send: vi.fn(),
    unsubscribe: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
  };

  return {
    supabase: {
      channel: vi.fn(() => mockChannel),
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                limit: vi.fn(() => ({
                  data: [],
                  error: null,
                })),
              })),
              single: vi.fn(() => ({
                data: null,
                error: null,
              })),
            })),
            single: vi.fn(() => ({
              data: null,
              error: null,
            })),
          })),
        })),
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => ({
              data: { id: "test-id" },
              error: null,
            })),
          })),
        })),
        update: vi.fn(() => ({
          eq: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn(() => ({
                data: { id: "test-id" },
                error: null,
              })),
            })),
          })),
        })),
      })),
      rpc: vi.fn(() => ({
        data: null,
        error: null,
      })),
    },
  };
});

// Mock comprehensive service ecosystem
vi.mock("@/services/TasksService", () => ({
  TasksService: {
    createTask: vi.fn(),
    updateTask: vi.fn(),
    deleteTask: vi.fn(),
    getTasks: vi.fn(),
    getTaskById: vi.fn(),
    assignTask: vi.fn(),
    completeTask: vi.fn(),
    getTasksByStatus: vi.fn(),
    getTasksByAssignee: vi.fn(),
    bulkUpdateTasks: vi.fn(),
  },
}));

vi.mock("@/services/NotificationsService", () => ({
  NotificationsService: {
    sendNotification: vi.fn(),
    getNotifications: vi.fn(),
    markAsRead: vi.fn(),
    deleteNotification: vi.fn(),
    createNotification: vi.fn(),
    updateNotification: vi.fn(),
    getUnreadCount: vi.fn(),
    subscribeToNotifications: vi.fn(),
    unsubscribeFromNotifications: vi.fn(),
  },
}));

vi.mock("@/services/AutonomousAgent", () => ({
  AutonomousAgent: {
    executeTask: vi.fn(),
    analyzeCandidate: vi.fn(),
    generateRecommendations: vi.fn(),
    processWorkflow: vi.fn(),
    handleDecision: vi.fn(),
    getAgentStatus: vi.fn(),
    updateAgentConfig: vi.fn(),
    getExecutionHistory: vi.fn(),
    cancelExecution: vi.fn(),
    scheduleTask: vi.fn(),
  },
}));

// Mock additional RMS-Refresh services
vi.mock("@/services/CandidateService", () => ({
  CandidateService: {
    createCandidate: vi.fn(),
    updateCandidate: vi.fn(),
    getCandidateById: vi.fn(),
    searchCandidates: vi.fn(),
    getCandidatesByStatus: vi.fn(),
    updateCandidateStatus: vi.fn(),
    addCandidateNote: vi.fn(),
    getCandidateNotes: vi.fn(),
    scheduleCandidateInterview: vi.fn(),
    getCandidateInterviews: vi.fn(),
  },
}));

vi.mock("@/services/WorkflowService", () => ({
  WorkflowService: {
    createWorkflow: vi.fn(),
    updateWorkflow: vi.fn(),
    deleteWorkflow: vi.fn(),
    getWorkflowById: vi.fn(),
    executeWorkflow: vi.fn(),
    pauseWorkflow: vi.fn(),
    resumeWorkflow: vi.fn(),
    getWorkflowStatus: vi.fn(),
    getWorkflowHistory: vi.fn(),
    validateWorkflow: vi.fn(),
  },
}));

vi.mock("@/services/InterviewService", () => ({
  InterviewService: {
    scheduleInterview: vi.fn(),
    updateInterview: vi.fn(),
    cancelInterview: vi.fn(),
    getInterviewById: vi.fn(),
    getInterviewsByCandidate: vi.fn(),
    getInterviewsByInterviewer: vi.fn(),
    addInterviewFeedback: vi.fn(),
    getInterviewFeedback: vi.fn(),
    generateInterviewReport: vi.fn(),
    sendInterviewReminders: vi.fn(),
  },
}));

describe("Cross-Service Integration Tests", () => {
  let publisher: RealtimeEventPublisher;
  let mockSupabase: any;
  let mockChannel: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Get the mocked supabase instance
    const { supabase } = await import("@/integrations/supabase/client");
    mockSupabase = supabase;
    mockChannel = mockSupabase.channel();

    // Reset singleton
    (RealtimeEventPublisher as any).instance = undefined;
    publisher = RealtimeEventPublisher.getInstance();

    // Setup default mock responses
    mockChannel.subscribe.mockImplementation((callback) => {
      callback("SUBSCRIBED");
      return mockChannel;
    });
    mockChannel.send.mockResolvedValue({ status: "ok" });
  });

  afterEach(() => {
    vi.useRealTimers();
    publisher.cleanup();
  });

  describe("Candidate Lifecycle Integration", () => {
    it("should handle complete candidate journey from application to hire", async () => {
      // Create mock service implementations directly
      const CandidateService = {
        createCandidate: vi.fn(),
        updateCandidateStatus: vi.fn(),
        getCandidateById: vi.fn(),
      };

      const TasksService = {
        createTask: vi.fn(),
        updateTask: vi.fn(),
        getTasks: vi.fn(),
      };

      const NotificationsService = {
        sendNotification: vi.fn(),
        getNotifications: vi.fn(),
      };

      const InterviewService = {
        scheduleInterview: vi.fn(),
        getInterviewById: vi.fn(),
      };

      const AutonomousAgent = {
        analyzeCandidate: vi.fn(),
        executeTask: vi.fn(),
      };

      const candidateData = {
        id: "candidate-lifecycle-123",
        name: "Jane Smith",
        email: "<EMAIL>",
        position: "Senior React Developer",
        status: "applied",
      };

      // Mock service responses for complete lifecycle
      CandidateService.createCandidate.mockResolvedValueOnce({
        success: true,
        data: candidateData,
      });

      AutonomousAgent.analyzeCandidate.mockResolvedValueOnce({
        success: true,
        result: {
          score: 89,
          recommendation: "proceed_to_interview",
          strengths: ["React expertise", "Strong portfolio"],
          concerns: [],
        },
      });

      TasksService.createTask.mockResolvedValueOnce({
        success: true,
        data: {
          id: "task-review-123",
          title: "Review candidate application",
          candidateId: candidateData.id,
          assigneeId: "recruiter-456",
        },
      });

      InterviewService.scheduleInterview.mockResolvedValueOnce({
        success: true,
        data: {
          id: "interview-123",
          candidateId: candidateData.id,
          interviewerId: "tech-lead-789",
          scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      NotificationsService.sendNotification.mockResolvedValue({
        success: true,
        notificationId: "notification-123",
      });

      // Step 1: Candidate applies
      const candidate = await CandidateService.createCandidate(candidateData);
      await publisher.publishEvent("candidate_lifecycle", {
        type: "started",
        nodeId: "candidate-application",
        nodeName: "Candidate Application",
        timestamp: new Date(),
        data: { candidateId: candidateData.id, stage: "application" },
      });

      // Step 2: AI analysis
      const analysis = await AutonomousAgent.analyzeCandidate({
        candidateId: candidateData.id,
        analysisType: "initial_screening",
      });
      await publisher.publishEvent("candidate_lifecycle", {
        type: "progress",
        nodeId: "ai-analysis",
        nodeName: "AI Analysis",
        timestamp: new Date(),
        data: { candidateId: candidateData.id, stage: "ai_analysis", result: analysis.result },
      });

      // Step 3: Create review task
      const task = await TasksService.createTask({
        title: "Review candidate application",
        candidateId: candidateData.id,
        assigneeId: "recruiter-456",
        priority: "high",
      });
      await publisher.publishEvent("candidate_lifecycle", {
        type: "progress",
        nodeId: "task-creation",
        nodeName: "Task Creation",
        timestamp: new Date(),
        data: { candidateId: candidateData.id, stage: "review_task", taskId: task.data.id },
      });

      // Step 4: Schedule interview
      const interview = await InterviewService.scheduleInterview({
        candidateId: candidateData.id,
        interviewerId: "tech-lead-789",
        type: "technical",
        duration: 60,
      });
      await publisher.publishEvent("candidate_lifecycle", {
        type: "progress",
        nodeId: "interview-scheduling",
        nodeName: "Interview Scheduling",
        timestamp: new Date(),
        data: { candidateId: candidateData.id, stage: "interview_scheduled", interviewId: interview.data.id },
      });

      // Step 5: Send notifications
      await NotificationsService.sendNotification({
        userId: "recruiter-456",
        type: "candidate",
        title: "New candidate requires review",
        message: `${candidateData.name} has applied for ${candidateData.position}`,
      });

      await NotificationsService.sendNotification({
        userId: "tech-lead-789",
        type: "interview",
        title: "Interview scheduled",
        message: `Technical interview scheduled with ${candidateData.name}`,
      });

      await publisher.publishEvent("candidate_lifecycle", {
        type: "success",
        nodeId: "notifications-sent",
        nodeName: "Notifications Sent",
        timestamp: new Date(),
        data: { candidateId: candidateData.id, stage: "notifications_complete" },
      });

      // Verify all services were called correctly
      expect(CandidateService.createCandidate).toHaveBeenCalledWith(candidateData);
      expect(AutonomousAgent.analyzeCandidate).toHaveBeenCalled();
      expect(TasksService.createTask).toHaveBeenCalled();
      expect(InterviewService.scheduleInterview).toHaveBeenCalled();
      expect(NotificationsService.sendNotification).toHaveBeenCalledTimes(2);
      expect(mockChannel.send).toHaveBeenCalledTimes(5);
    });

    it("should handle candidate rejection workflow", async () => {
      // Create mock service implementations directly
      const CandidateService = {
        updateCandidateStatus: vi.fn(),
        getCandidateById: vi.fn(),
      };

      const NotificationsService = {
        sendNotification: vi.fn(),
      };

      const AutonomousAgent = {
        analyzeCandidate: vi.fn(),
      };

      const candidateId = "candidate-rejection-456";

      // Mock rejection scenario
      AutonomousAgent.analyzeCandidate.mockResolvedValueOnce({
        success: true,
        result: {
          score: 45,
          recommendation: "reject",
          concerns: ["Insufficient experience", "Skills mismatch"],
        },
      });

      CandidateService.updateCandidateStatus.mockResolvedValueOnce({
        success: true,
        data: { id: candidateId, status: "rejected" },
      });

      NotificationsService.sendNotification.mockResolvedValueOnce({
        success: true,
        notificationId: "rejection-notification-123",
      });

      // Execute rejection workflow
      const analysis = await AutonomousAgent.analyzeCandidate({
        candidateId,
        analysisType: "comprehensive",
      });

      await CandidateService.updateCandidateStatus(candidateId, "rejected");

      await NotificationsService.sendNotification({
        userId: "recruiter-123",
        type: "candidate",
        title: "Candidate rejected",
        message: "AI analysis recommends rejection due to skills mismatch",
      });

      await publisher.publishEvent("candidate_rejection", {
        type: "success",
        nodeId: "rejection-complete",
        nodeName: "Rejection Complete",
        timestamp: new Date(),
        data: { 
          candidateId, 
          reason: "ai_recommendation",
          score: analysis.result.score,
        },
      });

      expect(AutonomousAgent.analyzeCandidate).toHaveBeenCalled();
      expect(CandidateService.updateCandidateStatus).toHaveBeenCalledWith(candidateId, "rejected");
      expect(NotificationsService.sendNotification).toHaveBeenCalled();
      expect(mockChannel.send).toHaveBeenCalled();
    });
  });

  describe("Workflow Orchestration Integration", () => {
    it("should coordinate complex multi-service workflows", async () => {
      // Create mock service implementations directly
      const WorkflowService = {
        executeWorkflow: vi.fn(),
        getWorkflowStatus: vi.fn(),
      };

      const TasksService = {
        bulkUpdateTasks: vi.fn(),
        createTask: vi.fn(),
      };

      const AutonomousAgent = {
        processWorkflow: vi.fn(),
        executeTask: vi.fn(),
      };

      const workflowConfig = {
        id: "complex-workflow-789",
        name: "Multi-Stage Recruitment Process",
        stages: [
          { id: "stage-1", name: "Initial Screening", type: "automated" },
          { id: "stage-2", name: "Technical Review", type: "manual" },
          { id: "stage-3", name: "Final Decision", type: "hybrid" },
        ],
      };

      // Mock workflow execution
      WorkflowService.executeWorkflow.mockResolvedValueOnce({
        success: true,
        executionId: "execution-123",
        status: "running",
      });

      AutonomousAgent.processWorkflow.mockResolvedValueOnce({
        success: true,
        result: {
          stageResults: [
            { stageId: "stage-1", status: "completed", score: 85 },
            { stageId: "stage-2", status: "pending", assignedTo: "tech-reviewer-456" },
          ],
        },
      });

      TasksService.bulkUpdateTasks.mockResolvedValueOnce({
        success: true,
        updatedCount: 3,
      });

      // Execute complex workflow
      const execution = await WorkflowService.executeWorkflow(workflowConfig);
      
      await publisher.publishEvent("workflow_orchestration", {
        type: "started",
        nodeId: "workflow-execution",
        nodeName: "Workflow Execution",
        timestamp: new Date(),
        data: { 
          workflowId: workflowConfig.id,
          executionId: execution.executionId,
        },
      });

      const agentResult = await AutonomousAgent.processWorkflow({
        workflowId: workflowConfig.id,
        executionId: execution.executionId,
      });

      await TasksService.bulkUpdateTasks([
        { id: "task-1", status: "completed" },
        { id: "task-2", status: "in_progress" },
        { id: "task-3", status: "pending" },
      ]);

      await publisher.publishEvent("workflow_orchestration", {
        type: "progress",
        nodeId: "workflow-progress",
        nodeName: "Workflow Progress",
        timestamp: new Date(),
        data: { 
          workflowId: workflowConfig.id,
          completedStages: 1,
          totalStages: 3,
          currentStage: "stage-2",
        },
      });

      expect(WorkflowService.executeWorkflow).toHaveBeenCalledWith(workflowConfig);
      expect(AutonomousAgent.processWorkflow).toHaveBeenCalled();
      expect(TasksService.bulkUpdateTasks).toHaveBeenCalled();
      expect(mockChannel.send).toHaveBeenCalledTimes(2);
    });
  });
});
