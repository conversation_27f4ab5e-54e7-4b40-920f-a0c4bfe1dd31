import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { RealtimeEventPublisher } from "@/engine/RealtimeEventPublisher";
import { ExecutionEvent } from "@/engine/types";

// Mock Supabase with comprehensive integration patterns
vi.mock("@/integrations/supabase/client", () => {
  const mockChannel = {
    subscribe: vi.fn(),
    send: vi.fn(),
    unsubscribe: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
  };

  return {
    supabase: {
      channel: vi.fn(() => mockChannel),
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                limit: vi.fn(() => ({
                  data: [],
                  error: null,
                })),
              })),
              single: vi.fn(() => ({
                data: null,
                error: null,
              })),
            })),
            single: vi.fn(() => ({
              data: null,
              error: null,
            })),
          })),
        })),
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => ({
              data: { id: "test-id" },
              error: null,
            })),
          })),
        })),
        update: vi.fn(() => ({
          eq: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn(() => ({
                data: { id: "test-id" },
                error: null,
              })),
            })),
          })),
        })),
        delete: vi.fn(() => ({
          eq: vi.fn(() => ({
            data: null,
            error: null,
          })),
        })),
      })),
      rpc: vi.fn(() => ({
        data: null,
        error: null,
      })),
    },
  };
});

// Mock services for integration testing
vi.mock("@/services/TasksService", () => ({
  TasksService: {
    createTask: vi.fn(),
    updateTask: vi.fn(),
    deleteTask: vi.fn(),
    getTasks: vi.fn(),
    getTaskById: vi.fn(),
    assignTask: vi.fn(),
    completeTask: vi.fn(),
    getTasksByStatus: vi.fn(),
    getTasksByAssignee: vi.fn(),
    bulkUpdateTasks: vi.fn(),
  },
}));

vi.mock("@/services/NotificationsService", () => ({
  NotificationsService: {
    sendNotification: vi.fn(),
    getNotifications: vi.fn(),
    markAsRead: vi.fn(),
    deleteNotification: vi.fn(),
    createNotification: vi.fn(),
    updateNotification: vi.fn(),
    getUnreadCount: vi.fn(),
    subscribeToNotifications: vi.fn(),
    unsubscribeFromNotifications: vi.fn(),
  },
}));

vi.mock("@/services/AutonomousAgent", () => ({
  AutonomousAgent: {
    executeTask: vi.fn(),
    analyzeCandidate: vi.fn(),
    generateRecommendations: vi.fn(),
    processWorkflow: vi.fn(),
    handleDecision: vi.fn(),
    getAgentStatus: vi.fn(),
    updateAgentConfig: vi.fn(),
    getExecutionHistory: vi.fn(),
    cancelExecution: vi.fn(),
    scheduleTask: vi.fn(),
  },
}));

describe("End-to-End Workflow Integration", () => {
  let publisher: RealtimeEventPublisher;
  let mockSupabase: any;
  let mockChannel: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Get the mocked supabase instance
    const { supabase } = await import("@/integrations/supabase/client");
    mockSupabase = supabase;
    mockChannel = mockSupabase.channel();

    // Reset singleton
    (RealtimeEventPublisher as any).instance = undefined;
    publisher = RealtimeEventPublisher.getInstance();

    // Setup default mock responses
    mockChannel.subscribe.mockImplementation((callback) => {
      callback("SUBSCRIBED");
      return mockChannel;
    });
    mockChannel.send.mockResolvedValue({ status: "ok" });
  });

  afterEach(() => {
    vi.useRealTimers();
    publisher.cleanup();
  });

  describe("Complete Recruitment Workflow", () => {
    it("should execute full candidate screening workflow", async () => {
      const candidateData = {
        id: "candidate-123",
        name: "John Doe",
        email: "<EMAIL>",
        resume: "Software Engineer with 5 years experience...",
        skills: ["JavaScript", "React", "Node.js"],
        position: "Senior Frontend Developer",
      };

      const workflowConfig = {
        id: "screening-workflow-456",
        name: "Candidate Screening",
        steps: [
          { id: "step-1", type: "resume_analysis", status: "pending" },
          { id: "step-2", type: "skills_matching", status: "pending" },
          { id: "step-3", type: "ai_evaluation", status: "pending" },
          { id: "step-4", type: "notification", status: "pending" },
        ],
      };

      // Mock successful database operations
      mockSupabase.from().insert().select().single.mockResolvedValueOnce({
        data: { ...candidateData, workflow_id: workflowConfig.id },
        error: null,
      });

      // Step 1: Resume Analysis
      await publisher.publishEvent("workflow_progress", {
        type: "started",
        nodeId: "step-1",
        nodeName: "Resume Analysis",
        timestamp: new Date(),
        data: { candidateId: candidateData.id, step: "resume_analysis" },
      });

      // Step 2: Skills Matching
      await publisher.publishEvent("workflow_progress", {
        type: "progress",
        nodeId: "step-2",
        nodeName: "Skills Matching",
        timestamp: new Date(),
        data: { 
          candidateId: candidateData.id, 
          step: "skills_matching",
          matchScore: 85,
          matchedSkills: ["JavaScript", "React"],
        },
      });

      // Step 3: AI Evaluation
      await publisher.publishEvent("workflow_progress", {
        type: "progress",
        nodeId: "step-3",
        nodeName: "AI Evaluation",
        timestamp: new Date(),
        data: { 
          candidateId: candidateData.id, 
          step: "ai_evaluation",
          score: 92,
          recommendation: "Strong candidate - proceed to interview",
        },
      });

      // Step 4: Notification
      await publisher.publishEvent("workflow_progress", {
        type: "success",
        nodeId: "step-4",
        nodeName: "Notification",
        timestamp: new Date(),
        data: { 
          candidateId: candidateData.id, 
          step: "notification",
          message: "Screening completed successfully",
        },
      });

      // Verify workflow completion
      expect(mockChannel.send).toHaveBeenCalledTimes(4);
      expect(mockSupabase.from).toHaveBeenCalled();
    });

    it("should handle workflow errors and recovery", async () => {
      const workflowId = "error-workflow-789";
      
      // Simulate error in skills matching step
      mockChannel.send.mockRejectedValueOnce(new Error("Skills matching service unavailable"));
      
      await publisher.publishEvent("workflow_errors", {
        type: "error",
        nodeId: "skills-matching-node",
        nodeName: "Skills Matching",
        timestamp: new Date(),
        data: { 
          workflowId,
          error: "Skills matching service unavailable",
          retryAttempt: 1,
        },
      });

      // Simulate successful retry
      mockChannel.send.mockResolvedValueOnce({ status: "ok" });
      
      await publisher.publishEvent("workflow_errors", {
        type: "retry",
        nodeId: "skills-matching-node",
        nodeName: "Skills Matching",
        timestamp: new Date(),
        data: { 
          workflowId,
          retryAttempt: 2,
          status: "success",
        },
      });

      expect(mockChannel.send).toHaveBeenCalledTimes(2);
    });
  });

  describe("Cross-Service Integration", () => {
    it("should integrate tasks, notifications, and real-time events", async () => {
      // Create mock service implementations directly
      const TasksService = {
        createTask: vi.fn(),
        updateTask: vi.fn(),
        getTasks: vi.fn(),
      };

      const NotificationsService = {
        sendNotification: vi.fn(),
        getNotifications: vi.fn(),
        markAsRead: vi.fn(),
      };
      
      const taskData = {
        id: "task-123",
        title: "Review candidate application",
        assigneeId: "user-456",
        candidateId: "candidate-789",
        priority: "high",
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      };

      // Mock service responses
      TasksService.createTask.mockResolvedValueOnce({
        success: true,
        data: taskData,
      });

      NotificationsService.sendNotification.mockResolvedValueOnce({
        success: true,
        notificationId: "notification-123",
      });

      // Create task
      const taskResult = await TasksService.createTask(taskData);
      
      // Send notification about new task
      await NotificationsService.sendNotification({
        userId: taskData.assigneeId,
        type: "task",
        title: "New Task Assigned",
        message: `You have been assigned: ${taskData.title}`,
        data: { taskId: taskData.id },
      });

      // Publish real-time event
      await publisher.publishEvent("task_assignments", {
        type: "started",
        nodeId: "task-assignment",
        nodeName: "Task Assignment",
        timestamp: new Date(),
        data: {
          taskId: taskData.id,
          assigneeId: taskData.assigneeId,
          action: "assigned",
        },
      });

      expect(TasksService.createTask).toHaveBeenCalledWith(taskData);
      expect(NotificationsService.sendNotification).toHaveBeenCalled();
      expect(mockChannel.send).toHaveBeenCalled();
    });

    it("should handle autonomous agent integration", async () => {
      // Create mock service implementation directly
      const AutonomousAgent = {
        executeTask: vi.fn(),
        analyzeCandidate: vi.fn(),
        getAgentStatus: vi.fn(),
      };
      
      const agentTask = {
        id: "agent-task-123",
        type: "candidate_analysis",
        candidateId: "candidate-456",
        parameters: {
          analysisType: "comprehensive",
          includeSkillsMatch: true,
          includeSentimentAnalysis: true,
        },
      };

      // Mock agent response
      AutonomousAgent.executeTask.mockResolvedValueOnce({
        success: true,
        result: {
          candidateScore: 88,
          skillsMatch: 92,
          sentimentScore: 85,
          recommendation: "Proceed to technical interview",
          confidence: 0.94,
        },
      });

      // Execute agent task
      const result = await AutonomousAgent.executeTask(agentTask);

      // Publish agent completion event
      await publisher.publishEvent("agent_executions", {
        type: "success",
        nodeId: "agent-analysis",
        nodeName: "AI Candidate Analysis",
        timestamp: new Date(),
        data: {
          taskId: agentTask.id,
          candidateId: agentTask.candidateId,
          result: result.result,
        },
      });

      expect(AutonomousAgent.executeTask).toHaveBeenCalledWith(agentTask);
      expect(result.success).toBe(true);
      expect(mockChannel.send).toHaveBeenCalled();
    });
  });

  describe("Performance and Load Testing", () => {
    it("should handle concurrent workflow executions", async () => {
      const concurrentWorkflows = 10;
      const workflowPromises: Promise<void>[] = [];

      for (let i = 0; i < concurrentWorkflows; i++) {
        const workflowPromise = publisher.publishEvent(`workflow_${i}`, {
          type: "started",
          nodeId: `workflow-${i}-start`,
          nodeName: `Workflow ${i} Start`,
          timestamp: new Date(),
          data: {
            workflowId: `workflow-${i}`,
            candidateId: `candidate-${i}`,
            priority: i % 3 === 0 ? "high" : "normal",
          },
        });
        workflowPromises.push(workflowPromise);
      }

      // Execute all workflows concurrently
      await Promise.all(workflowPromises);

      // Verify all workflows were processed
      expect(mockChannel.send).toHaveBeenCalledTimes(concurrentWorkflows);
    });

    it("should handle high-frequency real-time events", async () => {
      const eventCount = 100;
      const channelName = "high_frequency_test";
      const events: Promise<void>[] = [];

      // Generate high-frequency events
      for (let i = 0; i < eventCount; i++) {
        const event = publisher.publishEvent(channelName, {
          type: "progress",
          nodeId: `progress-node-${i}`,
          nodeName: `Progress Node ${i}`,
          timestamp: new Date(),
          data: {
            iteration: i,
            progress: (i / eventCount) * 100,
            batchId: Math.floor(i / 10),
          },
        });
        events.push(event);
      }

      // Process all events
      await Promise.all(events);

      // Verify batching occurred (should be fewer calls than events due to batching)
      expect(mockChannel.send).toHaveBeenCalled();
      expect(mockChannel.send.mock.calls.length).toBeLessThanOrEqual(eventCount);
    });

    it("should maintain performance under load", async () => {
      const startTime = Date.now();
      const loadTestDuration = 1000; // 1 second
      const eventsPerSecond = 50;
      let eventCount = 0;

      // Simulate sustained load
      const loadInterval = setInterval(async () => {
        await publisher.publishEvent("load_test", {
          type: "progress",
          nodeId: `load-test-${eventCount}`,
          nodeName: `Load Test Event ${eventCount}`,
          timestamp: new Date(),
          data: {
            eventNumber: eventCount,
            timestamp: Date.now(),
          },
        });
        eventCount++;
      }, 1000 / eventsPerSecond);

      // Run load test
      vi.advanceTimersByTime(loadTestDuration);
      clearInterval(loadInterval);

      const endTime = Date.now();
      const actualDuration = endTime - startTime;

      // Verify performance metrics
      expect(eventCount).toBeGreaterThan(0);
      expect(mockChannel.send).toHaveBeenCalled();
    });
  });

  describe("Database Integration", () => {
    it("should handle complex database operations", async () => {
      const candidateId = "candidate-db-test";
      const workflowId = "workflow-db-test";

      // Mock complex database query chain
      mockSupabase.from().select().eq().eq().order().limit.mockResolvedValueOnce({
        data: [
          { id: candidateId, status: "screening", workflow_id: workflowId },
        ],
        error: null,
      });

      // Mock database update
      mockSupabase.from().update().eq().select().single.mockResolvedValueOnce({
        data: { id: candidateId, status: "interview_scheduled" },
        error: null,
      });

      // Simulate database-driven workflow
      await publisher.publishEvent("database_operations", {
        type: "started",
        nodeId: "db-query",
        nodeName: "Database Query",
        timestamp: new Date(),
        data: {
          operation: "candidate_status_update",
          candidateId,
          workflowId,
        },
      });

      // Verify database interactions
      expect(mockSupabase.from).toHaveBeenCalled();
      expect(mockChannel.send).toHaveBeenCalled();
    });

    it("should handle real-time subscription patterns", async () => {
      const subscriptionChannels = [
        "workflow_updates",
        "candidate_changes",
        "task_assignments",
        "notification_events",
      ];

      // Subscribe to multiple channels
      subscriptionChannels.forEach(channel => {
        publisher.subscribeToChannel(channel);
      });

      // Simulate real-time data changes
      for (const channel of subscriptionChannels) {
        await publisher.publishEvent(channel, {
          type: "progress",
          nodeId: `${channel}-update`,
          nodeName: `${channel} Update`,
          timestamp: new Date(),
          data: {
            channel,
            updateType: "real_time_sync",
            timestamp: Date.now(),
          },
        });
      }

      // Verify all subscriptions were created
      expect(mockSupabase.channel).toHaveBeenCalledTimes(
        subscriptionChannels.length + 1 // +1 for the initial channel in beforeEach
      );
    });

    it("should handle data consistency across services", async () => {
      // Create mock service implementations directly
      const TasksService = {
        getTaskById: vi.fn(),
        updateTask: vi.fn(),
      };

      const NotificationsService = {
        getNotifications: vi.fn(),
        sendNotification: vi.fn(),
      };

      const candidateId = "candidate-consistency-test";
      const taskId = "task-consistency-test";

      // Mock consistent data across services
      TasksService.getTaskById.mockResolvedValueOnce({
        success: true,
        data: {
          id: taskId,
          candidateId,
          status: "in_progress",
          assigneeId: "user-123",
        },
      });

      NotificationsService.getNotifications.mockResolvedValueOnce({
        success: true,
        data: [
          {
            id: "notification-123",
            taskId,
            candidateId,
            type: "task_update",
            status: "unread",
          },
        ],
      });

      // Verify data consistency
      const taskData = await TasksService.getTaskById(taskId);
      const notifications = await NotificationsService.getNotifications("user-123");

      // Publish consistency verification event
      await publisher.publishEvent("data_consistency", {
        type: "success",
        nodeId: "consistency-check",
        nodeName: "Data Consistency Check",
        timestamp: new Date(),
        data: {
          candidateId,
          taskId,
          consistencyStatus: "verified",
          services: ["tasks", "notifications"],
        },
      });

      expect(taskData.success).toBe(true);
      expect(notifications.success).toBe(true);
      expect(mockChannel.send).toHaveBeenCalled();
    });
  });
});
