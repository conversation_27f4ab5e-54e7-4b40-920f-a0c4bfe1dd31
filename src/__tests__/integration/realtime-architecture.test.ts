import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

// Mock Supabase realtime first
vi.mock("@/integrations/supabase/client", () => {
  const mockChannel = {
    subscribe: vi.fn(),
    send: vi.fn(),
    unsubscribe: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
  };

  return {
    supabase: {
      channel: vi.fn(() => mockChannel),
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn(() => ({
              data: null,
              error: null,
            })),
          })),
        })),
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => ({
              data: null,
              error: null,
            })),
          })),
        })),
        update: vi.fn(() => ({
          eq: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn(() => ({
                data: null,
                error: null,
              })),
            })),
          })),
        })),
      })),
    },
  };
});

// Mock React components that don't exist yet
vi.mock("@/components/ai/workflow/WorkflowExecutionEngine", () => ({
  WorkflowExecutionEngine: {
    executeWorkflow: vi.fn(),
    getExecutionStatus: vi.fn(),
    cancelExecution: vi.fn(),
  },
}));

// Import the actual ExecutionEvent type
import { ExecutionEvent } from "@/engine/types";

interface WorkflowExecutionEngine {
  executeWorkflow: (workflow: any) => Promise<any>;
  getExecutionStatus: (id: string) => Promise<any>;
  cancelExecution: (id: string) => Promise<boolean>;
}

import { RealtimeEventPublisher } from "@/engine/RealtimeEventPublisher";

// Mock workflow logger
vi.mock("@/services/WorkflowLogger", () => ({
  workflowLogger: {
    logExecution: vi.fn(),
    logError: vi.fn(),
    logMetrics: vi.fn(),
  },
  LogLevel: {
    INFO: "info",
    ERROR: "error",
    DEBUG: "debug",
  },
}));

// Mock workflow alert service
vi.mock("@/services/WorkflowAlertService", () => ({
  workflowAlertService: {
    checkAndSendAlerts: vi.fn(),
  },
}));

// Mock executor registry
vi.mock("@/engine/ExecutorRegistry", () => ({
  executorRegistry: {
    getExecutor: vi.fn(),
    getAllExecutors: vi.fn(() => []),
  },
}));

describe("Real-time Architecture Integration", () => {
  let publisher: RealtimeEventPublisher;
  let mockSupabase: any;
  let mockChannel: any;
  let mockWorkflow: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Get the mocked supabase instance
    const { supabase } = await import("@/integrations/supabase/client");
    mockSupabase = supabase;
    mockChannel = mockSupabase.channel();

    // Reset singleton
    (RealtimeEventPublisher as any).instance = undefined;
    publisher = RealtimeEventPublisher.getInstance();

    mockWorkflow = {
      id: "test-workflow-123",
      name: "Test Workflow",
      nodes: [
        {
          id: "node-1",
          type: "trigger",
          data: { type: "manual" },
          position: { x: 0, y: 0 },
        },
        {
          id: "node-2",
          type: "action",
          data: { type: "send-email", template: "welcome" },
          position: { x: 200, y: 0 },
        },
      ],
      edges: [
        {
          id: "edge-1",
          source: "node-1",
          target: "node-2",
        },
      ],
    };

    // Setup default mock responses
    mockChannel.subscribe.mockImplementation((callback) => {
      callback("SUBSCRIBED");
      return mockChannel;
    });
    mockChannel.send.mockResolvedValue({ status: "ok" });
  });

  afterEach(() => {
    vi.useRealTimers();
    publisher.cleanup();
  });

  describe("Real-time Event Flow", () => {
    it("should publish workflow events in real-time", async () => {
      const channelName = `workflow_progress_${mockWorkflow.id}`;
      const events: ExecutionEvent[] = [];

      // Capture published events
      mockChannel.send.mockImplementation(({ payload }) => {
        events.push(...payload.events);
        return Promise.resolve({ status: "ok" });
      });

      // Publish workflow events
      await publisher.publishEvent(channelName, {
        type: "started",
        nodeId: "node-1",
        nodeName: "Manual Trigger",
        timestamp: new Date(),
        data: { message: "Workflow started" },
      });

      await publisher.publishEvent(channelName, {
        type: "progress",
        nodeId: "node-1",
        nodeName: "Manual Trigger",
        timestamp: new Date(),
        data: { progress: 50 },
      });

      await publisher.publishEvent(channelName, {
        type: "success",
        nodeId: "node-2",
        nodeName: "Send Email",
        timestamp: new Date(),
        data: { result: "Email sent successfully" },
      });

      // Advance timers to trigger batch publishing
      vi.advanceTimersByTime(500);

      expect(events).toHaveLength(3);
      expect(events[0].type).toBe("started");
      expect(events[1].type).toBe("progress");
      expect(events[2].type).toBe("success");
    });

    it("should handle real-time subscription lifecycle", () => {
      const channelName = "test-channel";
      const subscriptionCallback = vi.fn();

      // Subscribe to channel
      const channel = publisher.subscribeToChannel(channelName);

      expect(mockSupabase.channel).toHaveBeenCalledWith(channelName, {
        config: {
          broadcast: {
            self: true,
            ack: true,
          },
        },
      });

      expect(mockChannel.subscribe).toHaveBeenCalled();
      expect(channel).toBe(mockChannel);
    });

    it("should handle real-time errors gracefully", async () => {
      const channelName = "error-channel";
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      mockChannel.send.mockRejectedValueOnce(new Error("Network error"));

      await publisher.publishEvent(channelName, {
        type: "error",
        nodeId: "node-1",
        nodeName: "Manual Trigger",
        timestamp: new Date(),
        data: { error: "Test error" },
      });

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining("Error publishing events"),
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe("Workflow Engine Integration", () => {
    it("should integrate with workflow execution", async () => {
      const executionConfig = {
        workflowId: mockWorkflow.id,
        userId: "user-123",
        context: { candidateId: "candidate-456" },
      };

      // Mock successful workflow execution
      mockSupabase.from().select().eq().single.mockResolvedValueOnce({
        data: mockWorkflow,
        error: null,
      });

      // Simulate workflow execution by publishing events
      await publisher.publishEvent("workflow-execution", {
        type: "started",
        nodeId: "node-1",
        nodeName: "Manual Trigger",
        timestamp: new Date(),
        data: executionConfig,
      });

      expect(mockChannel.send).toHaveBeenCalled();
    });

    it("should handle concurrent workflow executions", async () => {
      const workflow1Config = {
        workflowId: "workflow-1",
        userId: "user-123",
        context: { candidateId: "candidate-1" },
      };

      const workflow2Config = {
        workflowId: "workflow-2",
        userId: "user-123",
        context: { candidateId: "candidate-2" },
      };

      mockSupabase.from().select().eq().single
        .mockResolvedValueOnce({ data: { ...mockWorkflow, id: "workflow-1" }, error: null })
        .mockResolvedValueOnce({ data: { ...mockWorkflow, id: "workflow-2" }, error: null });

      // Simulate concurrent workflow executions
      const [result1, result2] = await Promise.all([
        publisher.publishEvent("workflow-1", {
          type: "started",
          nodeId: "node-1",
          nodeName: "Workflow 1 Trigger",
          timestamp: new Date(),
          data: workflow1Config,
        }),
        publisher.publishEvent("workflow-2", {
          type: "started",
          nodeId: "node-1",
          nodeName: "Workflow 2 Trigger",
          timestamp: new Date(),
          data: workflow2Config,
        }),
      ]);

      expect(mockChannel.send).toHaveBeenCalledTimes(2);

      // Should create separate channels for each workflow
      expect(mockSupabase.channel).toHaveBeenCalledWith(
        "workflow_progress_workflow-1",
        expect.any(Object)
      );
      expect(mockSupabase.channel).toHaveBeenCalledWith(
        "workflow_progress_workflow-2",
        expect.any(Object)
      );
    });
  });

  describe("Event Batching and Performance", () => {
    it("should batch multiple events efficiently", async () => {
      const channelName = "batch-test-channel";
      const eventCount = 10;

      // Publish multiple events rapidly
      for (let i = 0; i < eventCount; i++) {
        await publisher.publishEvent(channelName, {
          type: "progress",
          nodeId: `node-${i}`,
          nodeName: `Node ${i}`,
          timestamp: new Date(),
          data: { progress: i * 10 },
        });
      }

      // Advance timers to trigger batch publishing
      vi.advanceTimersByTime(500);

      // Should batch all events into a single send call
      expect(mockChannel.send).toHaveBeenCalledTimes(1);
      expect(mockChannel.send).toHaveBeenCalledWith({
        type: "broadcast",
        event: "workflow_events",
        payload: {
          events: expect.arrayContaining([
            expect.objectContaining({ nodeId: "node-0" }),
            expect.objectContaining({ nodeId: "node-9" }),
          ]),
          timestamp: expect.any(String),
        },
      });
    });

    it("should handle high-frequency event publishing", async () => {
      const channelName = "high-frequency-channel";
      const eventPromises = [];

      // Simulate high-frequency event publishing
      for (let i = 0; i < 100; i++) {
        eventPromises.push(
          publisher.publishEvent(channelName, {
            type: "progress",
            nodeId: "high-freq-node",
            nodeName: "High Frequency Node",
            timestamp: new Date(),
            data: { iteration: i },
          })
        );
      }

      await Promise.all(eventPromises);

      // Advance timers multiple times to simulate batch intervals
      vi.advanceTimersByTime(500);
      vi.advanceTimersByTime(500);
      vi.advanceTimersByTime(500);

      // Should handle all events without errors
      expect(mockChannel.send).toHaveBeenCalled();
    });
  });

  describe("Channel Management", () => {
    it("should manage multiple channels independently", async () => {
      const channel1 = "workflow_progress_1";
      const channel2 = "workflow_progress_2";
      const channel3 = "notifications_user_123";

      // Subscribe to multiple channels
      publisher.subscribeToChannel(channel1);
      publisher.subscribeToChannel(channel2);
      publisher.subscribeToChannel(channel3);

      expect(mockSupabase.channel).toHaveBeenCalledTimes(3);
      expect(mockSupabase.channel).toHaveBeenCalledWith(channel1, expect.any(Object));
      expect(mockSupabase.channel).toHaveBeenCalledWith(channel2, expect.any(Object));
      expect(mockSupabase.channel).toHaveBeenCalledWith(channel3, expect.any(Object));
    });

    it("should cleanup channels properly", () => {
      const channelName = "cleanup-test-channel";
      publisher.subscribeToChannel(channelName);

      publisher.cleanup();

      expect(mockChannel.unsubscribe).toHaveBeenCalled();
    });

    it("should reuse existing channels", () => {
      const channelName = "reuse-test-channel";

      const channel1 = publisher.subscribeToChannel(channelName);
      const channel2 = publisher.subscribeToChannel(channelName);

      expect(channel1).toBe(channel2);
      expect(mockSupabase.channel).toHaveBeenCalledTimes(1);
    });
  });

  describe("Error Recovery", () => {
    it("should recover from temporary network failures", async () => {
      const channelName = "recovery-test-channel";

      // First attempt fails
      mockChannel.send.mockRejectedValueOnce(new Error("Network timeout"));
      
      await publisher.publishEvent(channelName, {
        type: "error",
        nodeId: "node-1",
        nodeName: "Error Node",
        timestamp: new Date(),
        data: { error: "Test error" },
      });

      // Second attempt succeeds
      mockChannel.send.mockResolvedValueOnce({ status: "ok" });

      await publisher.publishEvent(channelName, {
        type: "retry",
        nodeId: "node-1",
        nodeName: "Retry Node",
        timestamp: new Date(),
        data: { attempt: 2 },
      });

      // Trigger batch publishing
      vi.advanceTimersByTime(500);

      // Should include both the failed and new events
      expect(mockChannel.send).toHaveBeenCalledWith({
        type: "broadcast",
        event: "workflow_events",
        payload: {
          events: expect.arrayContaining([
            expect.objectContaining({ type: "error" }),
            expect.objectContaining({ type: "retry" }),
          ]),
          timestamp: expect.any(String),
        },
      });
    });
  });
});
