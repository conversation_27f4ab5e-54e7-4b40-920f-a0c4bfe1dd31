import { describe, it, expect, vi, beforeEach, type MockedFunction } from "vitest";
import { SkillsMatchExecutor } from "@/engine/executors/skills-match";
import {
  createMockExecutionContext,
  createMockNodeData,
  mockCandidateData,
} from "../../utils/mockServices";

// Mock the services
vi.mock("@/services", () => ({
  CandidatesService: {
    getCandidate: vi.fn(),
  },
}));

// Mock the Gemini utility for AI-powered matching
vi.mock("@/utils/gemini", () => ({
  generateText: vi.fn(),
}));

describe("SkillsMatchExecutor", () => {
  let executor: SkillsMatchExecutor;
  let mockCandidatesService: any;
  let mockGenerateText: MockedFunction<any>;

  beforeEach(async () => {
    vi.clearAllMocks();
    executor = new SkillsMatchExecutor();

    // Get mocked services
    const services = await import("@/services");
    const geminiUtils = await import("@/utils/gemini");

    mockCandidatesService = vi.mocked(services.CandidatesService);
    mockGenerateText = vi.mocked(geminiUtils.generateText);

    // Setup default mock responses
    mockCandidatesService.getCandidate.mockResolvedValue(mockCandidateData);
  });

  describe("Basic Properties", () => {
    it("should have correct metadata", () => {
      expect(executor.id).toBe("skills-match");
      expect(executor.name).toBe("Skills Match");
      expect(executor.description).toBe(
        "Check if candidate has required skills",
      );
      expect(executor.category).toBe("condition");
    });

    it("should have correct config schema with AI options", () => {
      expect(executor.configSchema).toBeDefined();
      expect(executor.configSchema?.requiredSkills).toBeDefined();
      expect(executor.configSchema?.requiredSkills.type).toBe("text");
      expect(executor.configSchema?.requiredSkills.required).toBe(true);

      expect(executor.configSchema?.minMatchPercentage).toBeDefined();
      expect(executor.configSchema?.minMatchPercentage.type).toBe("number");
      expect(executor.configSchema?.minMatchPercentage.default).toBe(70);
      expect(executor.configSchema?.minMatchPercentage.min).toBe(0);
      expect(executor.configSchema?.minMatchPercentage.max).toBe(100);

      expect(executor.configSchema?.useAIMatching).toBeDefined();
      expect(executor.configSchema?.useAIMatching.type).toBe("boolean");
      expect(executor.configSchema?.useAIMatching.default).toBe(false);
      expect(executor.configSchema?.useAIMatching.description).toContain("AI-Powered");
    });

    it("should have correct config schema", () => {
      expect(executor.configSchema).toBeDefined();
      expect(executor.configSchema?.requiredSkills).toBeDefined();
      expect(executor.configSchema?.minMatchPercentage).toBeDefined();
      expect(executor.configSchema?.minMatchPercentage.default).toBe(70);
    });
  });

  describe("Execute Method - With Candidate Skills", () => {
    it("should match when candidate has all required skills", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React, Node.js",
        minMatchPercentage: 100,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: true,
        matchPercentage: 100,
        matchedSkills: 3,
        totalRequired: 3,
        threshold: 100,
      });
    });

    it("should match when candidate meets minimum percentage", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React, Python",
        minMatchPercentage: 60,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: true,
        matchPercentage: 66.66666666666666, // 2/3 skills
        matchedSkills: 2,
        totalRequired: 3,
        threshold: 60,
      });
    });

    it("should not match when candidate does not meet minimum percentage", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "Python, Ruby, Go, Rust",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: false,
        matchPercentage: 0,
        matchedSkills: 0,
        totalRequired: 4,
        threshold: 50,
      });
    });

    it("should handle case-insensitive matching", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "javascript, REACT, node.JS",
        minMatchPercentage: 100,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).result).toBe(true);
      expect((result.data as any).matchedSkills).toBe(3);
    });

    it("should handle skills as objects with name property", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      mockCandidatesService.getCandidate.mockResolvedValueOnce({
        ...mockCandidateData,
        skills: [
          { name: "JavaScript", level: "expert" },
          { name: "React", level: "intermediate" },
        ],
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).result).toBe(true);
      expect((result.data as any).matchedSkills).toBe(2);
    });

    it("should handle empty required skills", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "",
        minMatchPercentage: 70,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).matchPercentage).toBe(0);
      expect((result.data as any).totalRequired).toBe(0);
    });
  });

  describe("Execute Method - With AI Screening Result", () => {
    it("should use AI screening score from lastResult", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React",
        minMatchPercentage: 75,
      });
      const context = createMockExecutionContext({
        lastResult: { score: 80 },
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: true,
        score: 80,
        threshold: 75,
      });

      // Should not call CandidatesService when using AI score
      expect(
        mockCandidatesService.getCandidate,
      ).not.toHaveBeenCalled();
    });

    it("should fail AI screening when score is below threshold", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React",
        minMatchPercentage: 80,
      });
      const context = createMockExecutionContext({
        lastResult: { score: 70 },
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: false,
        score: 70,
        threshold: 80,
      });
    });
  });

  describe("Execute Method - Edge Cases", () => {
    it("should use candidateId from lastResult if not in context", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext({
        candidateId: undefined,
        lastResult: { candidateId: "last-result-candidate-id" },
      });

      mockCandidatesService.getCandidate.mockResolvedValueOnce({
        ...mockCandidateData,
        id: "last-result-candidate-id",
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(mockCandidatesService.getCandidate).toHaveBeenCalledWith(
        "last-result-candidate-id",
      );
    });

    it("should return false when no candidate data is available", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext({
        candidateId: undefined,
        lastResult: null,
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: false,
        reason: "No candidate data available",
      });
    });

    it("should fail when candidate is not found", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      mockCandidatesService.getCandidate.mockResolvedValueOnce(null);

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Candidate not found");
    });

    it("should handle candidate with no skills", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      mockCandidatesService.getCandidate.mockResolvedValueOnce({
        ...mockCandidateData,
        skills: null,
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: false,
        matchPercentage: 0,
        matchedSkills: 0,
      });
    });

    it("should handle whitespace in required skills", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "  JavaScript  ,   React   ,  Node.js  ",
        minMatchPercentage: 100,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).matchedSkills).toBe(3);
    });

    it("should handle partial skill name matches", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "Java, React",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).result).toBe(true);
      expect((result.data as any).matchedSkills).toBe(2); // JavaScript contains 'Java'
    });
  });

  describe("Timeout and Retry Behavior", () => {
    it("should handle timeout correctly", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      // Make the service hang
      mockCandidatesService.getCandidate.mockImplementationOnce(
        () => new Promise(() => {}), // Never resolves
      );

      const result = await executor.execute(nodeData, context, {
        timeout: 100, // 100ms timeout
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("timeout");
    });

    it("should retry on failure", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      // Fail first time, succeed second time
      mockCandidatesService.getCandidate
        .mockRejectedValueOnce(new Error("Temporary failure"))
        .mockResolvedValueOnce(mockCandidateData);

      const result = await executor.execute(nodeData, context, {
        retries: 1,
        retryDelay: 10,
      });

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
      expect(mockCandidatesService.getCandidate).toHaveBeenCalledTimes(
        2,
      );
    });
  });

  describe("AI-Powered Skills Matching", () => {
    it("should use AI matching when enabled", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "React, TypeScript, Node.js",
        minMatchPercentage: 70,
        useAIMatching: true,
      });
      const context = createMockExecutionContext();

      // Mock AI response
      mockGenerateText.mockResolvedValueOnce(JSON.stringify({
        matchPercentage: 85,
        analysis: "Strong match with React and TypeScript experience. Node.js experience through similar backend work.",
        matchedSkills: ["React", "TypeScript"],
        relatedSkills: ["JavaScript", "Express.js"],
        reasoning: "Candidate has direct experience with React and TypeScript, plus related backend experience."
      }));

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: true,
        matchPercentage: 85,
        aiAnalysis: expect.any(String),
      });

      expect(mockGenerateText).toHaveBeenCalledWith(
        expect.stringContaining("Required Skills: React, TypeScript, Node.js"),
        expect.stringContaining("expert technical recruiter"),
      );
    });

    it("should fallback to basic matching when AI fails", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "React, Vue.js",
        minMatchPercentage: 50,
        useAIMatching: true,
      });
      const context = createMockExecutionContext();

      // Mock AI failure
      mockGenerateText.mockRejectedValueOnce(new Error("AI service unavailable"));

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: expect.any(Boolean),
        matchPercentage: expect.any(Number),
        // Should not have AI analysis due to fallback
      });
      expect(result.data).not.toHaveProperty('aiAnalysis');
    });

    it("should handle malformed AI response", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "Python, Django",
        minMatchPercentage: 60,
        useAIMatching: true,
      });
      const context = createMockExecutionContext();

      // Mock malformed AI response
      mockGenerateText.mockResolvedValueOnce("Invalid JSON response");

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      // Should fallback to basic matching
      expect(result.data).toMatchObject({
        result: expect.any(Boolean),
        matchPercentage: expect.any(Number),
      });
      expect(result.data).not.toHaveProperty('aiAnalysis');
    });

    it("should clean AI response from markdown formatting", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "Java, Spring Boot",
        minMatchPercentage: 70,
        useAIMatching: true,
      });
      const context = createMockExecutionContext();

      // Mock AI response with markdown formatting
      mockGenerateText.mockResolvedValueOnce(`\`\`\`json
{
  "matchPercentage": 75,
  "analysis": "Good match with Java experience"
}
\`\`\``);

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: true,
        matchPercentage: 75,
      });
    });

    it("should handle AI timeout gracefully", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "C++, Embedded Systems",
        minMatchPercentage: 60,
        useAIMatching: true,
      });
      const context = createMockExecutionContext();

      // Mock AI timeout
      mockGenerateText.mockImplementationOnce(() => new Promise(() => {}));

      const result = await executor.execute(nodeData, context, {
        timeout: 100,
      });

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain("timeout");
    });

    it("should validate AI response structure", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "Go, Kubernetes",
        minMatchPercentage: 70,
        useAIMatching: true,
      });
      const context = createMockExecutionContext();

      // Mock AI response missing required fields
      mockGenerateText.mockResolvedValueOnce(JSON.stringify({
        analysis: "Some analysis but missing matchPercentage"
      }));

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      // Should fallback to basic matching when AI response is invalid
      expect(result.data).toMatchObject({
        result: expect.any(Boolean),
        matchPercentage: expect.any(Number),
      });
      expect(result.data).not.toHaveProperty('aiAnalysis');
    });
  });
});
