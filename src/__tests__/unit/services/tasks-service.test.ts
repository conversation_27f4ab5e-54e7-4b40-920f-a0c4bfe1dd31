import { describe, it, expect, vi, beforeEach } from "vitest";
import { TasksService, type Task, type CreateTaskData, type UpdateTaskData, type TaskFilters } from "@/services/TasksService";

// Mock Supabase
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => ({
            limit: vi.fn(() => ({
              data: [],
              error: null,
            })),
            data: [],
            error: null,
          })),
          lte: vi.fn(() => ({
            data: [],
            error: null,
          })),
          gte: vi.fn(() => ({
            data: [],
            error: null,
          })),
          ilike: vi.fn(() => ({
            data: [],
            error: null,
          })),
          data: [],
          error: null,
        })),
        order: vi.fn(() => ({
          data: [],
          error: null,
        })),
        data: [],
        error: null,
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => ({
            data: null,
            error: null,
          })),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => ({
              data: null,
              error: null,
            })),
          })),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(() => ({
          data: null,
          error: null,
        })),
      })),
    })),
  },
}));

// Helper function for creating mock chains
const createMockChain = (finalResult = { data: [], error: null }, singleResult?: any) => {
  const chain = {
    select: vi.fn(() => chain),
    eq: vi.fn(() => chain),
    order: vi.fn(() => chain),
    limit: vi.fn(() => finalResult),
    single: vi.fn(() => singleResult || finalResult),
    insert: vi.fn(() => chain),
    update: vi.fn(() => chain),
    delete: vi.fn(() => chain),
    lte: vi.fn(() => chain),
    gte: vi.fn(() => chain),
    ilike: vi.fn(() => chain),
    neq: vi.fn(() => chain),
    not: vi.fn(() => chain),
    lt: vi.fn(() => chain),
    in: vi.fn(() => finalResult),
    data: finalResult.data,
    error: finalResult.error,
  };
  return chain;
};

describe("TasksService", () => {
  const mockUserId = "user-123";
  const mockTask: Task = {
    id: "task-123",
    title: "Review candidate applications",
    description: "Review and screen new candidate applications",
    status: "pending",
    priority: "high",
    category: "recruitment",
    user_id: mockUserId,
    assignee: "John Doe",
    due_date: "2024-01-15",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  };

  let mockSupabase: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    // Get the mocked supabase instance
    const { supabase } = await import("@/integrations/supabase/client");
    mockSupabase = supabase;
  });

  describe("getTasks", () => {
    it("should fetch tasks for a user", async () => {
      const mockTasks = [mockTask];
      mockSupabase.from().select().eq().order.mockReturnValueOnce({
        data: mockTasks,
        error: null,
      });

      const result = await TasksService.getTasks(mockUserId);

      expect(mockSupabase.from).toHaveBeenCalledWith("tasks");
      expect(result).toEqual(mockTasks);
    });

    it("should apply status filter", async () => {
      const filters: TaskFilters = { status: "completed" };
      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue({ data: [], error: null }),
      };
      mockSupabase.from().select().eq.mockReturnValueOnce(mockQuery);

      await TasksService.getTasks(mockUserId, filters);

      expect(mockQuery.eq).toHaveBeenCalledWith("status", "completed");
    });

    it("should apply priority filter", async () => {
      const filters: TaskFilters = { priority: "high" };
      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue({ data: [], error: null }),
      };
      mockSupabase.from().select().eq.mockReturnValueOnce(mockQuery);

      await TasksService.getTasks(mockUserId, filters);

      expect(mockQuery.eq).toHaveBeenCalledWith("priority", "high");
    });

    it("should apply category filter", async () => {
      const filters: TaskFilters = { category: "recruitment" };
      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue({ data: [], error: null }),
      };
      mockSupabase.from().select().eq.mockReturnValueOnce(mockQuery);

      await TasksService.getTasks(mockUserId, filters);

      expect(mockQuery.eq).toHaveBeenCalledWith("category", "recruitment");
    });

    it("should apply assignee filter", async () => {
      const filters: TaskFilters = { assignee: "John" };
      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        ilike: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue({ data: [], error: null }),
      };
      mockSupabase.from().select().eq.mockReturnValueOnce(mockQuery);

      await TasksService.getTasks(mockUserId, filters);

      expect(mockQuery.ilike).toHaveBeenCalledWith("assignee", "%John%");
    });

    it("should apply date range filters", async () => {
      const filters: TaskFilters = {
        due_before: "2024-01-31",
        due_after: "2024-01-01",
      };
      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue({ data: [], error: null }),
      };
      mockSupabase.from().select().eq.mockReturnValueOnce(mockQuery);

      await TasksService.getTasks(mockUserId, filters);

      expect(mockQuery.lte).toHaveBeenCalledWith("due_date", "2024-01-31");
      expect(mockQuery.gte).toHaveBeenCalledWith("due_date", "2024-01-01");
    });

    it("should handle database errors", async () => {
      mockSupabase.from().select().eq().order.mockReturnValueOnce({
        data: null,
        error: new Error("Database error"),
      });

      await expect(TasksService.getTasks(mockUserId)).rejects.toThrow("Database error");
    });
  });

  describe("getTask", () => {
    it("should fetch a specific task", async () => {
      const mockChain = createMockChain(
        { data: [], error: null },
        { data: mockTask, error: null }
      );
      mockSupabase.from.mockReturnValueOnce(mockChain);

      const result = await TasksService.getTask("task-123");

      expect(result).toEqual(mockTask);
    });

    it("should handle task not found", async () => {
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      mockSupabase.from.mockReturnValueOnce(createMockChain({
        data: null,
        error: new Error("Task not found"),
      }));

      await expect(TasksService.getTask("non-existent")).rejects.toThrow("Task not found");
      expect(consoleErrorSpy).toHaveBeenCalled();

      consoleErrorSpy.mockRestore();
    });
  });

  describe("createTask", () => {
    const createTaskData: CreateTaskData = {
      title: "New task",
      description: "Task description",
      user_id: mockUserId,
      status: "pending",
      priority: "medium",
      category: "general",
    };

    it("should create a new task", async () => {
      mockSupabase.from().insert().select().single.mockReturnValueOnce({
        data: { ...createTaskData, id: "new-task-123" },
        error: null,
      });

      const result = await TasksService.createTask(createTaskData);

      expect(mockSupabase.from).toHaveBeenCalledWith("tasks");
      expect(result).toMatchObject(createTaskData);
    });

    it("should apply default values", async () => {
      const minimalTaskData: CreateTaskData = {
        title: "Minimal task",
        user_id: mockUserId,
      };

      mockSupabase.from().insert().select().single.mockReturnValueOnce({
        data: { ...minimalTaskData, id: "new-task-123" },
        error: null,
      });

      await TasksService.createTask(minimalTaskData);

      expect(mockSupabase.from().insert).toHaveBeenCalledWith({
        ...minimalTaskData,
        status: "pending",
        priority: "medium",
        category: "general",
      });
    });

    it("should validate required fields", async () => {
      const invalidTaskData = {
        title: "",
        user_id: mockUserId,
      } as CreateTaskData;

      await expect(TasksService.createTask(invalidTaskData)).rejects.toThrow(
        "Task title is required"
      );
    });

    it("should validate user ID", async () => {
      const invalidTaskData = {
        title: "Valid title",
        user_id: "",
      } as CreateTaskData;

      await expect(TasksService.createTask(invalidTaskData)).rejects.toThrow(
        "User ID is required"
      );
    });

    it("should handle creation errors", async () => {
      mockSupabase.from().insert().select().single.mockReturnValueOnce({
        data: null,
        error: new Error("Creation failed"),
      });

      await expect(TasksService.createTask(createTaskData)).rejects.toThrow("Creation failed");
    });
  });

  describe("updateTask", () => {
    const updateTaskData: UpdateTaskData = {
      title: "Updated task",
      status: "completed",
    };

    it("should update a task", async () => {
      const updatedTask = { ...mockTask, ...updateTaskData };
      const mockChain = createMockChain(
        { data: [], error: null },
        { data: updatedTask, error: null }
      );
      mockSupabase.from.mockReturnValueOnce(mockChain);

      const result = await TasksService.updateTask("task-123", updateTaskData);

      expect(mockSupabase.from).toHaveBeenCalledWith("tasks");
      expect(result).toEqual(updatedTask);
    });

    it("should handle update errors", async () => {
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      mockSupabase.from.mockReturnValueOnce(createMockChain({
        data: null,
        error: new Error("Update failed"),
      }));

      await expect(TasksService.updateTask("task-123", updateTaskData)).rejects.toThrow("Update failed");
      expect(consoleErrorSpy).toHaveBeenCalled();

      consoleErrorSpy.mockRestore();
    });
  });

  describe("deleteTask", () => {
    it("should delete a task", async () => {
      mockSupabase.from().delete().eq.mockReturnValueOnce({
        data: null,
        error: null,
      });

      await expect(TasksService.deleteTask("task-123")).resolves.not.toThrow();

      expect(mockSupabase.from).toHaveBeenCalledWith("tasks");
    });

    it("should handle deletion errors", async () => {
      mockSupabase.from().delete().eq.mockReturnValueOnce({
        data: null,
        error: new Error("Deletion failed"),
      });

      await expect(TasksService.deleteTask("task-123")).rejects.toThrow("Deletion failed");
    });
  });

  describe("getTaskStats", () => {
    it("should calculate task statistics", async () => {
      const mockTasks = [mockTask];
      const overdueTasks = [mockTask];

      // Mock the service methods
      vi.spyOn(TasksService, 'getTasks').mockResolvedValueOnce(mockTasks);
      vi.spyOn(TasksService, 'getOverdueTasks').mockResolvedValueOnce(overdueTasks);

      const result = await TasksService.getTaskStats(mockUserId);

      expect(result).toEqual({
        total: 1,
        pending: 1,
        inProgress: 0,
        completed: 0,
        overdue: 1,
      });
    });
  });

  describe("getOverdueTasks", () => {
    it("should fetch overdue tasks", async () => {
      const mockTasks = [mockTask];
      mockSupabase.from.mockReturnValueOnce(createMockChain({
        data: mockTasks,
        error: null,
      }));

      const result = await TasksService.getOverdueTasks(mockUserId);

      expect(result).toEqual(mockTasks);
    });
  });

  describe("Edge Cases", () => {
    it("should handle empty results", async () => {
      mockSupabase.from().select().eq().order.mockReturnValueOnce({
        data: [],
        error: null,
      });

      const result = await TasksService.getTasks(mockUserId);

      expect(result).toEqual([]);
    });

    it("should handle null data responses", async () => {
      mockSupabase.from().select().eq().order.mockReturnValueOnce({
        data: null,
        error: null,
      });

      const result = await TasksService.getTasks(mockUserId);

      expect(result).toEqual([]);
    });

    it("should handle multiple filters", async () => {
      const filters: TaskFilters = {
        status: "pending",
        priority: "high",
        category: "recruitment",
        assignee: "John",
        due_before: "2024-01-31",
        due_after: "2024-01-01",
      };

      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        ilike: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue({ data: [], error: null }),
      };
      mockSupabase.from().select().eq.mockReturnValueOnce(mockQuery);

      await TasksService.getTasks(mockUserId, filters);

      expect(mockQuery.eq).toHaveBeenCalledTimes(4); // user_id, status, priority, category
      expect(mockQuery.ilike).toHaveBeenCalledWith("assignee", "%John%");
      expect(mockQuery.lte).toHaveBeenCalledWith("due_date", "2024-01-31");
      expect(mockQuery.gte).toHaveBeenCalledWith("due_date", "2024-01-01");
    });
  });
});
