import { describe, it, expect, vi, beforeEach } from "vitest";
import { 
  NotificationsService, 
  type NotificationData, 
  type CreateNotificationData, 
  type UpdateNotificationData 
} from "@/services/NotificationsService";

// Mock Supabase with comprehensive chaining support
const createMockChain = (finalResult = { data: [], error: null }) => {
  const chain = {
    select: vi.fn(() => chain),
    eq: vi.fn(() => chain),
    order: vi.fn(() => chain),
    limit: vi.fn(() => finalResult),
    single: vi.fn(() => finalResult),
    insert: vi.fn(() => chain),
    update: vi.fn(() => chain),
    delete: vi.fn(() => chain),
    data: finalResult.data,
    error: finalResult.error,
  };
  return chain;
};

vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn(() => createMockChain()),
  },
}));

describe("NotificationsService", () => {
  const mockUserId = "user-123";
  const mockNotification: NotificationData = {
    id: "notification-123",
    user_id: mockUserId,
    type: "candidate",
    title: "New Application",
    message: "You have a new application for Frontend Developer position",
    read: false,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    metadata: { jobId: "job-456", candidateId: "candidate-789" },
  };

  let mockSupabase: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    // Get the mocked supabase instance
    const { supabase } = await import("@/integrations/supabase/client");
    mockSupabase = supabase;
  });

  describe("getNotifications", () => {
    it("should fetch notifications for a user", async () => {
      const mockNotifications = [mockNotification];

      // Mock the chain to return our test data
      mockSupabase.from.mockReturnValueOnce(createMockChain({
        data: mockNotifications,
        error: null,
      }));

      const result = await NotificationsService.getNotifications(mockUserId);

      expect(mockSupabase.from).toHaveBeenCalledWith("notifications");
      expect(result).toEqual(mockNotifications);
    });

    it("should apply limit parameter", async () => {
      const limit = 25;
      const mockChain = createMockChain({ data: [], error: null });
      mockSupabase.from.mockReturnValueOnce(mockChain);

      await NotificationsService.getNotifications(mockUserId, limit);

      expect(mockChain.limit).toHaveBeenCalledWith(limit);
    });

    it("should use default limit of 50", async () => {
      const mockChain = createMockChain({ data: [], error: null });
      mockSupabase.from.mockReturnValueOnce(mockChain);

      await NotificationsService.getNotifications(mockUserId);

      expect(mockChain.limit).toHaveBeenCalledWith(50);
    });

    it("should handle database errors", async () => {
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      mockSupabase.from.mockReturnValueOnce(createMockChain({
        data: null,
        error: new Error("Database error"),
      }));

      await expect(NotificationsService.getNotifications(mockUserId)).rejects.toThrow("Database error");
      expect(consoleErrorSpy).toHaveBeenCalled();

      consoleErrorSpy.mockRestore();
    });
  });

  describe("getUnreadNotifications", () => {
    it("should fetch unread notifications", async () => {
      const unreadNotifications = [{ ...mockNotification, read: false }];
      mockSupabase.from().select().eq().order.mockReturnValueOnce({
        data: unreadNotifications,
        error: null,
      });

      const result = await NotificationsService.getUnreadNotifications(mockUserId);

      expect(result).toEqual(unreadNotifications);
    });

    it("should filter by read status", async () => {
      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue({ data: [], error: null }),
      };
      mockSupabase.from().select().eq.mockReturnValueOnce(mockQuery);

      await NotificationsService.getUnreadNotifications(mockUserId);

      expect(mockQuery.eq).toHaveBeenCalledWith("user_id", mockUserId);
      expect(mockQuery.eq).toHaveBeenCalledWith("read", false);
    });
  });

  describe("createNotification", () => {
    const createNotificationData: CreateNotificationData = {
      user_id: mockUserId,
      type: "interview",
      title: "Interview Scheduled",
      message: "Your interview has been scheduled for tomorrow",
      metadata: { interviewId: "interview-123" },
    };

    it("should create a new notification", async () => {
      const createdNotification = { ...createNotificationData, id: "new-notification-123" };
      mockSupabase.from.mockReturnValueOnce(createMockChain({
        data: [createdNotification],
        error: null,
      }));

      const result = await NotificationsService.createNotification(createNotificationData);

      expect(mockSupabase.from).toHaveBeenCalledWith("notifications");
      expect(result).toEqual(createdNotification);
    });

    it("should handle missing metadata", async () => {
      const dataWithoutMetadata: CreateNotificationData = {
        user_id: mockUserId,
        type: "system",
        title: "Reminder",
        message: "Don't forget to review applications",
      };

      const createdData = { ...dataWithoutMetadata, id: "new-notification-123" };
      const mockChain = createMockChain({
        data: [createdData],
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(mockChain);

      await NotificationsService.createNotification(dataWithoutMetadata);

      expect(mockChain.insert).toHaveBeenCalledWith([{
        ...dataWithoutMetadata,
        metadata: {},
      }]);
    });

    it("should handle creation errors", async () => {
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      mockSupabase.from().insert().select().single.mockReturnValueOnce({
        data: null,
        error: new Error("Creation failed"),
      });

      await expect(NotificationsService.createNotification(createNotificationData)).rejects.toThrow("Creation failed");
      expect(consoleErrorSpy).toHaveBeenCalled();
      
      consoleErrorSpy.mockRestore();
    });
  });

  describe("updateNotification", () => {
    const updateNotificationData: UpdateNotificationData = {
      id: "notification-123",
      read: true,
    };

    it("should update a notification", async () => {
      const updatedNotification = { ...mockNotification, read: true };
      mockSupabase.from().update().eq().select().single.mockReturnValueOnce({
        data: updatedNotification,
        error: null,
      });

      const result = await NotificationsService.updateNotification(updateNotificationData);

      expect(mockSupabase.from).toHaveBeenCalledWith("notifications");
      expect(result).toEqual(updatedNotification);
    });

    it("should exclude id from update data", async () => {
      mockSupabase.from().update().eq().select().single.mockReturnValueOnce({
        data: mockNotification,
        error: null,
      });

      await NotificationsService.updateNotification(updateNotificationData);

      expect(mockSupabase.from().update).toHaveBeenCalledWith({ read: true });
    });

    it("should handle update errors", async () => {
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      mockSupabase.from().update().eq().select().single.mockReturnValueOnce({
        data: null,
        error: new Error("Update failed"),
      });

      await expect(NotificationsService.updateNotification(updateNotificationData)).rejects.toThrow("Update failed");
      expect(consoleErrorSpy).toHaveBeenCalled();
      
      consoleErrorSpy.mockRestore();
    });
  });

  describe("markAsRead", () => {
    it("should mark notification as read", async () => {
      const readNotification = { ...mockNotification, read: true };
      mockSupabase.from().update().eq().select().single.mockReturnValueOnce({
        data: readNotification,
        error: null,
      });

      const result = await NotificationsService.markAsRead("notification-123");

      expect(result.read).toBe(true);
    });
  });

  describe("markAllAsRead", () => {
    it("should mark all notifications as read", async () => {
      mockSupabase.from().update().eq().eq.mockReturnValueOnce({
        data: null,
        error: null,
      });

      await expect(NotificationsService.markAllAsRead(mockUserId)).resolves.not.toThrow();

      expect(mockSupabase.from).toHaveBeenCalledWith("notifications");
    });
  });

  describe("deleteNotification", () => {
    it("should delete a notification", async () => {
      mockSupabase.from().delete().eq.mockReturnValueOnce({
        data: null,
        error: null,
      });

      await expect(NotificationsService.deleteNotification("notification-123")).resolves.not.toThrow();

      expect(mockSupabase.from).toHaveBeenCalledWith("notifications");
    });

    it("should handle deletion errors", async () => {
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      mockSupabase.from().delete().eq.mockReturnValueOnce({
        data: null,
        error: new Error("Deletion failed"),
      });

      await expect(NotificationsService.deleteNotification("notification-123")).rejects.toThrow("Deletion failed");
      expect(consoleErrorSpy).toHaveBeenCalled();
      
      consoleErrorSpy.mockRestore();
    });
  });

  describe("initializeDefaultNotifications", () => {
    it("should create default notifications for a user", async () => {
      const defaultNotifications = [
        { ...mockNotification, type: "interview" as const },
        { ...mockNotification, type: "deadline" as const },
      ];

      // Mock getNotifications to return empty array (no existing notifications)
      mockSupabase.from().select().eq().order().limit.mockReturnValueOnce({
        data: [],
        error: null,
      });

      // Mock createNotification calls
      mockSupabase.from().insert().select().single
        .mockResolvedValueOnce({ data: defaultNotifications[0], error: null })
        .mockResolvedValueOnce({ data: defaultNotifications[1], error: null })
        .mockResolvedValueOnce({ data: { ...mockNotification, type: "candidate" }, error: null })
        .mockResolvedValueOnce({ data: { ...mockNotification, type: "message" }, error: null });

      const result = await NotificationsService.initializeDefaultNotifications(mockUserId);

      expect(result).toHaveLength(4);
      expect(result[0].type).toBe("interview");
      expect(result[1].type).toBe("deadline");
    });
  });

  describe("getNotificationsByType", () => {
    it("should fetch notifications by type", async () => {
      const candidateNotifications = [{ ...mockNotification, type: "candidate" as const }];
      mockSupabase.from().select().eq().order.mockReturnValueOnce({
        data: candidateNotifications,
        error: null,
      });

      const result = await NotificationsService.getNotificationsByType(mockUserId, "candidate");

      expect(result).toEqual(candidateNotifications);
    });

    it("should filter by notification type", async () => {
      const mockQuery = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue({ data: [], error: null }),
      };
      mockSupabase.from().select().eq.mockReturnValueOnce(mockQuery);

      await NotificationsService.getNotificationsByType(mockUserId, "interview");

      expect(mockQuery.eq).toHaveBeenCalledWith("user_id", mockUserId);
      expect(mockQuery.eq).toHaveBeenCalledWith("type", "interview");
    });
  });

  describe("Edge Cases", () => {
    it("should handle empty results", async () => {
      mockSupabase.from().select().eq().order().limit.mockReturnValueOnce({
        data: [],
        error: null,
      });

      const result = await NotificationsService.getNotifications(mockUserId);

      expect(result).toEqual([]);
    });

    it("should handle null data responses", async () => {
      mockSupabase.from().select().eq().order().limit.mockReturnValueOnce({
        data: null,
        error: null,
      });

      const result = await NotificationsService.getNotifications(mockUserId);

      expect(result).toEqual([]);
    });

    it("should handle notifications with complex metadata", async () => {
      const complexNotificationData: CreateNotificationData = {
        user_id: mockUserId,
        type: "system",
        title: "Workflow Completed",
        message: "Your recruitment workflow has completed successfully",
        metadata: {
          workflowId: "workflow-123",
          candidateIds: ["candidate-1", "candidate-2"],
          results: {
            screened: 2,
            passed: 1,
            rejected: 1,
          },
        },
      };

      mockSupabase.from().insert().select().single.mockReturnValueOnce({
        data: { ...complexNotificationData, id: "new-notification-123" },
        error: null,
      });

      const result = await NotificationsService.createNotification(complexNotificationData);

      expect(result.metadata).toEqual(complexNotificationData.metadata);
    });

    it("should handle bulk operations", async () => {
      const notifications = [mockNotification, { ...mockNotification, id: "notification-456" }];
      mockSupabase.from().select().eq().order().limit.mockReturnValueOnce({
        data: notifications,
        error: null,
      });

      const result = await NotificationsService.getNotifications(mockUserId, 100);

      expect(result).toHaveLength(2);
    });
  });
});
