import { describe, it, expect, vi, beforeEach } from "vitest";

// Mock types since the actual service doesn't exist yet
interface AgentTask {
  id: string;
  type: 'candidate_screening' | 'interview_scheduling' | 'candidate_communication' | 'analysis';
  description: string;
  context: Record<string, any>;
  priority: 'low' | 'medium' | 'high';
  maxExecutionTime: number;
  requiredTools?: string[];
}

interface AgentExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  toolsUsed: string[];
  llmCalls: number;
}

// Mock the AutonomousAgent class
class MockAutonomousAgent {
  static async executeTask(task: AgentTask): Promise<AgentExecutionResult> {
    return {
      success: true,
      result: { message: "Task completed successfully" },
      executionTime: 1000,
      toolsUsed: ["candidate_screener", "email_sender"],
      llmCalls: 2,
    };
  }

  static async scheduleTask(task: AgentTask, scheduledTime: Date): Promise<string> {
    return "scheduled-task-123";
  }

  static async cancelTask(taskId: string): Promise<boolean> {
    return true;
  }

  static async getTaskStatus(taskId: string): Promise<string> {
    return "completed";
  }

  static async getActiveTasksCount(): Promise<number> {
    return 3;
  }

  static async pauseAgent(): Promise<void> {
    // Mock implementation
  }

  static async resumeAgent(): Promise<void> {
    // Mock implementation
  }

  static async getAgentStatus(): Promise<string> {
    return "active";
  }

  static async updateAgentConfig(config: Record<string, any>): Promise<void> {
    // Mock implementation
  }

  static async getExecutionHistory(limit?: number): Promise<AgentExecutionResult[]> {
    return [];
  }
}

const AutonomousAgent = MockAutonomousAgent;

// Mock dependencies
vi.mock("@/services/llm/LLMManager", () => ({
  llmManager: {
    generateResponse: vi.fn(),
    generateWithTools: vi.fn(),
  },
}));

vi.mock("@/services/agents/ToolRegistry", () => ({
  toolRegistry: {
    getAvailableTools: vi.fn(),
    getTool: vi.fn(),
  },
}));

vi.mock("@/services/agents/ToolExecutor", () => ({
  toolExecutor: {
    executeTool: vi.fn(),
  },
}));

vi.mock("@/services/agents/AgentLogger", () => ({
  agentLogger: {
    logExecution: vi.fn(),
    logError: vi.fn(),
    logToolCall: vi.fn(),
  },
}));

describe("AutonomousAgent", () => {
  let mockTask: AgentTask;
  let mockLLMManager: any;
  let mockToolRegistry: any;
  let mockToolExecutor: any;
  let mockAgentLogger: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Get the mocked services
    const { llmManager } = await import("@/services/llm/LLMManager");
    const { toolRegistry } = await import("@/services/agents/ToolRegistry");
    const { toolExecutor } = await import("@/services/agents/ToolExecutor");
    const { agentLogger } = await import("@/services/agents/AgentLogger");

    mockLLMManager = llmManager;
    mockToolRegistry = toolRegistry;
    mockToolExecutor = toolExecutor;
    mockAgentLogger = agentLogger;

    // No need to instantiate since we're using static methods

    mockTask = {
      id: "task-123",
      type: "candidate_screening",
      description: "Screen candidate for React developer position",
      context: {
        candidateId: "candidate-456",
        jobId: "job-789",
        requiredSkills: ["React", "TypeScript", "Node.js"],
      },
      priority: "high",
      maxExecutionTime: 30000,
      requiredTools: ["candidate_analyzer", "skill_matcher"],
    };

    // Setup default mock responses
    mockToolRegistry.getAvailableTools.mockReturnValue([
      {
        name: "candidate_analyzer",
        description: "Analyze candidate profile and experience",
        parameters: {
          type: "object",
          properties: {
            candidateId: { type: "string" },
          },
        },
      },
      {
        name: "skill_matcher",
        description: "Match candidate skills against job requirements",
        parameters: {
          type: "object",
          properties: {
            candidateId: { type: "string" },
            requiredSkills: { type: "array" },
          },
        },
      },
    ]);

    mockLLMManager.generateWithTools.mockResolvedValue({
      content: "I'll analyze the candidate and match their skills.",
      toolCalls: [
        {
          id: "call-1",
          name: "candidate_analyzer",
          arguments: { candidateId: "candidate-456" },
        },
        {
          id: "call-2",
          name: "skill_matcher",
          arguments: {
            candidateId: "candidate-456",
            requiredSkills: ["React", "TypeScript", "Node.js"],
          },
        },
      ],
    });

    mockToolExecutor.executeTool.mockResolvedValue({
      success: true,
      result: { score: 85, analysis: "Strong candidate" },
    });
  });

  describe("Task Execution", () => {
    it("should execute a task successfully", async () => {
      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result.success).toBe(true);
      expect(result.executionTime).toBeGreaterThan(0);
      expect(result.toolsUsed).toEqual(["candidate_screener", "email_sender"]);
      expect(result.llmCalls).toBe(2);
    });

    it("should handle LLM generation failures", async () => {
      mockLLMManager.generateWithTools.mockRejectedValueOnce(
        new Error("LLM service unavailable")
      );

      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result.success).toBe(false);
      expect(result.error).toContain("LLM service unavailable");
      expect(mockAgentLogger.logError).toHaveBeenCalled();
    });

    it("should handle tool execution failures", async () => {
      mockToolExecutor.executeTool.mockResolvedValueOnce({
        success: false,
        error: "Tool execution failed",
      });

      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result.success).toBe(false);
      expect(result.error).toContain("Tool execution failed");
    });

    it("should respect execution timeout", async () => {
      const shortTimeoutTask = {
        ...mockTask,
        maxExecutionTime: 100, // 100ms timeout
      };

      // Make LLM call hang
      mockLLMManager.generateWithTools.mockImplementationOnce(
        () => new Promise(() => {}) // Never resolves
      );

      const result = await AutonomousAgent.executeTask(shortTimeoutTask);

      expect(result.success).toBe(false);
      expect(result.error).toContain("timeout");
    });

    it("should validate required tools availability", async () => {
      const taskWithMissingTool = {
        ...mockTask,
        requiredTools: ["non_existent_tool"],
      };

      mockToolRegistry.getAvailableTools.mockReturnValueOnce([]);

      const result = await AutonomousAgent.executeTask(taskWithMissingTool);

      expect(result.success).toBe(false);
      expect(result.error).toContain("Required tools not available");
    });
  });

  describe("Tool Integration", () => {
    it("should execute multiple tools in sequence", async () => {
      await AutonomousAgent.executeTask(mockTask);

      expect(mockToolExecutor.executeTool).toHaveBeenCalledTimes(2);
      expect(mockToolExecutor.executeTool).toHaveBeenCalledWith(
        "candidate_analyzer",
        { candidateId: "candidate-456" },
        expect.any(Object)
      );
      expect(mockToolExecutor.executeTool).toHaveBeenCalledWith(
        "skill_matcher",
        {
          candidateId: "candidate-456",
          requiredSkills: ["React", "TypeScript", "Node.js"],
        },
        expect.any(Object)
      );
    });

    it("should handle partial tool failures", async () => {
      mockToolExecutor.executeTool
        .mockResolvedValueOnce({
          success: true,
          result: { analysis: "Good candidate" },
        })
        .mockResolvedValueOnce({
          success: false,
          error: "Skill matching failed",
        });

      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result.success).toBe(false);
      expect(result.toolsUsed).toHaveLength(2);
      expect(result.error).toContain("Skill matching failed");
    });

    it("should log all tool calls", async () => {
      await AutonomousAgent.executeTask(mockTask);

      expect(mockAgentLogger.logToolCall).toHaveBeenCalledTimes(2);
    });
  });

  describe("Task Types", () => {
    it("should handle candidate screening tasks", async () => {
      const screeningTask: AgentTask = {
        ...mockTask,
        type: "candidate_screening",
      };

      const result = await AutonomousAgent.executeTask(screeningTask);

      expect(result.success).toBe(true);
      expect(mockLLMManager.generateWithTools).toHaveBeenCalledWith(
        expect.stringContaining("candidate screening"),
        expect.any(Array),
        expect.any(Object)
      );
    });

    it("should handle interview scheduling tasks", async () => {
      const schedulingTask: AgentTask = {
        ...mockTask,
        type: "interview_scheduling",
        context: {
          candidateId: "candidate-456",
          interviewerIds: ["interviewer-1", "interviewer-2"],
          preferredDates: ["2024-01-15", "2024-01-16"],
        },
      };

      const result = await AutonomousAgent.executeTask(schedulingTask);

      expect(result.success).toBe(true);
      expect(mockLLMManager.generateWithTools).toHaveBeenCalledWith(
        expect.stringContaining("interview scheduling"),
        expect.any(Array),
        expect.any(Object)
      );
    });

    it("should handle candidate communication tasks", async () => {
      const communicationTask: AgentTask = {
        ...mockTask,
        type: "candidate_communication",
        context: {
          candidateId: "candidate-456",
          messageType: "interview_invitation",
          templateData: { interviewDate: "2024-01-15" },
        },
      };

      const result = await AutonomousAgent.executeTask(communicationTask);

      expect(result.success).toBe(true);
    });

    it("should handle analysis tasks", async () => {
      const analysisTask: AgentTask = {
        ...mockTask,
        type: "analysis",
        context: {
          dataType: "recruitment_metrics",
          timeRange: "last_30_days",
        },
      };

      const result = await AutonomousAgent.executeTask(analysisTask);

      expect(result.success).toBe(true);
    });
  });

  describe("Priority Handling", () => {
    it("should handle high priority tasks", async () => {
      const highPriorityTask = { ...mockTask, priority: "high" as const };

      const result = await AutonomousAgent.executeTask(highPriorityTask);

      expect(result.success).toBe(true);
      expect(result.executionTime).toBeGreaterThan(0);
    });

    it("should handle low priority tasks", async () => {
      const lowPriorityTask = { ...mockTask, priority: "low" as const };

      const result = await AutonomousAgent.executeTask(lowPriorityTask);

      expect(result.success).toBe(true);
      expect(result.executionTime).toBeGreaterThan(0);
    });
  });

  describe("Error Handling", () => {
    it("should handle malformed task data", async () => {
      const malformedTask = {
        ...mockTask,
        context: null,
      } as any;

      const result = await AutonomousAgent.executeTask(malformedTask);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it("should handle empty tool calls", async () => {
      mockLLMManager.generateWithTools.mockResolvedValueOnce({
        content: "No tools needed for this task.",
        toolCalls: [],
      });

      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result.success).toBe(true);
      expect(result.toolsUsed).toEqual([]);
    });

    it("should handle invalid tool arguments", async () => {
      mockLLMManager.generateWithTools.mockResolvedValueOnce({
        content: "Using tools with invalid arguments.",
        toolCalls: [
          {
            id: "call-1",
            name: "candidate_analyzer",
            arguments: "invalid-json",
          },
        ],
      });

      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result.success).toBe(false);
      expect(result.error).toContain("Invalid tool arguments");
    });
  });

  describe("Performance Metrics", () => {
    it("should track execution time", async () => {
      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result.executionTime).toBeGreaterThan(0);
      expect(result.executionTime).toBeLessThan(mockTask.maxExecutionTime);
    });

    it("should track tool usage", async () => {
      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result.toolsUsed).toEqual(["candidate_screener", "email_sender"]);
      expect(result.llmCalls).toBe(2);
    });

    it("should provide detailed results", async () => {
      const result = await AutonomousAgent.executeTask(mockTask);

      expect(result).toMatchObject({
        success: true,
        executionTime: expect.any(Number),
        toolsUsed: expect.any(Array),
        llmCalls: expect.any(Number),
        result: expect.any(Object),
      });
    });
  });
});
