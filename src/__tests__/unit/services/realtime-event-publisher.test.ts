import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { RealtimeEventPublisher } from "@/engine/RealtimeEventPublisher";
import { ExecutionEvent } from "@/engine/types";

// Mock Supabase
const mockChannel = {
  subscribe: vi.fn(),
  send: vi.fn(),
  unsubscribe: vi.fn(),
};

vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    channel: vi.fn(() => mockChannel),
  },
}));

describe("RealtimeEventPublisher", () => {
  let publisher: RealtimeEventPublisher;
  let mockEvent: ExecutionEvent;
  let mockSupabase: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Get the mocked supabase instance
    const { supabase } = await import("@/integrations/supabase/client");
    mockSupabase = supabase;

    // Reset singleton instance
    (RealtimeEventPublisher as any).instance = undefined;
    publisher = RealtimeEventPublisher.getInstance();

    mockEvent = {
      type: "progress",
      nodeId: "test-node-1",
      nodeName: "Test Node",
      timestamp: new Date(),
      data: { progress: 50 },
    };

    // Setup default mock responses
    mockChannel.subscribe.mockImplementation((callback) => {
      callback("SUBSCRIBED");
      return mockChannel;
    });
    mockChannel.send.mockResolvedValue({ status: "ok" });
  });

  afterEach(() => {
    vi.useRealTimers();
    publisher.cleanup();
  });

  describe("Singleton Pattern", () => {
    it("should return the same instance", () => {
      const instance1 = RealtimeEventPublisher.getInstance();
      const instance2 = RealtimeEventPublisher.getInstance();
      
      expect(instance1).toBe(instance2);
    });

    it("should initialize batch publishing on creation", () => {
      expect(publisher).toBeDefined();
      // Batch publishing should be started automatically
    });
  });

  describe("Channel Management", () => {
    it("should create and subscribe to new channels", () => {
      const channelName = "test-channel";
      
      const channel = publisher.subscribeToChannel(channelName);
      
      expect(mockSupabase.channel).toHaveBeenCalledWith(channelName, {
        config: {
          broadcast: {
            self: true,
            ack: true,
          },
        },
      });
      expect(mockChannel.subscribe).toHaveBeenCalled();
      expect(channel).toBe(mockChannel);
    });

    it("should reuse existing channels", () => {
      const channelName = "test-channel";
      
      const channel1 = publisher.subscribeToChannel(channelName);
      const channel2 = publisher.subscribeToChannel(channelName);
      
      expect(mockSupabase.channel).toHaveBeenCalledTimes(1);
      expect(channel1).toBe(channel2);
    });

    it("should handle subscription status updates", () => {
      const channelName = "test-channel";
      const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});
      
      publisher.subscribeToChannel(channelName);
      
      // Simulate subscription callback
      const subscribeCallback = mockChannel.subscribe.mock.calls[0][0];
      subscribeCallback("SUBSCRIBED");
      
      expect(consoleSpy).toHaveBeenCalledWith(
        `Channel ${channelName} subscription status:`,
        "SUBSCRIBED"
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe("Event Publishing", () => {
    it("should queue events for batch publishing", async () => {
      const channelName = "workflow_progress_123";
      
      await publisher.publishEvent(channelName, mockEvent);
      
      expect(mockSupabase.channel).toHaveBeenCalledWith(channelName, expect.any(Object));
      // Event should be queued, not immediately sent for non-critical events
    });

    it("should immediately publish critical events", async () => {
      const channelName = "workflow_progress_123";
      const criticalEvent: ExecutionEvent = {
        ...mockEvent,
        type: "error",
      };
      
      await publisher.publishEvent(channelName, criticalEvent);
      
      expect(mockChannel.send).toHaveBeenCalledWith({
        type: "broadcast",
        event: "workflow_events",
        payload: {
          events: [criticalEvent],
          timestamp: expect.any(String),
        },
      });
    });

    it("should immediately publish started events", async () => {
      const channelName = "workflow_progress_123";
      const startedEvent: ExecutionEvent = {
        ...mockEvent,
        type: "started",
      };
      
      await publisher.publishEvent(channelName, startedEvent);
      
      expect(mockChannel.send).toHaveBeenCalledWith({
        type: "broadcast",
        event: "workflow_events",
        payload: {
          events: [startedEvent],
          timestamp: expect.any(String),
        },
      });
    });

    it("should publish multiple events", async () => {
      const channelName = "workflow_progress_123";
      const events: ExecutionEvent[] = [
        { ...mockEvent, nodeId: "node-1" },
        { ...mockEvent, nodeId: "node-2" },
      ];
      
      await publisher.publishEvents(channelName, events);
      
      // Should queue both events
      expect(mockSupabase.channel).toHaveBeenCalledWith(channelName, expect.any(Object));
    });
  });

  describe("Batch Publishing", () => {
    it("should batch publish events at intervals", async () => {
      const channelName = "workflow_progress_123";
      
      // Queue some events
      await publisher.publishEvent(channelName, { ...mockEvent, nodeId: "node-1" });
      await publisher.publishEvent(channelName, { ...mockEvent, nodeId: "node-2" });
      
      // Advance timers to trigger batch publishing
      vi.advanceTimersByTime(500);
      
      expect(mockChannel.send).toHaveBeenCalledWith({
        type: "broadcast",
        event: "workflow_events",
        payload: {
          events: expect.arrayContaining([
            expect.objectContaining({ nodeId: "node-1" }),
            expect.objectContaining({ nodeId: "node-2" }),
          ]),
          timestamp: expect.any(String),
        },
      });
    });

    it("should handle publishing errors gracefully", async () => {
      const channelName = "workflow_progress_123";
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      
      mockChannel.send.mockRejectedValueOnce(new Error("Network error"));
      
      await publisher.publishEvent(channelName, mockEvent);
      
      // Trigger batch publishing
      vi.advanceTimersByTime(500);
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        `Error publishing events to channel ${channelName}:`,
        expect.any(Error)
      );
      
      consoleErrorSpy.mockRestore();
    });

    it("should re-queue failed events", async () => {
      const channelName = "workflow_progress_123";
      
      mockChannel.send.mockRejectedValueOnce(new Error("Network error"));
      
      await publisher.publishEvent(channelName, mockEvent);
      
      // Trigger batch publishing (will fail)
      vi.advanceTimersByTime(500);
      
      // Add another event
      await publisher.publishEvent(channelName, { ...mockEvent, nodeId: "node-2" });
      
      // Reset mock and trigger another batch
      mockChannel.send.mockResolvedValueOnce({ status: "ok" });
      vi.advanceTimersByTime(500);
      
      // Should include both the failed event and the new event
      expect(mockChannel.send).toHaveBeenLastCalledWith({
        type: "broadcast",
        event: "workflow_events",
        payload: {
          events: expect.arrayContaining([
            expect.objectContaining({ nodeId: "test-node-1" }),
            expect.objectContaining({ nodeId: "node-2" }),
          ]),
          timestamp: expect.any(String),
        },
      });
    });
  });

  describe("Cleanup", () => {
    it("should cleanup channels and intervals", () => {
      const channelName = "test-channel";
      publisher.subscribeToChannel(channelName);
      
      publisher.cleanup();
      
      expect(mockChannel.unsubscribe).toHaveBeenCalled();
    });

    it("should handle cleanup when no channels exist", () => {
      expect(() => publisher.cleanup()).not.toThrow();
    });
  });

  describe("Edge Cases", () => {
    it("should handle empty event queues", async () => {
      const channelName = "empty-channel";
      publisher.subscribeToChannel(channelName);
      
      // Trigger batch publishing with no events
      vi.advanceTimersByTime(500);
      
      expect(mockChannel.send).not.toHaveBeenCalled();
    });

    it("should handle malformed events gracefully", async () => {
      const channelName = "workflow_progress_123";
      const malformedEvent = {} as ExecutionEvent;
      
      await expect(
        publisher.publishEvent(channelName, malformedEvent)
      ).resolves.not.toThrow();
    });

    it("should handle channel creation failures", () => {
      mockSupabase.channel.mockImplementationOnce(() => {
        throw new Error("Channel creation failed");
      });
      
      expect(() => publisher.subscribeToChannel("failing-channel")).toThrow();
    });
  });
});
