export interface JobMatch {
  id: string;
  title: string;
  company: string;
  location: string; // Keep for backward compatibility
  location_name?: string; // New normalized field
  location_type?: string; // New normalized field
  location_city?: string; // New normalized field
  location_state?: string; // New normalized field
  location_is_remote?: boolean; // New normalized field
  match: number;
  department: string; // Keep for backward compatibility
  department_name?: string; // New normalized field
  salary: string;
  status: string;
  postedDate: string;
  requirements: string[]; // Keep for backward compatibility
  benefits: string[]; // Keep for backward compatibility
  normalized_requirements?: Array<{id: string; name: string; category?: string}>; // New normalized field
  normalized_benefits?: Array<{id: string; name: string; category?: string}>; // New normalized field
  description?: string; // Added optional description property
}

export interface RelationshipFactor {
  name: string;
  score: number;
}

export interface RelationshipAnalysis {
  score: number;
  factors: RelationshipFactor[];
  trend: "increasing" | "stable" | "decreasing";
}

export interface ScreeningQuestion {
  id: string;
  question: string;
  response?: string;
  category: "technical" | "behavioral" | "cultural" | "experience";
}

export interface Requirements {
  workAuthorization: "verified" | "pending" | "not_required";
  backgroundCheck: "verified" | "pending" | "not_required";
  drugScreening: "verified" | "pending" | "not_required";
  references: "verified" | "pending" | "not_required";
}

export interface Evaluation {
  category: string;
  score: number;
  notes: string;
}

// Enhanced screening interfaces for comprehensive functionality
export interface InterviewStage {
  id: string;
  name: string;
  status: "not_started" | "scheduled" | "completed" | "cancelled";
  scheduledDate?: string;
  completedDate?: string;
  interviewer?: string;
  notes?: string;
  score?: number; // 1-5 rating
  feedback?: string;
}

export interface ScreeningDecision {
  status: "pending" | "approved" | "rejected" | "on_hold";
  reason?: string;
  notes?: string;
  decidedBy?: string;
  decidedAt?: string;
  nextSteps?: string;
}

export interface AssessmentScore {
  id: string;
  name: string;
  score: number;
  maxScore: number;
  notes?: string;
  assessedBy?: string;
  assessedAt?: string;
}

export interface ScreeningNote {
  id: string;
  content: string;
  author: string;
  createdAt: string;
  type: "general" | "interview" | "assessment" | "decision";
}

// Enhanced screening interfaces for comprehensive candidate evaluation
export interface CompensationDetails {
  currentSalary?: number;
  expectedSalaryMin?: number;
  expectedSalaryMax?: number;
  bonusPercentage?: number;
  bonusAchieved?: boolean;
  benefits?: string[]; // ["equity", "rsp", "extended_health", "dental"]
  vacationWeeks?: number;
  contractPreference?: "contract" | "permanent" | "either";
  hourlyRateMin?: number;
  currentWorkStatus?: string;
}

export interface LocationPreferences {
  workArrangement?: "remote" | "hybrid" | "in_office" | "flexible";
  relocateWilling?: boolean;
  relocateScope?: "local" | "national" | "international";
  travelPercentage?: number; // 0-100
  workEligibility?: string; // "Canada", "US", "Both", etc.
  transportationPreference?: "public_transport" | "car" | "walking" | "cycling";
  commuteMaxMinutes?: number;
  interviewAvailability?: string;
}

export interface CommunicationAssessment {
  overallLevel?: number; // 1-5 scale
  grammar?: number; // 1-5
  articulation?: number; // 1-5
  attitude?: number; // 1-5
  energy?: number; // 1-5
  languages?: string[]; // ["English", "French", "Spanish"]
  languageProficiency?: { [language: string]: "basic" | "intermediate" | "advanced" | "native" };
}

export interface WorkExperienceDetails {
  totalYears?: number;
  teamSizeManaged?: number;
  directReports?: number;
  reasonForLeaving?: string;
  motivationsForChange?: string;
  industryPreferences?: string[]; // ["Financial", "Technology", "Healthcare"]
  companySizePreference?: "startup" | "small" | "medium" | "large" | "enterprise";
  jobTitleAspiration?: string;
  dayToDayPreferences?: string;
  targetDesires?: string;
}

export interface EducationDetails {
  highestLevel?: "high_school" | "college" | "bachelors" | "masters" | "phd" | "other";
  degree?: string;
  specialization?: string;
  graduationDate?: string;
  institution?: string;
  isPartTime?: boolean;
}

// AI-Powered Candidate Ranking System
export interface CandidateRanking {
  overallScore: number; // 0-100
  criteriaScores: {
    technicalFit: number; // 0-100
    experienceMatch: number; // 0-100
    culturalFit: number; // 0-100
    compensationAlignment: number; // 0-100
    locationCompatibility: number; // 0-100
  };
  aiExplanation: string;
  confidenceLevel: number; // 0-100
  lastUpdated: string;
  rankingFactors: {
    strengths: string[];
    concerns: string[];
    recommendations: string[];
  };
}

// Collaborative Evaluation System
export interface ReviewerAssignment {
  id: string;
  userId: string;
  name: string;
  role: string;
  email: string;
  status: "pending" | "in_progress" | "completed" | "declined";
  assignedAt: string;
  completedAt?: string;
  evaluation?: Evaluation[];
  overallScore?: number;
  recommendation?: "strong_hire" | "hire" | "maybe" | "no_hire" | "strong_no_hire";
  notes?: string;
  weight?: number; // Reviewer importance weight
}

// Skills Assessment Integration
export interface SkillsAssessment {
  id: string;
  type: "technical" | "behavioral" | "cognitive" | "coding_challenge";
  title: string;
  provider?: string;
  status: "not_started" | "in_progress" | "completed" | "expired";
  score?: number;
  maxScore?: number;
  percentile?: number;
  completedAt?: string;
  timeSpent?: number; // minutes
  results?: {
    skillBreakdown?: { [skill: string]: number };
    strengths?: string[];
    weaknesses?: string[];
    detailedResults?: any;
  };
  assessmentUrl?: string;
}

export interface CandidateType {
  id: string;
  name: string;
  role: string;
  email: string;
  phone: string;
  // Database normalized fields
  location_id?: string; // Foreign key to locations table
  location_name?: string; // Denormalized from locations table
  location_type?: string; // Denormalized from locations table
  location_city?: string; // Denormalized from locations table
  location_state?: string; // Denormalized from locations table
  location_is_remote?: boolean; // Denormalized from locations table
  // Backward compatibility
  location?: string; // Keep for backward compatibility
  avatar: string;
  recruiter: {
    id: string;
    name: string;
    avatar: string | null;
  };
  // Database normalized fields
  recruiter_id?: string; // Foreign key to profiles table
  recruiter_name?: string; // Denormalized from profiles table
  recruiter_avatar?: string | null; // Denormalized from profiles table
  tags: string[];
  normalized_tags?: {
    id: string;
    name: string;
    color?: string;
  }[];
  socialLinks: {
    github?: string;
    linkedin?: string;
    twitter?: string;
  };
  relationshipScore: number;
  relationshipAnalysis?: RelationshipAnalysis;
  aiSummary?: string;
  matchedJobs?: JobMatch[];
  experience: string;
  industry: string;
  remotePreference: string;
  visaStatus: string;
  skills: {
    name: string;
    level: string;
    years: number;
    category?: string;
  }[];
  normalized_skills?: {
    name: string;
    level: string;
    years: number;
    category?: string;
  }[];
  createdAt: string;
  updatedAt: string;
  screening?: {
    // Basic screening info
    notes?: string;
    dayToDay?: string;
    skills?: string[];
    location?: string;
    education?: string[];

    // Core screening components
    questions?: ScreeningQuestion[];
    status: "pending" | "in_progress" | "completed";
    lastUpdated: string;
    requirements: Requirements;
    evaluations: Evaluation[];

    // Enhanced screening features
    interviewStages?: InterviewStage[];
    decision?: ScreeningDecision;
    assessmentScores?: AssessmentScore[];
    screeningNotes?: ScreeningNote[];

    // Comprehensive candidate evaluation (NEW)
    compensation?: CompensationDetails;
    locationPreferences?: LocationPreferences;
    communicationAssessment?: CommunicationAssessment;
    workExperience?: WorkExperienceDetails;
    educationDetails?: EducationDetails;

    // Advanced screening features (LATEST)
    candidateRanking?: CandidateRanking;
    reviewerAssignments?: ReviewerAssignment[];
    skillsAssessments?: SkillsAssessment[];

    // Metadata
    createdBy?: string;
    createdAt?: string;
    updatedBy?: string;
  };
  activity?: Array<{
    type: string;
    description: string;
    date: string;
  }>;
  skill_names?: string[];
}
