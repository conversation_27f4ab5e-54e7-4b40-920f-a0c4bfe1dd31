/**
 * Executor Registration
 * Registers all node executors with the executor registry
 */

import { executorRegistry } from "../ExecutorRegistry";

// Import executors
import { SendEmailExecutor } from "./send-email";
import { AIScreenExecutor } from "./ai-screen";
import { SkillsMatchExecutor } from "./skills-match";
import { ScheduleInterviewExecutor } from "./schedule-interview";
import { UpdateStatusExecutor } from "./update-status";
import { AddToPoolExecutor } from "./add-to-pool";
import { SendMessageExecutor } from "./send-message";
import { NotifyTeamExecutor } from "./notify-team";
import { NewApplicationTriggerExecutor } from "./new-application";
import { ApplicationStatusTriggerExecutor } from "./application-status";
import { ScheduledTriggerExecutor } from "./scheduled-trigger";
import { SendAssessmentExecutor } from "./send-assessment";
import { ExperienceCheckExecutor } from "./experience-check";
import { EducationCheckExecutor } from "./education-check";
import { LocationCheckExecutor } from "./location-check";
import { DelayExecutor } from "./delay";
import { DataTransformExecutor } from "./data-transform";
import { DataFilterExecutor } from "./data-filter";
import { AsyncApiCallExecutor } from "./async-api-call";
import { ParallelAggregatorExecutor } from "./parallel-aggregator";
import { HumanApprovalExecutor } from "./human-approval";

/**
 * Register all executors with the registry
 */
export function registerAllExecutors(): void {
  // Actions
  executorRegistry.register(new SendEmailExecutor());
  executorRegistry.register(new AIScreenExecutor());
  executorRegistry.register(new ScheduleInterviewExecutor());
  executorRegistry.register(new UpdateStatusExecutor());
  executorRegistry.register(new NotifyTeamExecutor());
  executorRegistry.register(new SendAssessmentExecutor());

  // Conditions
  executorRegistry.register(new SkillsMatchExecutor());
  executorRegistry.register(new ExperienceCheckExecutor());
  executorRegistry.register(new EducationCheckExecutor());
  executorRegistry.register(new LocationCheckExecutor());

  // Outputs
  executorRegistry.register(new AddToPoolExecutor());
  executorRegistry.register(new SendMessageExecutor());

  // Triggers
  executorRegistry.register(new NewApplicationTriggerExecutor());
  executorRegistry.register(new ApplicationStatusTriggerExecutor());
  executorRegistry.register(new ScheduledTriggerExecutor());

  // Transformations
  executorRegistry.register(new DelayExecutor());
  executorRegistry.register(new DataTransformExecutor());
  executorRegistry.register(new DataFilterExecutor());
  executorRegistry.register(new ParallelAggregatorExecutor());

  // Advanced Actions
  executorRegistry.register(new AsyncApiCallExecutor());

  // Human-in-the-Loop Controls
  executorRegistry.register(new HumanApprovalExecutor());

  console.log(`Registered ${executorRegistry.getCount()} executors`);
}

// Auto-register on import
registerAllExecutors();
