import { NodeExecutor, ExecutionContext, NodeExecutorConfig, NodeExecutorResult } from "../types";
import { supabase } from "@/integrations/supabase/client";
import { workflowLogger, LogLevel } from "@/services/WorkflowLogger";
import { AdvancedAuditTrail } from "@/services/audit/AdvancedAuditTrail";

export interface HumanApprovalConfig {
  approvalType: 'single' | 'multi' | 'consensus';
  approvers: string[]; // User IDs or email addresses
  requiredApprovals?: number; // For multi-approval
  timeoutHours?: number; // Auto-reject after timeout
  escalationHours?: number; // Escalate if no response
  escalationApprovers?: string[]; // Escalation approvers
  approvalMessage: string;
  contextData?: Record<string, any>; // Data to show approvers
  allowComments: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  autoApproveConditions?: {
    enabled: boolean;
    conditions: Array<{
      field: string;
      operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
      value: any;
    }>;
  };
}

export interface ApprovalRequest {
  id: string;
  workflowId: string;
  executionId: string;
  nodeId: string;
  requesterId: string;
  approvers: string[];
  approvalType: string;
  status: 'pending' | 'approved' | 'rejected' | 'escalated' | 'timeout';
  approvalMessage: string;
  contextData: Record<string, any>;
  priority: string;
  timeoutAt?: Date;
  escalationAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  responses: Array<{
    approverId: string;
    decision: 'approve' | 'reject';
    comment?: string;
    timestamp: Date;
  }>;
}

export class HumanApprovalExecutor implements NodeExecutor {
  id = "human-approval";
  name = "Human Approval Gate";
  description = "Requires human approval before proceeding with workflow execution";
  category = "action" as const;

  private auditTrail = AdvancedAuditTrail.getInstance();

  async execute(
    nodeData: Record<string, unknown>,
    context: ExecutionContext,
    _config?: NodeExecutorConfig
  ): Promise<NodeExecutorResult> {
    const startTime = Date.now();
    const approvalConfig = nodeData.config as HumanApprovalConfig;

    try {
      await workflowLogger.log({
        level: LogLevel.INFO,
        message: "Starting human approval process",
        workflowId: context.workflowId as string,
        executionId: context.executionId as string,
        nodeId: context.nodeId as string,
        metadata: {
          approvalType: approvalConfig.approvalType,
          approversCount: approvalConfig.approvers.length,
          priority: approvalConfig.priority,
        },
      });

      // Check auto-approval conditions first
      if (approvalConfig.autoApproveConditions?.enabled) {
        const autoApproved = await this.checkAutoApprovalConditions(
          approvalConfig.autoApproveConditions.conditions,
          context
        );

        if (autoApproved) {
          await workflowLogger.log({
            level: LogLevel.INFO,
            message: "Auto-approval conditions met, bypassing human approval",
            workflowId: context.workflowId as string,
            executionId: context.executionId as string,
            nodeId: context.nodeId as string,
          });

          return {
            success: true,
            data: {
              approved: true,
              autoApproved: true,
              approvalTime: Date.now() - startTime,
              approvers: [],
              comments: ["Auto-approved based on predefined conditions"],
            },
          };
        }
      }

      // Create approval request
      const approvalRequest = await this.createApprovalRequest(approvalConfig, context);

      // Log approval request creation
      await this.auditTrail.logEvent({
        eventType: 'approval',
        entityType: 'approval',
        entityId: approvalRequest.id,
        userId: context.userId as string,
        workflowId: context.workflowId as string,
        executionId: context.executionId as string,
        nodeId: context.nodeId as string,
        approvalChain: {
          requestId: approvalRequest.id,
          approverLevel: 1,
          decision: 'escalate', // Initial state
          timeToDecision: 0,
        },
        context: {
          approvalType: approvalConfig.approvalType,
          approversCount: approvalConfig.approvers.length,
          priority: approvalConfig.priority,
        },
        metadata: {
          timestamp: new Date(),
          success: true,
        },
        compliance: {
          sensitivityLevel: 'high',
          gdprRelevant: true,
          retentionPeriod: 2555, // 7 years
        },
      });

      // Send notifications to approvers
      await this.notifyApprovers(approvalRequest, approvalConfig);

      // Wait for approval with timeout
      const result = await this.waitForApproval(approvalRequest, approvalConfig);
      
      await workflowLogger.log({
        level: LogLevel.INFO,
        message: `Human approval ${result.approved ? 'granted' : 'denied'}`,
        workflowId: context.workflowId as string,
        executionId: context.executionId as string,
        nodeId: context.nodeId as string,
        metadata: {
          approvalTime: result.approvalTime,
          approversCount: result.approvers.length,
          autoApproved: result.autoApproved || false,
        },
      });

      return {
        success: result.approved,
        data: result,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Human approval process failed",
        workflowId: context.workflowId as string,
        executionId: context.executionId as string,
        nodeId: context.nodeId as string,
        error: error as Error,
      });
      return {
        success: false,
        error: error as Error,
        duration: Date.now() - startTime,
      };
    }
  }

  private async checkAutoApprovalConditions(
    conditions: Array<{
      field: string;
      operator: string;
      value: any;
    }>,
    context: Record<string, any>
  ): Promise<boolean> {
    for (const condition of conditions) {
      const fieldValue = this.getNestedValue(context, condition.field);
      
      switch (condition.operator) {
        case 'equals':
          if (fieldValue !== condition.value) return false;
          break;
        case 'greater_than':
          if (Number(fieldValue) <= Number(condition.value)) return false;
          break;
        case 'less_than':
          if (Number(fieldValue) >= Number(condition.value)) return false;
          break;
        case 'contains':
          if (!String(fieldValue).includes(String(condition.value))) return false;
          break;
        default:
          return false;
      }
    }
    return true;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async createApprovalRequest(
    config: HumanApprovalConfig,
    context: Record<string, any>
  ): Promise<ApprovalRequest> {
    const now = new Date();
    const timeoutAt = config.timeoutHours 
      ? new Date(now.getTime() + config.timeoutHours * 60 * 60 * 1000)
      : undefined;
    const escalationAt = config.escalationHours
      ? new Date(now.getTime() + config.escalationHours * 60 * 60 * 1000)
      : undefined;

    const approvalRequest: ApprovalRequest = {
      id: crypto.randomUUID(),
      workflowId: context.workflowId,
      executionId: context.executionId,
      nodeId: context.nodeId,
      requesterId: context.userId,
      approvers: config.approvers,
      approvalType: config.approvalType,
      status: 'pending',
      approvalMessage: config.approvalMessage,
      contextData: config.contextData || {},
      priority: config.priority,
      timeoutAt,
      escalationAt,
      createdAt: now,
      updatedAt: now,
      responses: [],
    };

    // Store in database
    const { error } = await supabase
      .from('workflow_approvals')
      .insert({
        id: approvalRequest.id,
        workflow_id: approvalRequest.workflowId,
        execution_id: approvalRequest.executionId,
        node_id: approvalRequest.nodeId,
        requester_id: approvalRequest.requesterId,
        approvers: approvalRequest.approvers,
        approval_type: approvalRequest.approvalType,
        status: approvalRequest.status,
        approval_message: approvalRequest.approvalMessage,
        context_data: approvalRequest.contextData,
        priority: approvalRequest.priority,
        timeout_at: approvalRequest.timeoutAt?.toISOString(),
        escalation_at: approvalRequest.escalationAt?.toISOString(),
        responses: approvalRequest.responses,
      });

    if (error) {
      throw new Error(`Failed to create approval request: ${error.message}`);
    }

    return approvalRequest;
  }

  private async notifyApprovers(
    approvalRequest: ApprovalRequest,
    config: HumanApprovalConfig
  ): Promise<void> {
    // Send notifications via multiple channels
    for (const approverId of config.approvers) {
      try {
        // Create notification record
        await supabase.from('notifications').insert({
          user_id: approverId,
          type: 'workflow_approval',
          title: `Approval Required: ${config.approvalMessage}`,
          message: `A workflow requires your approval. Priority: ${config.priority}`,
          data: {
            approvalId: approvalRequest.id,
            workflowId: approvalRequest.workflowId,
            priority: config.priority,
            contextData: config.contextData,
          },
          is_read: false,
        });

        // TODO: Send email notification
        // TODO: Send Slack notification if configured
        
      } catch (error) {
        await workflowLogger.log({
          level: LogLevel.WARN,
          message: `Failed to notify approver ${approverId}`,
          workflowId: approvalRequest.workflowId,
          executionId: approvalRequest.executionId,
          nodeId: approvalRequest.nodeId,
          error: error as Error,
        });
      }
    }
  }

  private async waitForApproval(
    approvalRequest: ApprovalRequest,
    config: HumanApprovalConfig
  ): Promise<any> {
    const startTime = Date.now();
    const pollInterval = 5000; // 5 seconds
    const maxWaitTime = (config.timeoutHours || 24) * 60 * 60 * 1000; // Default 24 hours

    while (Date.now() - startTime < maxWaitTime) {
      // Check current status
      const { data: currentRequest } = await supabase
        .from('workflow_approvals')
        .select('*')
        .eq('id', approvalRequest.id)
        .single();

      if (!currentRequest) {
        throw new Error('Approval request not found');
      }

      // Check if approval is complete
      const result = this.evaluateApprovalStatus(currentRequest, config);
      if (result.complete) {
        return {
          approved: result.approved,
          approvalTime: Date.now() - startTime,
          approvers: result.approvers,
          comments: result.comments,
          autoApproved: false,
        };
      }

      // Check for escalation
      if (currentRequest.escalation_at && new Date() > new Date(currentRequest.escalation_at)) {
        await this.handleEscalation(currentRequest, config);
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    // Timeout reached
    await this.handleTimeout(approvalRequest);
    return {
      approved: false,
      approvalTime: Date.now() - startTime,
      approvers: [],
      comments: ['Approval request timed out'],
      timedOut: true,
    };
  }

  private evaluateApprovalStatus(request: any, config: HumanApprovalConfig): {
    complete: boolean;
    approved: boolean;
    approvers: string[];
    comments: string[];
  } {
    const responses = request.responses || [];
    const approvals = responses.filter((r: any) => r.decision === 'approve');
    const rejections = responses.filter((r: any) => r.decision === 'reject');

    switch (config.approvalType) {
      case 'single':
        if (approvals.length > 0) {
          return {
            complete: true,
            approved: true,
            approvers: approvals.map((r: any) => r.approverId),
            comments: approvals.map((r: any) => r.comment || ''),
          };
        }
        if (rejections.length > 0) {
          return {
            complete: true,
            approved: false,
            approvers: rejections.map((r: any) => r.approverId),
            comments: rejections.map((r: any) => r.comment || ''),
          };
        }
        break;

      case 'multi':
        const required = config.requiredApprovals || config.approvers.length;
        if (approvals.length >= required) {
          return {
            complete: true,
            approved: true,
            approvers: approvals.map((r: any) => r.approverId),
            comments: approvals.map((r: any) => r.comment || ''),
          };
        }
        if (rejections.length > config.approvers.length - required) {
          return {
            complete: true,
            approved: false,
            approvers: rejections.map((r: any) => r.approverId),
            comments: rejections.map((r: any) => r.comment || ''),
          };
        }
        break;

      case 'consensus':
        if (responses.length === config.approvers.length) {
          const approved = rejections.length === 0;
          return {
            complete: true,
            approved,
            approvers: responses.map((r: any) => r.approverId),
            comments: responses.map((r: any) => r.comment || ''),
          };
        }
        break;
    }

    return { complete: false, approved: false, approvers: [], comments: [] };
  }

  private async handleEscalation(request: any, config: HumanApprovalConfig): Promise<void> {
    if (!config.escalationApprovers?.length) return;

    await supabase
      .from('workflow_approvals')
      .update({
        status: 'escalated',
        approvers: [...request.approvers, ...config.escalationApprovers],
        updated_at: new Date().toISOString(),
      })
      .eq('id', request.id);

    // Notify escalation approvers
    for (const approverId of config.escalationApprovers) {
      await supabase.from('notifications').insert({
        user_id: approverId,
        type: 'workflow_approval_escalation',
        title: `Escalated Approval Required: ${config.approvalMessage}`,
        message: `A workflow approval has been escalated to you. Priority: ${config.priority}`,
        data: {
          approvalId: request.id,
          workflowId: request.workflow_id,
          priority: config.priority,
          escalated: true,
        },
        is_read: false,
      });
    }
  }

  private async handleTimeout(request: ApprovalRequest): Promise<void> {
    await supabase
      .from('workflow_approvals')
      .update({
        status: 'timeout',
        updated_at: new Date().toISOString(),
      })
      .eq('id', request.id);
  }

  static get configSchema() {
    return {
      type: "object",
      title: "Human Approval Gate",
      description: "Requires human approval before proceeding",
      properties: {
        approvalType: {
          type: "string",
          title: "Approval Type",
          enum: ["single", "multi", "consensus"],
          default: "single",
          description: "Single: any one approver, Multi: specified number, Consensus: all must approve",
        },
        approvers: {
          type: "array",
          title: "Approvers",
          items: { type: "string" },
          description: "List of user IDs or email addresses who can approve",
          minItems: 1,
        },
        requiredApprovals: {
          type: "number",
          title: "Required Approvals",
          description: "Number of approvals needed (for multi-approval type)",
          minimum: 1,
        },
        approvalMessage: {
          type: "string",
          title: "Approval Message",
          description: "Message shown to approvers",
          maxLength: 500,
        },
        priority: {
          type: "string",
          title: "Priority",
          enum: ["low", "medium", "high", "urgent"],
          default: "medium",
        },
        timeoutHours: {
          type: "number",
          title: "Timeout (Hours)",
          description: "Auto-reject after this many hours",
          minimum: 1,
          maximum: 168, // 1 week
          default: 24,
        },
        escalationHours: {
          type: "number",
          title: "Escalation (Hours)",
          description: "Escalate if no response after this many hours",
          minimum: 1,
          maximum: 72,
        },
        allowComments: {
          type: "boolean",
          title: "Allow Comments",
          description: "Allow approvers to add comments",
          default: true,
        },
      },
      required: ["approvers", "approvalMessage"],
    };
  }
}
