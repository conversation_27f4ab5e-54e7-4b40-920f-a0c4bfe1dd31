/**
 * AI Screen Executor
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";
import { supabase } from "@/integrations/supabase/client";
import { screenCandidate } from "@/utils/gemini";
import { CandidatesService, ActivityService } from "@/services";
import { ScreeningAgent, ScreeningContext } from "@/services/agents/ScreeningAgent";

export class AIScreenExecutor extends BaseExecutor {
  id = "ai-screen";
  name = "AI Screen";
  description = "Screen candidate using AI";
  category = "action" as const;

  configSchema = {
    criteria: {
      type: "string",
      label: "Screening Criteria",
      required: true,
      options: [
        "comprehensive",
        "technical",
        "cultural_fit",
        "document_verification",
      ],
    },
    minScore: {
      type: "number",
      label: "Minimum Score (%)",
      required: true,
      default: 75,
      min: 0,
      max: 100,
    },
    useAdvancedAI: {
      type: "boolean",
      label: "Use Advanced AI Analysis",
      required: false,
      default: true,
      description: "Enable advanced AI analysis with detailed scoring, confidence levels, and interview questions",
    },
    includeInterviewQuestions: {
      type: "boolean",
      label: "Generate Interview Questions",
      required: false,
      default: true,
      description: "Generate targeted interview questions based on candidate analysis",
    },
    includeRiskAssessment: {
      type: "boolean",
      label: "Include Risk Assessment",
      required: false,
      default: true,
      description: "Perform risk assessment and identify potential concerns",
    },
    confidenceThreshold: {
      type: "number",
      label: "Confidence Threshold (%)",
      required: false,
      default: 80,
      min: 0,
      max: 100,
      description: "Minimum confidence level required for AI recommendations",
    },
    llmProvider: {
      type: "string",
      label: "AI Provider",
      required: false,
      default: "groq",
      options: ["groq", "gemini", "auto"],
      description: "AI provider to use for analysis (auto = intelligent selection)",
    },
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId =
        (context.candidateId as string) || ((context.lastResult as any)?.candidateId as string);
      const jobId = (context.jobId as string) || ((context.lastResult as any)?.jobId as string);

      if (!candidateId) {
        throw new Error("No candidate ID found in context");
      }

      // Get candidate details
      const candidate = await CandidatesService.getCandidate(candidateId);

      if (!candidate) {
        throw new Error("Candidate not found");
      }

      // Get job details if available
      let jobDescription = "";
      if (jobId) {
        const { data: job, error: jobError } = await supabase
          .from("jobs")
          .select("*")
          .eq("id", jobId)
          .single();

        if (!jobError && job) {
          jobDescription = job.description;
        }
      }

      // Get current user for context
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      let screeningResult: any;
      let isAdvancedAnalysis = false;

      // Use advanced AI analysis if enabled
      if (config.useAdvancedAI !== false) {
        try {
          // Create screening context
          const screeningContext: ScreeningContext = {
            userId: user.id,
            jobId,
            workflowId: typeof context.workflowId === 'string' ? context.workflowId : undefined,
            priority: 'high',
            includeInterviewQuestions: config.includeInterviewQuestions !== false,
            includeRiskAssessment: config.includeRiskAssessment !== false,
          };

          // Create screening criteria
          const screeningCriteria = {
            jobTitle: candidate.role,
            requiredSkills: jobDescription ? [jobDescription] : [],
            experienceLevel: 'mid' as const,
            department: 'general',
            customCriteria: {
              criteria: config.criteria,
              minScore: config.minScore,
              customInstructions: config.customInstructions,
            },
          };

          // Use ScreeningAgent for advanced analysis
          const agent = new ScreeningAgent(user.id, screeningContext);
          const advancedResult = await agent.screenCandidate(candidate, screeningCriteria);

          screeningResult = {
            score: advancedResult.overallScore,
            strengths: advancedResult.analysis.strengths,
            gaps: advancedResult.analysis.concerns,
            summary: advancedResult.reasoning,
            confidence: advancedResult.confidenceLevel,
            interviewQuestions: advancedResult.interviewQuestions,
            riskAssessment: { risks: advancedResult.riskFactors.map(r => ({ description: r })) },
            criteriaScores: [
              { criterion: 'Technical Fit', score: advancedResult.criteriaScores.technicalFit, reasoning: 'Technical skills assessment' },
              { criterion: 'Experience Match', score: advancedResult.criteriaScores.experienceMatch, reasoning: 'Experience level evaluation' },
              { criterion: 'Cultural Fit', score: advancedResult.criteriaScores.culturalFit, reasoning: 'Cultural alignment assessment' },
              { criterion: 'Skills Match', score: advancedResult.criteriaScores.skillsMatch, reasoning: 'Skills compatibility evaluation' },
            ],
          };
          isAdvancedAnalysis = true;
        } catch (advancedError) {
          console.warn("Advanced AI analysis failed, falling back to basic screening:", advancedError);
          // Fall back to basic screening
          screeningResult = await screenCandidate(
            candidate,
            jobDescription || `${candidate.role} position`,
          );
        }
      } else {
        // Use basic screening
        screeningResult = await screenCandidate(
          candidate,
          jobDescription || `${candidate.role} position`,
        );
      }

      // Create activity entry
      await ActivityService.createActivityEntry({
        candidate_id: candidateId,
        user_id: user.id,
        activity_type: "ai_screening",
        title: isAdvancedAnalysis ? "Advanced AI Screening Completed" : "AI Screening Completed",
        description: `${isAdvancedAnalysis ? 'Advanced ' : ''}AI screening completed with score: ${screeningResult.score}%. Criteria: ${config.criteria || "comprehensive"}`,
        metadata: {
          score: screeningResult.score,
          criteria: config.criteria,
          min_score: config.minScore,
          passed: screeningResult.score >= parseInt(config.minScore || "75", 10),
          status: "completed",
          advanced_analysis: isAdvancedAnalysis,
          confidence: screeningResult.confidence,
          provider: config.llmProvider || 'auto',
        },
      });

      const passed = screeningResult.score >= parseInt(config.minScore || "75", 10);

      return {
        success: true,
        data: {
          score: screeningResult.score,
          passed,
          candidateId,
          candidateName: candidate.name,
          criteria: config.criteria,
          minScore: config.minScore,
          jobId,
          timestamp: new Date().toISOString(),
          // Enhanced data for advanced analysis
          ...(isAdvancedAnalysis && {
            confidence: screeningResult.confidence,
            strengths: screeningResult.strengths,
            gaps: screeningResult.gaps,
            summary: screeningResult.summary,
            interviewQuestions: screeningResult.interviewQuestions,
            riskAssessment: screeningResult.riskAssessment,
            criteriaScores: screeningResult.criteriaScores,
            advancedAnalysis: true,
          }),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!((context.candidateId as string) || ((context.lastResult as any)?.candidateId as string));
  }
}
