import React from 'react';
import { cn } from '@/lib/utils';

export interface KPIGroupProps {
  children: React.ReactNode;
  columns?: 2 | 3 | 4 | 5;
  className?: string;
}

export function KPIGroup({ 
  children, 
  columns = 4,
  className 
}: KPIGroupProps) {
  const columnClasses = {
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
  };

  return (
    <div className={cn(
      'grid gap-4',
      columnClasses[columns],
      className
    )}>
      {children}
    </div>
  );
}
