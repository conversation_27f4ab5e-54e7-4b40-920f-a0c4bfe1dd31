import React from 'react';
import { cn } from '@/lib/utils';

export interface PanelProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  elevation?: 0 | 1 | 2 | 3 | 4;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  headerClassName?: string;
  bodyClassName?: string;
}

export function Panel({
  title,
  description,
  actions,
  elevation = 1,
  padding = 'md',
  children,
  className,
  headerClassName,
  bodyClassName,
}: PanelProps) {
  const elevationClasses = {
    0: 'bg-card border',
    1: 'bg-surface-1 border shadow-sm',
    2: 'bg-surface-2 border shadow-md',
    3: 'bg-surface-3 border shadow-lg',
    4: 'bg-surface-4 border shadow-xl',
  };

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  const hasHeader = title || description || actions;

  return (
    <div
      className={cn(
        'rounded-xl overflow-hidden',
        'transition-all duration-200',
        elevationClasses[elevation],
        !hasHeader && paddingClasses[padding],
        className
      )}
    >
      {hasHeader && (
        <div
          className={cn(
            'border-b bg-background/50',
            padding !== 'none' && 'px-6 py-4',
            headerClassName
          )}
        >
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              {title && (
                <h3 className="text-lg font-semibold tracking-tight">
                  {title}
                </h3>
              )}
              {description && (
                <p className="text-sm text-muted-foreground">
                  {description}
                </p>
              )}
            </div>
            {actions && (
              <div className="flex items-center gap-2">
                {actions}
              </div>
            )}
          </div>
        </div>
      )}

      <div
        className={cn(
          hasHeader && paddingClasses[padding],
          bodyClassName
        )}
      >
        {children}
      </div>
    </div>
  );
}

// Panel Header component for custom headers
export interface PanelHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export function PanelHeader({ children, className }: PanelHeaderProps) {
  return (
    <div className={cn('border-b bg-background/50 px-6 py-4', className)}>
      {children}
    </div>
  );
}

// Panel Body component for custom body
export interface PanelBodyProps {
  children: React.ReactNode;
  className?: string;
  noPadding?: boolean;
}

export function PanelBody({ children, className, noPadding = false }: PanelBodyProps) {
  return (
    <div className={cn(!noPadding && 'p-6', className)}>
      {children}
    </div>
  );
}
