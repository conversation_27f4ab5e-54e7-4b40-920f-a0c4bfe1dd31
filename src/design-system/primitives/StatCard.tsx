import React from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

export interface StatCardProps {
  title: string;
  value: string | number;
  delta?: {
    value: number;
    trend: 'up' | 'down' | 'neutral';
  };
  icon?: LucideIcon;
  loading?: boolean;
  href?: string;
  className?: string;
  compact?: boolean;
}

export function StatCard({
  title,
  value,
  delta,
  icon: Icon,
  loading = false,
  href,
  className,
  compact = false,
}: StatCardProps) {
  const getTrendIcon = () => {
    if (!delta) return null;
    switch (delta.trend) {
      case 'up':
        return TrendingUp;
      case 'down':
        return TrendingDown;
      default:
        return Minus;
    }
  };

  const getTrendColor = () => {
    if (!delta) return '';
    switch (delta.trend) {
      case 'up':
        return 'text-success';
      case 'down':
        return 'text-error';
      default:
        return 'text-muted-foreground';
    }
  };

  const formatDelta = () => {
    if (!delta) return '';
    const sign = delta.value >= 0 ? '+' : '';
    return `${sign}${delta.value}%`;
  };

  const TrendIcon = getTrendIcon();

  const content = (
    <div
      className={cn(
        'relative overflow-hidden rounded-xl border bg-card',
        'transition-all duration-200',
        'hover:shadow-md hover:-translate-y-0.5',
        'group',
        compact ? 'p-4' : 'p-6',
        className
      )}
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      <div className="relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <p className={cn(
            'font-medium text-muted-foreground',
            compact ? 'text-xs' : 'text-sm'
          )}>
            {title}
          </p>
          {Icon && (
            <div className={cn(
              'rounded-lg bg-primary/10 text-primary',
              compact ? 'p-1.5' : 'p-2'
            )}>
              <Icon className={compact ? 'h-3.5 w-3.5' : 'h-4 w-4'} />
            </div>
          )}
        </div>

        {/* Value */}
        {loading ? (
          <div className="space-y-2">
            <Skeleton className={cn(
              'h-8',
              compact ? 'w-20' : 'w-24'
            )} />
            {delta && (
              <Skeleton className={cn(
                'h-4',
                compact ? 'w-16' : 'w-20'
              )} />
            )}
          </div>
        ) : (
          <>
            <div className={cn(
              'font-bold tracking-tight',
              compact ? 'text-xl' : 'text-2xl lg:text-3xl'
            )}>
              {value}
            </div>

            {/* Delta */}
            {delta && (
              <div className={cn(
                'flex items-center gap-1 mt-2',
                getTrendColor()
              )}>
                {TrendIcon && (
                  <TrendIcon className={compact ? 'h-3 w-3' : 'h-3.5 w-3.5'} />
                )}
                <span className={cn(
                  'font-medium',
                  compact ? 'text-xs' : 'text-sm'
                )}>
                  {formatDelta()}
                </span>
                <span className={cn(
                  'text-muted-foreground',
                  compact ? 'text-xs' : 'text-sm'
                )}>
                  from last period
                </span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );

  if (href) {
    return (
      <a
        href={href}
        className="block focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-xl"
      >
        {content}
      </a>
    );
  }

  return content;
}

// Skeleton loader for StatCard
export function StatCardSkeleton({ compact = false }: { compact?: boolean }) {
  return (
    <div className={cn(
      'rounded-xl border bg-card',
      compact ? 'p-4' : 'p-6'
    )}>
      <div className="flex items-center justify-between mb-3">
        <Skeleton className={cn(
          compact ? 'h-3 w-20' : 'h-4 w-24'
        )} />
        <Skeleton className={cn(
          'rounded-lg',
          compact ? 'h-7 w-7' : 'h-8 w-8'
        )} />
      </div>
      <Skeleton className={cn(
        compact ? 'h-6 w-16 mb-2' : 'h-8 w-20 mb-3'
      )} />
      <Skeleton className={cn(
        compact ? 'h-3 w-24' : 'h-4 w-32'
      )} />
    </div>
  );
}
