import React from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';

export interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'secondary' | 'outline' | 'ghost';
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
  compact?: boolean;
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  secondaryAction,
  className,
  compact = false,
}: EmptyStateProps) {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center text-center',
        compact ? 'py-8' : 'py-12',
        className
      )}
    >
      {Icon && (
        <div className={cn(
          'rounded-full bg-muted/50 text-muted-foreground mb-4',
          compact ? 'p-3' : 'p-4'
        )}>
          <Icon className={compact ? 'h-6 w-6' : 'h-8 w-8'} />
        </div>
      )}
      
      <h3 className={cn(
        'font-semibold',
        compact ? 'text-base' : 'text-lg'
      )}>
        {title}
      </h3>
      
      {description && (
        <p className={cn(
          'text-muted-foreground mt-2 max-w-sm',
          compact ? 'text-sm' : 'text-base'
        )}>
          {description}
        </p>
      )}
      
      {(action || secondaryAction) && (
        <div className={cn(
          'flex flex-col sm:flex-row gap-3',
          compact ? 'mt-4' : 'mt-6'
        )}>
          {action && (
            <Button
              onClick={action.onClick}
              variant={action.variant || 'default'}
              size={compact ? 'sm' : 'default'}
            >
              {action.label}
            </Button>
          )}
          {secondaryAction && (
            <Button
              onClick={secondaryAction.onClick}
              variant="outline"
              size={compact ? 'sm' : 'default'}
            >
              {secondaryAction.label}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
