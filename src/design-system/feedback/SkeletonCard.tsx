import React from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

export interface SkeletonCardProps {
  className?: string;
  showHeader?: boolean;
  showActions?: boolean;
  lines?: number;
}

export function SkeletonCard({
  className,
  showHeader = true,
  showActions = false,
  lines = 3,
}: SkeletonCardProps) {
  return (
    <div className={cn('rounded-xl border bg-card p-6', className)}>
      {showHeader && (
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32" />
            {showActions && <Skeleton className="h-8 w-20" />}
          </div>
        </div>
      )}
      
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, i) => (
          <Skeleton
            key={i}
            className={cn(
              'h-4',
              i === lines - 1 ? 'w-3/4' : 'w-full'
            )}
          />
        ))}
      </div>
    </div>
  );
}

export interface SkeletonRowProps {
  className?: string;
  columns?: number;
  showAvatar?: boolean;
  showActions?: boolean;
}

export function SkeletonRow({
  className,
  columns = 4,
  showAvatar = false,
  showActions = false,
}: SkeletonRowProps) {
  return (
    <div className={cn(
      'flex items-center gap-4 p-4 border-b',
      className
    )}>
      {showAvatar && (
        <Skeleton className="h-10 w-10 rounded-full flex-shrink-0" />
      )}
      
      <div className="flex-1 grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton
            key={i}
            className={cn(
              'h-4',
              i === 0 ? 'w-full' : 'w-3/4'
            )}
          />
        ))}
      </div>
      
      {showActions && (
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-8 rounded" />
          <Skeleton className="h-8 w-8 rounded" />
        </div>
      )}
    </div>
  );
}

export interface SkeletonKPIProps {
  className?: string;
  compact?: boolean;
}

export function SkeletonKPI({ className, compact = false }: SkeletonKPIProps) {
  return (
    <div className={cn(
      'rounded-xl border bg-card',
      compact ? 'p-4' : 'p-6',
      className
    )}>
      <div className="flex items-center justify-between mb-3">
        <Skeleton className={cn(
          compact ? 'h-3 w-20' : 'h-4 w-24'
        )} />
        <Skeleton className={cn(
          'rounded-lg',
          compact ? 'h-7 w-7' : 'h-8 w-8'
        )} />
      </div>
      <Skeleton className={cn(
        compact ? 'h-6 w-16 mb-2' : 'h-8 w-20 mb-3'
      )} />
      <Skeleton className={cn(
        compact ? 'h-3 w-24' : 'h-4 w-32'
      )} />
    </div>
  );
}
