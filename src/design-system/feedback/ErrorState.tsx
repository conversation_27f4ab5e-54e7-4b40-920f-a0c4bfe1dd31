import React, { useEffect } from 'react';
import { cn } from '@/lib/utils';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import * as Sentry from '@sentry/react';

export interface ErrorStateProps {
  title?: string;
  message?: string;
  error?: Error | unknown;
  onRetry?: () => void;
  supportLink?: string;
  className?: string;
  compact?: boolean;
}

export function ErrorState({
  title = 'Something went wrong',
  message = 'An unexpected error occurred. Please try again.',
  error,
  onRetry,
  supportLink,
  className,
  compact = false,
}: ErrorStateProps) {
  useEffect(() => {
    if (error && error instanceof Error) {
      // Log to Sentry
      Sentry.captureException(error, {
        tags: {
          component: 'ErrorState',
        },
        extra: {
          title,
          message,
        },
      });
      
      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error('ErrorState:', error);
      }
    }
  }, [error, title, message]);

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center text-center',
        compact ? 'py-8' : 'py-12',
        className
      )}
    >
      <div className={cn(
        'rounded-full bg-error/10 text-error mb-4',
        compact ? 'p-3' : 'p-4'
      )}>
        <AlertCircle className={compact ? 'h-6 w-6' : 'h-8 w-8'} />
      </div>
      
      <h3 className={cn(
        'font-semibold',
        compact ? 'text-base' : 'text-lg'
      )}>
        {title}
      </h3>
      
      <p className={cn(
        'text-muted-foreground mt-2 max-w-sm',
        compact ? 'text-sm' : 'text-base'
      )}>
        {message}
      </p>
      
      {process.env.NODE_ENV === 'development' && error instanceof Error && (
        <details className="mt-4 max-w-md">
          <summary className="cursor-pointer text-xs text-muted-foreground hover:text-foreground">
            Technical details
          </summary>
          <pre className="mt-2 p-3 bg-muted rounded-lg text-xs text-left overflow-auto">
            {error.stack || error.message}
          </pre>
        </details>
      )}
      
      <div className={cn(
        'flex flex-col sm:flex-row gap-3',
        compact ? 'mt-4' : 'mt-6'
      )}>
        {onRetry && (
          <Button
            onClick={onRetry}
            size={compact ? 'sm' : 'default'}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        )}
        {supportLink && (
          <Button
            variant="outline"
            size={compact ? 'sm' : 'default'}
            asChild
          >
            <a href={supportLink} target="_blank" rel="noopener noreferrer">
              Get Support
            </a>
          </Button>
        )}
      </div>
    </div>
  );
}
