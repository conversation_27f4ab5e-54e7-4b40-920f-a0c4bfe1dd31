import React from 'react';
import { createPortal } from 'react-dom';
import { ModernTabs, ModernTabsList, ModernTabsTrigger, ModernTabsContent } from '@/components/ui/modern-tabs';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { LucideIcon, Info } from 'lucide-react';
import { Panel } from '../primitives/Panel';
import { SkeletonCard } from '../feedback/SkeletonCard';
import { useTabKeyboardNavigation } from '@/hooks/useTabKeyboardNavigation';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export interface TabConfig {
  value: string;
  label: string;
  icon?: LucideIcon;
  badge?: number | string | { value: number | string; variant?: 'default' | 'secondary' | 'destructive' | 'outline' };
  disabled?: boolean;
  href?: string;
  description?: string;
}

export interface EnhancedTabsProps {
  tabs: TabConfig[];
  value: string;
  onValueChange: (value: string) => void;
  variant?: 'navigation' | 'content' | 'filter';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
  loading?: boolean;
  fullWidth?: boolean;
  indicatorStyle?: 'default' | 'underline' | 'pill' | 'neon' | 'blob' | 'wave' | 'magnetic' | 'laser' | 'orb' | 'circuit';
  alignTabs?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  hideLabels?: boolean;
  showLabelTooltips?: boolean;
  tooltipSide?: 'top' | 'right' | 'bottom' | 'left';
  triggerClassName?: string;
  tabListClassName?: string;
}

export function EnhancedTabs({
  tabs,
  value,
  onValueChange,
  variant = 'navigation',
  size = 'md',
  fullWidth = true,
  className,
  loading = false,
  children,
  enableKeyboardNavigation = true,
  indicatorStyle = 'underline',
  alignTabs = 'start',
  hideLabels = false,
  showLabelTooltips = false,
  tooltipSide = 'top',
  triggerClassName,
  tabListClassName,
}: EnhancedTabsProps & { enableKeyboardNavigation?: boolean }) {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const variantClasses = {
    navigation: 'glass-md glass-inset',
    content: 'bg-muted/30',
    filter: 'bg-transparent border-b',
  };

  const alignmentClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  // Grid column classes - Tailwind requires these to be statically analyzable
  // Enable keyboard navigation
  const tabValues = tabs.map(tab => tab.value);
  useTabKeyboardNavigation({
    tabs: tabValues,
    activeTab: value,
    onTabChange: onValueChange,
    enabled: enableKeyboardNavigation,
  });

  const useGrid = fullWidth && tabs.length <= 8;
  const gridColsClass = useGrid ? {
    2: 'sm:grid-cols-2',
    3: 'sm:grid-cols-3',
    4: 'sm:grid-cols-4',
    5: 'sm:grid-cols-5',
    6: 'sm:grid-cols-6',
    7: 'sm:grid-cols-7',
    8: 'sm:grid-cols-8',
  }[tabs.length] : '';

  return (
    <ModernTabs
      value={value}
      onValueChange={onValueChange}
      className={cn('space-y-6', className)}
    >
      <div className={cn(
        'rounded-xl p-2',
        variantClasses[variant],
        'motion-safe:animate-fade-in'
      )}>
        <ModernTabsList
          className={cn(
            'modern-tabs-list relative flex gap-2 flex-wrap sm:flex-nowrap',
            fullWidth && 'w-full',
            useGrid && 'sm:grid',
            gridColsClass,
            sizeClasses[size],
            // Only apply alignment classes when NOT using grid
            !useGrid && alignmentClasses[alignTabs],
            // When using grid, add place-items-center to center items in grid cells
            useGrid && 'place-items-center',
            tabListClassName
          )}
          indicatorStyle={indicatorStyle}
        >
          {tabs.map((tab, index) => (
            <EnhancedTabsTrigger
              key={tab.value}
              value={tab.value}
              icon={tab.icon}
              label={tab.label}
              badge={tab.badge}
              disabled={tab.disabled}
              staggerIndex={index}
              size={size}
              hideLabel={hideLabels}
              showTooltip={showLabelTooltips}
              tooltipSide={tooltipSide}
              triggerClassName={triggerClassName}
              description={tab.description}
            />
          ))}
        </ModernTabsList>
      </div>

      {loading ? (
        <SkeletonCard lines={5} />
      ) : (
        children
      )}
    </ModernTabs>
  );
}

interface EnhancedTabsTriggerProps {
  value: string;
  icon?: LucideIcon;
  label: string;
  badge?: number | string | { value: number | string; variant?: 'default' | 'secondary' | 'destructive' | 'outline' };
  disabled?: boolean;
  staggerIndex?: number;
  size?: 'sm' | 'md' | 'lg';
  hideLabel?: boolean;
  showTooltip?: boolean;
  tooltipSide?: 'top' | 'right' | 'bottom' | 'left';
  triggerClassName?: string;
  align?: 'start' | 'center' | 'end';
  description?: string;
}

function EnhancedTabsTrigger({
  value,
  icon: Icon,
  label,
  badge,
  disabled,
  staggerIndex = 0,
  size = 'md',
  hideLabel = false,
  showTooltip = false,
  tooltipSide = 'top',
  triggerClassName,
  align = 'center',
  description,
}: EnhancedTabsTriggerProps) {
  const [showDescription, setShowDescription] = React.useState(false);
  const [baseOpen, setBaseOpen] = React.useState(false);
  const [forceBaseOpen, setForceBaseOpen] = React.useState(false);
  const descriptionRef = React.useRef<HTMLDivElement>(null);
  const infoButtonRef = React.useRef<HTMLButtonElement>(null);
  const triggerRef = React.useRef<HTMLDivElement>(null);
  const timeoutRef = React.useRef<NodeJS.Timeout | undefined>(undefined);

  // Clean up on unmount
  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Handle click outside for description tooltip
  React.useEffect(() => {
    if (showDescription) {
      const handleClickOutside = (e: MouseEvent) => {
        const target = e.target as Node;
        if (
          descriptionRef.current && 
          !descriptionRef.current.contains(target) &&
          infoButtonRef.current &&
          !infoButtonRef.current.contains(target)
        ) {
          setShowDescription(false);
          setForceBaseOpen(false);
          setBaseOpen(false);
        }
      };
      
      // Add a small delay to prevent immediate closure
      const timer = setTimeout(() => {
        document.addEventListener('click', handleClickOutside);
      }, 100);
      
      return () => {
        clearTimeout(timer);
        document.removeEventListener('click', handleClickOutside);
      };
    }
  }, [showDescription]);

  // Reset state when hovering over different tabs
  React.useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      const target = e.target as Element;
      // Check if we're hovering over a different tab trigger
      const isOverDifferentTab = target.closest('.modern-tabs-trigger') && 
                                 !triggerRef.current?.contains(target);
      
      if (isOverDifferentTab) {
        setShowDescription(false);
        setForceBaseOpen(false);
        setBaseOpen(false);
      }
    };

    if (showTooltip && (baseOpen || showDescription)) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
      };
    }
  }, [baseOpen, showDescription, showTooltip]);
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-5 py-2.5 text-base',
  };

  const iconSizes = {
    sm: 'h-3.5 w-3.5',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  const alignmentClasses = {
    start: 'justify-start text-left',
    center: 'justify-center text-center',
    end: 'justify-end text-right',
  };

  const renderBadge = () => {
    if (!badge) return null;

    if (typeof badge === 'object') {
      return (
        <Badge
          variant={badge.variant || 'secondary'}
          className="ml-2 px-1.5 py-0 text-xs"
        >
          {badge.value}
        </Badge>
      );
    }

    const isNumber = typeof badge === 'number';
    return (
      <Badge
        variant={isNumber && badge > 0 ? 'default' : 'secondary'}
        className="ml-2 px-1.5 py-0 text-xs"
      >
        {badge}
      </Badge>
    );
  };

  const handleInfoClick = React.useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    const newShowDescription = !showDescription;
    setShowDescription(newShowDescription);
    
    if (newShowDescription) {
      setForceBaseOpen(true);
      setBaseOpen(true);
    } else {
      setForceBaseOpen(false);
      // Delay closing base tooltip
      timeoutRef.current = setTimeout(() => {
        setBaseOpen(false);
      }, 100);
    }
  }, [showDescription]);

  const triggerContent = (
    <div ref={triggerRef}>
      <ModernTabsTrigger
        value={value}
        disabled={disabled}
        aria-label={label}
        className={cn(
          'modern-tabs-trigger relative flex items-center gap-2 rounded-lg font-medium',
          'transition-all duration-200',
          'hover:bg-muted/50',
          'data-[state=active]:text-foreground data-[state=active]:font-semibold',
          'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
          disabled && 'opacity-50 cursor-not-allowed',
          'whitespace-nowrap', // Prevent text wrapping
          alignmentClasses[align],
          sizeClasses[size],
          staggerIndex !== undefined && 'trigger-stagger',
          triggerClassName
        )}
        style={{
          animationDelay: `${staggerIndex * 50}ms`,
        }}
      >
        {Icon && <Icon className={cn(iconSizes[size], 'flex-shrink-0')} />}
        {hideLabel ? (
          <span className="sr-only">{label}</span>
        ) : (
          <span className="truncate">{label}</span>
        )}
        {renderBadge()}
      </ModernTabsTrigger>
    </div>
  );

  if (showTooltip) {
    return (
      <>
        <TooltipProvider delayDuration={0}>
          <Tooltip 
            open={baseOpen || forceBaseOpen} 
            onOpenChange={(open) => {
              if (!forceBaseOpen) {
                setBaseOpen(open);
                if (!open) {
                  setShowDescription(false);
                }
              }
            }}
          >
            <TooltipTrigger asChild>
              {triggerContent}
            </TooltipTrigger>
            <TooltipContent 
              side={tooltipSide} 
              className="max-w-xs pointer-events-auto z-[1300]"
              sideOffset={8}
            >
              <div className="flex items-center gap-2">
                <span className="font-medium">{label}</span>
                {description && (
                  <button
                    ref={infoButtonRef}
                    onClick={handleInfoClick}
                    onMouseDown={(e) => e.preventDefault()}
                    className="inline-flex items-center justify-center p-0.5 rounded hover:bg-accent/20 transition-colors"
                    type="button"
                    aria-label="Show more information"
                  >
                    <Info className="h-3.5 w-3.5 text-muted-foreground hover:text-foreground transition-colors cursor-pointer" />
                  </button>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        {/* Render description tooltip separately using portal */}
        {description && showDescription && infoButtonRef.current && createPortal(
          <div 
            ref={descriptionRef}
            className="fixed max-w-xs p-3 z-[1400] glass-md shadow-lg border border-border/50 rounded-md bg-popover text-popover-foreground animate-in fade-in-0 zoom-in-95"
            style={{
              left: `${infoButtonRef.current.getBoundingClientRect().right + 10}px`,
              top: `${infoButtonRef.current.getBoundingClientRect().top - 10}px`,
            }}
          >
            <div className="space-y-2">
              <div className="flex items-center justify-between gap-3">
                <p className="font-medium">{label}</p>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowDescription(false);
                    setForceBaseOpen(false);
                    setBaseOpen(false);
                  }}
                  className="rounded hover:bg-accent/20 transition-colors p-0.5"
                  type="button"
                  aria-label="Close"
                >
                  <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">{description}</p>
            </div>
          </div>,
          document.body
        )}
      </>
    );
  }

  return triggerContent;
}

// TabPanel component for consistent tab content styling
export interface TabPanelProps {
  value: string;
  children: React.ReactNode;
  className?: string;
  noPadding?: boolean;
}

export function TabPanel({
  value,
  children,
  className,
  noPadding = false,
}: TabPanelProps) {
  return (
    <ModernTabsContent
      value={value}
      className={cn(
        'motion-safe:animate-fade-in',
        !noPadding && 'space-y-4',
        className
      )}
    >
      {children}
    </ModernTabsContent>
  );
}

// TabPanelWithCard - Tab content wrapped in a Panel
export interface TabPanelWithCardProps extends TabPanelProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  elevation?: 0 | 1 | 2 | 3 | 4;
}

export function TabPanelWithCard({
  value,
  title,
  description,
  actions,
  elevation = 1,
  children,
  className,
}: TabPanelWithCardProps) {
  return (
    <TabPanel value={value} noPadding>
      <Panel
        title={title}
        description={description}
        actions={actions}
        elevation={elevation}
        className={className}
      >
        {children}
      </Panel>
    </TabPanel>
  );
}
