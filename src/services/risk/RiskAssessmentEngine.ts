import { AdvancedAuditTrail } from "@/services/audit/AdvancedAuditTrail";
import { workflowLogger, LogLevel } from "@/services/WorkflowLogger";

export interface RiskFactor {
  id: string;
  name: string;
  category: 'data' | 'decision' | 'compliance' | 'operational' | 'security';
  weight: number; // 0-1
  description: string;
  evaluator: (context: any) => Promise<number>; // Returns 0-1 risk score
}

export interface RiskAssessment {
  id: string;
  entityType: string;
  entityId: string;
  workflowId?: string;
  executionId?: string;
  nodeId?: string;
  
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  riskScore: number; // 0-1
  confidence: number; // 0-1
  
  factors: Array<{
    factorId: string;
    score: number;
    weight: number;
    contribution: number;
    details?: any;
  }>;
  
  recommendations: Array<{
    type: 'escalate' | 'approve' | 'reject' | 'review' | 'monitor';
    reason: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    action?: string;
  }>;
  
  escalationTriggers: Array<{
    condition: string;
    threshold: number;
    action: 'human_review' | 'supervisor_approval' | 'immediate_stop' | 'enhanced_monitoring';
  }>;
  
  metadata: {
    assessedAt: Date;
    assessedBy: string;
    version: string;
    processingTime: number;
  };
}

export interface EscalationRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  action: 'human_review' | 'supervisor_approval' | 'immediate_stop' | 'enhanced_monitoring';
  escalationLevel: number;
  notifyUsers: string[];
  timeoutMinutes?: number;
  description: string;
}

export class RiskAssessmentEngine {
  private static instance: RiskAssessmentEngine;
  private auditTrail = AdvancedAuditTrail.getInstance();
  private riskFactors: Map<string, RiskFactor> = new Map();
  private escalationRules: EscalationRule[] = [];

  public static getInstance(): RiskAssessmentEngine {
    if (!RiskAssessmentEngine.instance) {
      RiskAssessmentEngine.instance = new RiskAssessmentEngine();
      RiskAssessmentEngine.instance.initializeDefaultFactors();
      RiskAssessmentEngine.instance.initializeDefaultEscalationRules();
    }
    return RiskAssessmentEngine.instance;
  }

  private initializeDefaultFactors(): void {
    // Data Quality Risk Factors
    this.registerRiskFactor({
      id: 'data_completeness',
      name: 'Data Completeness',
      category: 'data',
      weight: 0.3,
      description: 'Assesses completeness of candidate data',
      evaluator: async (context) => {
        const candidate = context.candidate || {};
        const requiredFields = ['name', 'email', 'resume', 'experience'];
        const missingFields = requiredFields.filter(field => !candidate[field]);
        return missingFields.length / requiredFields.length;
      },
    });

    this.registerRiskFactor({
      id: 'ai_confidence',
      name: 'AI Decision Confidence',
      category: 'decision',
      weight: 0.4,
      description: 'Measures AI confidence in decision-making',
      evaluator: async (context) => {
        const confidence = context.aiResult?.confidence || 0;
        return 1 - (confidence / 100); // Lower confidence = higher risk
      },
    });

    this.registerRiskFactor({
      id: 'sensitive_data',
      name: 'Sensitive Data Processing',
      category: 'compliance',
      weight: 0.5,
      description: 'Assesses risk of processing sensitive personal data',
      evaluator: async (context) => {
        const candidate = context.candidate || {};
        const sensitiveFields = ['ssn', 'passport', 'medical_info', 'criminal_background'];
        const hasSensitiveData = sensitiveFields.some(field => candidate[field]);
        return hasSensitiveData ? 0.8 : 0.2;
      },
    });

    this.registerRiskFactor({
      id: 'workflow_complexity',
      name: 'Workflow Complexity',
      category: 'operational',
      weight: 0.2,
      description: 'Assesses complexity of workflow execution',
      evaluator: async (context) => {
        const nodeCount = context.workflow?.nodes?.length || 0;
        const parallelPaths = context.workflow?.parallelPaths || 0;
        const complexity = (nodeCount / 20) + (parallelPaths / 5);
        return Math.min(complexity, 1);
      },
    });

    this.registerRiskFactor({
      id: 'previous_escalations',
      name: 'Previous Escalations',
      category: 'operational',
      weight: 0.3,
      description: 'Considers history of escalations for similar cases',
      evaluator: async (context) => {
        // In a real implementation, this would query historical data
        const escalationHistory = context.escalationHistory || [];
        const recentEscalations = escalationHistory.filter((e: any) => 
          new Date(e.date) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        );
        return Math.min(recentEscalations.length / 5, 1);
      },
    });
  }

  private initializeDefaultEscalationRules(): void {
    this.escalationRules = [
      {
        id: 'critical_risk',
        name: 'Critical Risk Level',
        condition: 'overallRisk === "critical"',
        threshold: 0.8,
        action: 'immediate_stop',
        escalationLevel: 1,
        notifyUsers: ['<EMAIL>', '<EMAIL>'],
        timeoutMinutes: 15,
        description: 'Immediately stop processing for critical risk levels',
      },
      {
        id: 'high_risk_sensitive_data',
        name: 'High Risk with Sensitive Data',
        condition: 'overallRisk === "high" && factors.sensitive_data > 0.7',
        threshold: 0.7,
        action: 'supervisor_approval',
        escalationLevel: 2,
        notifyUsers: ['<EMAIL>', '<EMAIL>'],
        timeoutMinutes: 60,
        description: 'Require supervisor approval for high-risk sensitive data processing',
      },
      {
        id: 'low_ai_confidence',
        name: 'Low AI Confidence',
        condition: 'factors.ai_confidence > 0.6',
        threshold: 0.6,
        action: 'human_review',
        escalationLevel: 3,
        notifyUsers: ['<EMAIL>'],
        timeoutMinutes: 120,
        description: 'Human review required for low AI confidence decisions',
      },
      {
        id: 'medium_risk_monitoring',
        name: 'Medium Risk Enhanced Monitoring',
        condition: 'overallRisk === "medium"',
        threshold: 0.5,
        action: 'enhanced_monitoring',
        escalationLevel: 4,
        notifyUsers: ['<EMAIL>'],
        description: 'Enhanced monitoring for medium risk operations',
      },
    ];
  }

  registerRiskFactor(factor: RiskFactor): void {
    this.riskFactors.set(factor.id, factor);
  }

  async assessRisk(context: {
    entityType: string;
    entityId: string;
    workflowId?: string;
    executionId?: string;
    nodeId?: string;
    candidate?: any;
    aiResult?: any;
    workflow?: any;
    escalationHistory?: any[];
    [key: string]: any;
  }): Promise<RiskAssessment> {
    const startTime = Date.now();
    
    try {
      const assessment: RiskAssessment = {
        id: crypto.randomUUID(),
        entityType: context.entityType,
        entityId: context.entityId,
        workflowId: context.workflowId,
        executionId: context.executionId,
        nodeId: context.nodeId,
        overallRisk: 'low',
        riskScore: 0,
        confidence: 0,
        factors: [],
        recommendations: [],
        escalationTriggers: [],
        metadata: {
          assessedAt: new Date(),
          assessedBy: 'RiskAssessmentEngine',
          version: '1.0',
          processingTime: 0,
        },
      };

      // Evaluate all risk factors
      let totalWeightedScore = 0;
      let totalWeight = 0;

      for (const [factorId, factor] of this.riskFactors) {
        try {
          const score = await factor.evaluator(context);
          const contribution = score * factor.weight;
          
          assessment.factors.push({
            factorId,
            score,
            weight: factor.weight,
            contribution,
          });

          totalWeightedScore += contribution;
          totalWeight += factor.weight;
        } catch (error) {
          console.error(`Error evaluating risk factor ${factorId}:`, error);
        }
      }

      // Calculate overall risk score
      assessment.riskScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
      assessment.confidence = Math.min(totalWeight, 1); // Confidence based on factor coverage

      // Determine risk level
      if (assessment.riskScore >= 0.8) {
        assessment.overallRisk = 'critical';
      } else if (assessment.riskScore >= 0.6) {
        assessment.overallRisk = 'high';
      } else if (assessment.riskScore >= 0.3) {
        assessment.overallRisk = 'medium';
      } else {
        assessment.overallRisk = 'low';
      }

      // Generate recommendations
      assessment.recommendations = this.generateRecommendations(assessment);

      // Check escalation triggers
      assessment.escalationTriggers = this.checkEscalationTriggers(assessment);

      // Record processing time
      assessment.metadata.processingTime = Date.now() - startTime;

      // Log the risk assessment
      await this.auditTrail.logEvent({
        eventType: 'execution',
        entityType: context.entityType as any,
        entityId: context.entityId,
        userId: 'system',
        workflowId: context.workflowId,
        executionId: context.executionId,
        nodeId: context.nodeId,
        context: {
          riskAssessment: {
            id: assessment.id,
            overallRisk: assessment.overallRisk,
            riskScore: assessment.riskScore,
            confidence: assessment.confidence,
            factorCount: assessment.factors.length,
            escalationTriggersCount: assessment.escalationTriggers.length,
          },
        },
        metadata: {
          timestamp: new Date(),
          success: true,
          duration: assessment.metadata.processingTime,
        },
        compliance: {
          sensitivityLevel: assessment.overallRisk === 'critical' ? 'critical' : 'high',
          gdprRelevant: true,
          retentionPeriod: 2555, // 7 years
        },
      });

      return assessment;
    } catch (error) {
      console.error('Risk assessment failed:', error);
      throw error;
    }
  }

  private generateRecommendations(assessment: RiskAssessment): RiskAssessment['recommendations'] {
    const recommendations: RiskAssessment['recommendations'] = [];

    switch (assessment.overallRisk) {
      case 'critical':
        recommendations.push({
          type: 'escalate',
          reason: 'Critical risk level detected - immediate escalation required',
          priority: 'urgent',
          action: 'Stop processing and notify supervisors immediately',
        });
        break;

      case 'high':
        recommendations.push({
          type: 'review',
          reason: 'High risk level - human review recommended',
          priority: 'high',
          action: 'Route to experienced reviewer for manual assessment',
        });
        break;

      case 'medium':
        recommendations.push({
          type: 'monitor',
          reason: 'Medium risk level - enhanced monitoring advised',
          priority: 'medium',
          action: 'Continue with additional logging and monitoring',
        });
        break;

      case 'low':
        recommendations.push({
          type: 'approve',
          reason: 'Low risk level - safe to proceed autonomously',
          priority: 'low',
          action: 'Continue with standard processing',
        });
        break;
    }

    // Add specific factor-based recommendations
    const lowConfidenceFactor = assessment.factors.find(f => 
      f.factorId === 'ai_confidence' && f.score > 0.6
    );
    if (lowConfidenceFactor) {
      recommendations.push({
        type: 'review',
        reason: 'Low AI confidence detected',
        priority: 'medium',
        action: 'Human review of AI decision recommended',
      });
    }

    const sensitiveDataFactor = assessment.factors.find(f => 
      f.factorId === 'sensitive_data' && f.score > 0.7
    );
    if (sensitiveDataFactor) {
      recommendations.push({
        type: 'escalate',
        reason: 'Sensitive data processing detected',
        priority: 'high',
        action: 'Ensure compliance with data protection regulations',
      });
    }

    return recommendations;
  }

  private checkEscalationTriggers(assessment: RiskAssessment): RiskAssessment['escalationTriggers'] {
    const triggers: RiskAssessment['escalationTriggers'] = [];

    for (const rule of this.escalationRules) {
      if (this.evaluateEscalationCondition(rule, assessment)) {
        triggers.push({
          condition: rule.condition,
          threshold: rule.threshold,
          action: rule.action,
        });
      }
    }

    return triggers;
  }

  private evaluateEscalationCondition(rule: EscalationRule, assessment: RiskAssessment): boolean {
    try {
      // Simple condition evaluation - in production, use a proper expression evaluator
      const condition = rule.condition
        .replace(/overallRisk/g, `"${assessment.overallRisk}"`)
        .replace(/riskScore/g, assessment.riskScore.toString());

      // Handle factor references
      for (const factor of assessment.factors) {
        const factorRef = `factors.${factor.factorId}`;
        condition.replace(new RegExp(factorRef, 'g'), factor.score.toString());
      }

      // For demo purposes, use simple string matching
      if (rule.condition.includes('overallRisk === "critical"')) {
        return assessment.overallRisk === 'critical';
      }
      if (rule.condition.includes('overallRisk === "high"')) {
        return assessment.overallRisk === 'high';
      }
      if (rule.condition.includes('overallRisk === "medium"')) {
        return assessment.overallRisk === 'medium';
      }
      if (rule.condition.includes('factors.ai_confidence > 0.6')) {
        const aiConfidenceFactor = assessment.factors.find(f => f.factorId === 'ai_confidence');
        return aiConfidenceFactor ? aiConfidenceFactor.score > 0.6 : false;
      }

      return assessment.riskScore >= rule.threshold;
    } catch (error) {
      console.error('Error evaluating escalation condition:', error);
      return false;
    }
  }

  async triggerEscalation(
    assessment: RiskAssessment,
    trigger: RiskAssessment['escalationTriggers'][0],
    context: any
  ): Promise<void> {
    try {
      const rule = this.escalationRules.find(r => r.condition === trigger.condition);
      if (!rule) return;

      await workflowLogger.log({
        level: LogLevel.WARN,
        message: `Risk escalation triggered: ${rule.name}`,
        workflowId: assessment.workflowId,
        executionId: assessment.executionId,
        nodeId: assessment.nodeId,
        metadata: {
          riskAssessmentId: assessment.id,
          escalationRule: rule.id,
          action: trigger.action,
          riskScore: assessment.riskScore,
          overallRisk: assessment.overallRisk,
        },
      });

      // Log escalation event
      await this.auditTrail.logEvent({
        eventType: 'escalation',
        entityType: assessment.entityType as any,
        entityId: assessment.entityId,
        userId: context.userId || 'system',
        workflowId: assessment.workflowId,
        executionId: assessment.executionId,
        nodeId: assessment.nodeId,
        context: {
          riskAssessmentId: assessment.id,
          escalationRule: rule.id,
          trigger: trigger,
          riskScore: assessment.riskScore,
          overallRisk: assessment.overallRisk,
        },
        metadata: {
          timestamp: new Date(),
          success: true,
        },
        compliance: {
          sensitivityLevel: 'critical',
          gdprRelevant: true,
          retentionPeriod: 2555, // 7 years
        },
      });

      // Execute escalation action
      switch (trigger.action) {
        case 'immediate_stop':
          await this.executeImmediateStop(assessment, rule, context);
          break;
        case 'supervisor_approval':
          await this.requestSupervisorApproval(assessment, rule, context);
          break;
        case 'human_review':
          await this.requestHumanReview(assessment, rule, context);
          break;
        case 'enhanced_monitoring':
          await this.enableEnhancedMonitoring(assessment, rule, context);
          break;
      }
    } catch (error) {
      console.error('Error triggering escalation:', error);
      throw error;
    }
  }

  private async executeImmediateStop(assessment: RiskAssessment, rule: EscalationRule, context: any): Promise<void> {
    // Implementation would stop workflow execution and notify stakeholders
    console.log(`IMMEDIATE STOP: ${rule.name} - Risk Assessment ${assessment.id}`);
  }

  private async requestSupervisorApproval(assessment: RiskAssessment, rule: EscalationRule, context: any): Promise<void> {
    // Implementation would create supervisor approval request
    console.log(`SUPERVISOR APPROVAL: ${rule.name} - Risk Assessment ${assessment.id}`);
  }

  private async requestHumanReview(assessment: RiskAssessment, rule: EscalationRule, context: any): Promise<void> {
    // Implementation would create human review task
    console.log(`HUMAN REVIEW: ${rule.name} - Risk Assessment ${assessment.id}`);
  }

  private async enableEnhancedMonitoring(assessment: RiskAssessment, rule: EscalationRule, context: any): Promise<void> {
    // Implementation would enable enhanced monitoring
    console.log(`ENHANCED MONITORING: ${rule.name} - Risk Assessment ${assessment.id}`);
  }
}
