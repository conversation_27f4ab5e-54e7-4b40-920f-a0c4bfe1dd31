/**
 * Production Deployment Service - Deploy autonomous workflows to production with comprehensive monitoring
 */

import { supabase } from '@/integrations/supabase/client';
import { WorkflowTemplate } from '@/components/ai/workflow/types';
import { AdvancedAuditTrail } from '@/services/audit/AdvancedAuditTrail';
import { intelligentDecisionEngine } from '@/services/decision/IntelligentDecisionEngine';
import { continuousLearningSystem } from '@/services/learning/ContinuousLearningSystem';

export interface DeploymentConfig {
  workflowTemplate: WorkflowTemplate;
  environment: 'staging' | 'production';
  autoStart: boolean;
  monitoringEnabled: boolean;
  learningEnabled: boolean;
  approvalRequired: boolean;
  rollbackOnFailure: boolean;
  healthCheckInterval: number; // minutes
  maxConcurrentExecutions: number;
  resourceLimits: {
    maxMemoryMB: number;
    maxExecutionTimeMs: number;
    maxApiCallsPerMinute: number;
  };
}

export interface DeploymentStatus {
  id: string;
  workflowId: string;
  status: 'deploying' | 'active' | 'paused' | 'failed' | 'rollback';
  environment: string;
  deployedAt: Date;
  lastHealthCheck: Date;
  healthStatus: 'healthy' | 'warning' | 'critical';
  metrics: {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    lastExecution?: Date;
  };
  alerts: DeploymentAlert[];
}

export interface DeploymentAlert {
  id: string;
  type: 'performance' | 'error' | 'resource' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  resolved: boolean;
}

export class ProductionDeploymentService {
  private static instance: ProductionDeploymentService;
  private auditTrail: AdvancedAuditTrail;
  private deployments: Map<string, DeploymentStatus> = new Map();

  private constructor() {
    this.auditTrail = AdvancedAuditTrail.getInstance();
    this.initializeDeploymentMonitoring();
  }

  public static getInstance(): ProductionDeploymentService {
    if (!ProductionDeploymentService.instance) {
      ProductionDeploymentService.instance = new ProductionDeploymentService();
    }
    return ProductionDeploymentService.instance;
  }

  /**
   * Deploy a workflow template to production
   */
  async deployWorkflow(config: DeploymentConfig): Promise<DeploymentStatus> {
    try {
      // Validate deployment configuration
      await this.validateDeploymentConfig(config);

      // Create workflow configuration in database
      const workflowId = await this.createWorkflowConfiguration(config);

      // Initialize deployment status
      const deploymentStatus: DeploymentStatus = {
        id: workflowId,
        workflowId,
        status: 'deploying',
        environment: config.environment,
        deployedAt: new Date(),
        lastHealthCheck: new Date(),
        healthStatus: 'healthy',
        metrics: {
          totalExecutions: 0,
          successfulExecutions: 0,
          failedExecutions: 0,
          averageExecutionTime: 0,
        },
        alerts: [],
      };

      this.deployments.set(workflowId, deploymentStatus);

      // Deploy workflow components
      await this.deployWorkflowComponents(config, workflowId);

      // Set up monitoring
      if (config.monitoringEnabled) {
        await this.setupWorkflowMonitoring(workflowId, config);
      }

      // Set up learning
      if (config.learningEnabled) {
        await this.setupWorkflowLearning(workflowId, config);
      }

      // Start workflow if auto-start is enabled
      if (config.autoStart) {
        await this.startWorkflow(workflowId);
      }

      // Update deployment status
      deploymentStatus.status = config.autoStart ? 'active' : 'paused';
      this.deployments.set(workflowId, deploymentStatus);

      // Log deployment to audit trail
      const { data: user } = await supabase.auth.getUser();
      await this.auditTrail.logEvent({
        eventType: 'execution',
        entityType: 'workflow',
        entityId: workflowId,
        userId: user.user?.id || workflowId, // Use actual user ID or workflow ID as fallback
        workflowId,
        context: {
          action: 'workflow_deployment',
          environment: config.environment,
          template: config.workflowTemplate.name,
          autoStart: config.autoStart,
        },
        metadata: {
          timestamp: new Date(),
          success: true,
        },
        compliance: {
          sensitivityLevel: 'high',
          gdprRelevant: false,
          retentionPeriod: 2555,
        },
      });

      return deploymentStatus;

    } catch (error) {
      console.error('Error deploying workflow:', error);
      throw new Error(`Deployment failed: ${error}`);
    }
  }

  /**
   * Get deployment status for a workflow
   */
  async getDeploymentStatus(workflowId: string): Promise<DeploymentStatus | null> {
    const status = this.deployments.get(workflowId);
    if (status) {
      // Update metrics from database
      await this.updateDeploymentMetrics(status);
    }
    return status || null;
  }

  /**
   * Get all active deployments
   */
  async getAllDeployments(): Promise<DeploymentStatus[]> {
    const deployments = Array.from(this.deployments.values());
    
    // Update metrics for all deployments
    for (const deployment of deployments) {
      await this.updateDeploymentMetrics(deployment);
    }

    return deployments;
  }

  /**
   * Pause a deployed workflow
   */
  async pauseWorkflow(workflowId: string): Promise<void> {
    const deployment = this.deployments.get(workflowId);
    if (!deployment) {
      throw new Error(`Deployment not found: ${workflowId}`);
    }

    // Update workflow configuration
    await supabase
      .from('workflow_configurations')
      .update({ is_active: false })
      .eq('id', workflowId);

    // Update deployment status
    deployment.status = 'paused';
    this.deployments.set(workflowId, deployment);

    // Log pause action
    const { data: user } = await supabase.auth.getUser();
    await this.auditTrail.logEvent({
      eventType: 'execution',
      entityType: 'workflow',
      entityId: workflowId,
      userId: user.user?.id || workflowId,
      workflowId,
      context: {
        action: 'workflow_pause',
        environment: deployment.environment,
      },
      metadata: {
        timestamp: new Date(),
        success: true,
      },
      compliance: {
        sensitivityLevel: 'medium',
        gdprRelevant: false,
        retentionPeriod: 365,
      },
    });
  }

  /**
   * Resume a paused workflow
   */
  async resumeWorkflow(workflowId: string): Promise<void> {
    const deployment = this.deployments.get(workflowId);
    if (!deployment) {
      throw new Error(`Deployment not found: ${workflowId}`);
    }

    // Update workflow configuration
    await supabase
      .from('workflow_configurations')
      .update({ is_active: true })
      .eq('id', workflowId);

    // Update deployment status
    deployment.status = 'active';
    this.deployments.set(workflowId, deployment);

    // Log resume action
    const { data: user } = await supabase.auth.getUser();
    await this.auditTrail.logEvent({
      eventType: 'execution',
      entityType: 'workflow',
      entityId: workflowId,
      userId: user.user?.id || workflowId,
      workflowId,
      context: {
        action: 'workflow_resume',
        environment: deployment.environment,
      },
      metadata: {
        timestamp: new Date(),
        success: true,
      },
      compliance: {
        sensitivityLevel: 'medium',
        gdprRelevant: false,
        retentionPeriod: 365,
      },
    });
  }

  /**
   * Rollback a deployment
   */
  async rollbackDeployment(workflowId: string, reason: string): Promise<void> {
    const deployment = this.deployments.get(workflowId);
    if (!deployment) {
      throw new Error(`Deployment not found: ${workflowId}`);
    }

    // Pause the workflow
    await this.pauseWorkflow(workflowId);

    // Update deployment status
    deployment.status = 'rollback';
    deployment.alerts.push({
      id: `rollback_${Date.now()}`,
      type: 'error',
      severity: 'high',
      message: `Deployment rolled back: ${reason}`,
      timestamp: new Date(),
      resolved: false,
    });
    this.deployments.set(workflowId, deployment);

    // Log rollback action
    const { data: user } = await supabase.auth.getUser();
    await this.auditTrail.logEvent({
      eventType: 'error',
      entityType: 'workflow',
      entityId: workflowId,
      userId: user.user?.id || workflowId,
      workflowId,
      context: {
        action: 'workflow_rollback',
        reason,
        environment: deployment.environment,
      },
      metadata: {
        timestamp: new Date(),
        success: true,
      },
      compliance: {
        sensitivityLevel: 'high',
        gdprRelevant: false,
        retentionPeriod: 2555,
      },
    });
  }

  /**
   * Get deployment health metrics
   */
  async getHealthMetrics(workflowId: string): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    metrics: Record<string, number>;
    alerts: DeploymentAlert[];
    recommendations: string[];
  }> {
    const deployment = this.deployments.get(workflowId);
    if (!deployment) {
      throw new Error(`Deployment not found: ${workflowId}`);
    }

    await this.updateDeploymentMetrics(deployment);

    const recommendations = [];
    let healthStatus: 'healthy' | 'warning' | 'critical' = 'healthy';

    // Analyze metrics for health status
    const successRate = deployment.metrics.totalExecutions > 0 ? 
      deployment.metrics.successfulExecutions / deployment.metrics.totalExecutions : 1;

    if (successRate < 0.8) {
      healthStatus = 'critical';
      recommendations.push('Low success rate detected - review workflow logic');
    } else if (successRate < 0.9) {
      healthStatus = 'warning';
      recommendations.push('Success rate below optimal - monitor for patterns');
    }

    if (deployment.metrics.averageExecutionTime > 30000) { // 30 seconds
      if (healthStatus === 'healthy') healthStatus = 'warning';
      recommendations.push('High execution time - consider optimization');
    }

    // Check for recent failures
    const recentFailures = deployment.alerts.filter(
      alert => alert.type === 'error' && 
      alert.timestamp > new Date(Date.now() - 60 * 60 * 1000) // Last hour
    );

    if (recentFailures.length > 5) {
      healthStatus = 'critical';
      recommendations.push('High error rate in the last hour - investigate immediately');
    }

    deployment.healthStatus = healthStatus;
    this.deployments.set(workflowId, deployment);

    return {
      status: healthStatus,
      metrics: {
        successRate,
        totalExecutions: deployment.metrics.totalExecutions,
        averageExecutionTime: deployment.metrics.averageExecutionTime,
        errorRate: 1 - successRate,
      },
      alerts: deployment.alerts.filter(alert => !alert.resolved),
      recommendations,
    };
  }

  // Private helper methods

  private async validateDeploymentConfig(config: DeploymentConfig): Promise<void> {
    if (!config.workflowTemplate) {
      throw new Error('Workflow template is required');
    }

    if (!config.workflowTemplate.nodes || config.workflowTemplate.nodes.length === 0) {
      throw new Error('Workflow template must have at least one node');
    }

    if (config.maxConcurrentExecutions < 1) {
      throw new Error('Max concurrent executions must be at least 1');
    }

    if (config.resourceLimits.maxMemoryMB < 128) {
      throw new Error('Max memory must be at least 128MB');
    }
  }

  private async createWorkflowConfiguration(config: DeploymentConfig): Promise<string> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('workflow_configurations')
      .insert({
        name: config.workflowTemplate.name,
        description: config.workflowTemplate.description,
        config: {
          nodes: config.workflowTemplate.nodes,
          edges: config.workflowTemplate.edges,
          metadata: config.workflowTemplate.metadata,
        },
        is_active: config.autoStart,
        created_by: user.user.id,
        agent_type: 'screening', // Use existing valid agent type
        autonomy_level: 'autonomous', // High autonomy for production workflows
        goals: config.workflowTemplate.metadata.features,
        learning_enabled: config.learningEnabled,
        continuous_mode: true,
        performance_metrics: {
          maxConcurrentExecutions: config.maxConcurrentExecutions,
          resourceLimits: config.resourceLimits,
          healthCheckInterval: config.healthCheckInterval,
        },
      })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create workflow configuration: ${error.message}`);
    }

    return data.id;
  }

  private async deployWorkflowComponents(config: DeploymentConfig, workflowId: string): Promise<void> {
    // In a real implementation, this would:
    // 1. Deploy workflow nodes to execution environment
    // 2. Set up triggers and webhooks
    // 3. Configure resource limits
    // 4. Set up networking and security
    
    // For now, we'll simulate the deployment process
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private async setupWorkflowMonitoring(workflowId: string, config: DeploymentConfig): Promise<void> {
    // Set up monitoring alerts and health checks
    const deployment = this.deployments.get(workflowId);
    if (deployment) {
      // Schedule health checks
      setInterval(async () => {
        await this.performHealthCheck(workflowId);
      }, config.healthCheckInterval * 60 * 1000);
    }
  }

  private async setupWorkflowLearning(workflowId: string, config: DeploymentConfig): Promise<void> {
    // Initialize learning system for this workflow
    await continuousLearningSystem.recordLearningData({
      agentType: 'autonomous_workflow',
      taskType: config.workflowTemplate.category,
      inputData: { workflowId, template: config.workflowTemplate.name },
      aiDecision: { decision: 'deploy', confidence: 1.0 },
      outcome: { success: true, timeToComplete: 1000 },
      timestamp: new Date(),
      contextMetadata: { environment: config.environment },
    });
  }

  private async startWorkflow(workflowId: string): Promise<void> {
    // Start the workflow execution
    await supabase
      .from('workflow_configurations')
      .update({ 
        is_active: true,
        last_continuous_run: new Date().toISOString(),
      })
      .eq('id', workflowId);
  }

  private async updateDeploymentMetrics(deployment: DeploymentStatus): Promise<void> {
    try {
      // Query workflow execution metrics
      const { data: executions, error } = await supabase
        .from('workflow_executions')
        .select('status, execution_time_ms, created_at')
        .eq('workflow_id', deployment.workflowId)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

      if (error) {
        console.error('Error updating deployment metrics:', error);
        return;
      }

      const totalExecutions = executions?.length || 0;
      const successfulExecutions = executions?.filter(e => e.status === 'completed').length || 0;
      const failedExecutions = executions?.filter(e => e.status === 'failed').length || 0;
      
      const executionTimes = executions?.filter(e => e.execution_time_ms).map(e => e.execution_time_ms) || [];
      const averageExecutionTime = executionTimes.length > 0 ? 
        executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length : 0;

      const lastExecution = executions && executions.length > 0 ? 
        new Date(Math.max(...executions.map(e => new Date(e.created_at).getTime()))) : undefined;

      deployment.metrics = {
        totalExecutions,
        successfulExecutions,
        failedExecutions,
        averageExecutionTime,
        lastExecution,
      };

      deployment.lastHealthCheck = new Date();
      this.deployments.set(deployment.workflowId, deployment);

    } catch (error) {
      console.error('Error updating deployment metrics:', error);
    }
  }

  private async performHealthCheck(workflowId: string): Promise<void> {
    const deployment = this.deployments.get(workflowId);
    if (!deployment) return;

    try {
      await this.updateDeploymentMetrics(deployment);
      
      // Check for issues and create alerts
      const healthMetrics = await this.getHealthMetrics(workflowId);
      
      if (healthMetrics.status === 'critical') {
        deployment.alerts.push({
          id: `health_${Date.now()}`,
          type: 'performance',
          severity: 'critical',
          message: 'Workflow health check failed - critical issues detected',
          timestamp: new Date(),
          resolved: false,
        });
      }

    } catch (error) {
      console.error('Health check failed for workflow:', workflowId, error);
      
      deployment.alerts.push({
        id: `health_error_${Date.now()}`,
        type: 'error',
        severity: 'high',
        message: `Health check failed: ${error}`,
        timestamp: new Date(),
        resolved: false,
      });
    }

    this.deployments.set(workflowId, deployment);
  }

  private initializeDeploymentMonitoring(): void {
    // Initialize monitoring for existing deployments
    this.loadExistingDeployments();
    
    // Set up periodic health checks
    setInterval(async () => {
      for (const [workflowId] of this.deployments) {
        await this.performHealthCheck(workflowId);
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  private async loadExistingDeployments(): Promise<void> {
    try {
      const { data: workflows, error } = await supabase
        .from('workflow_configurations')
        .select('*')
        .eq('agent_type', 'autonomous')
        .eq('continuous_mode', true);

      if (error) {
        console.error('Error loading existing deployments:', error);
        return;
      }

      for (const workflow of workflows || []) {
        const deploymentStatus: DeploymentStatus = {
          id: workflow.id,
          workflowId: workflow.id,
          status: workflow.is_active ? 'active' : 'paused',
          environment: 'production', // Default to production
          deployedAt: new Date(workflow.created_at),
          lastHealthCheck: new Date(),
          healthStatus: 'healthy',
          metrics: {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageExecutionTime: 0,
          },
          alerts: [],
        };

        this.deployments.set(workflow.id, deploymentStatus);
      }

    } catch (error) {
      console.error('Error loading existing deployments:', error);
    }
  }
}

export const productionDeploymentService = ProductionDeploymentService.getInstance();
