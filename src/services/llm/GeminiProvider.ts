/**
 * Google Gemini LLM Provider Implementation
 */

import { GoogleGenerativeAI, SchemaType } from "@google/generative-ai";
import { LL<PERSON>rovider } from './LLMProvider';
import {
  LLMRequest,
  LLMResponse,
  LLMToolRequest,
  LLMToolResponse,
  LLMProviderConfig,
  LLMMessage,
  LLMTool
} from './types';

export class GeminiProvider extends LLMProvider {
  private apiKey: string;
  private genAI: GoogleGenerativeAI;

  constructor(config: LLMProviderConfig) {
    super(config);
    this.apiKey = config.apiKey || import.meta.env.VITE_GEMINI_API_KEY || '';

    if (!this.apiKey) {
      throw new Error('Gemini API key is required');
    }

    this.genAI = new GoogleGenerativeAI(this.apiKey);
  }

  get name(): string {
    return this.config.name;
  }

  get type(): 'gemini' {
    return 'gemini';
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiKey) {
      console.warn('Gemini API key not configured');
      return false;
    }

    try {
      // Try to get the model to test availability
      const model = this.genAI.getGenerativeModel({ model: this.config.model });
      return !!model;
    } catch (error) {
      console.error('Gemini availability check failed:', error);
      return false;
    }
  }

  async generateResponse(request: LLMRequest): Promise<LLMResponse> {
    this.validateRequest(request);
    await this.checkRateLimit();

    const startTime = Date.now();

    try {
      const model = this.genAI.getGenerativeModel({
        model: this.config.model,
        generationConfig: {
          temperature: request.temperature ?? this.config.temperature,
          maxOutputTokens: request.maxTokens ?? this.config.maxTokens,
        }
      });

      // Combine system prompt and messages
      const systemPrompt = request.messages.find(msg => msg.role === 'system')?.content || request.systemPrompt;
      const userMessages = request.messages.filter(msg => msg.role !== 'system');

      let prompt = '';
      if (systemPrompt) {
        prompt += `System: ${systemPrompt}\n\n`;
      }

      // Add conversation history
      userMessages.forEach(msg => {
        if (msg.role === 'user') {
          prompt += `User: ${msg.content}\n`;
        } else if (msg.role === 'assistant') {
          prompt += `Assistant: ${msg.content}\n`;
        }
      });

      const result = await model.generateContent(prompt);
      const response = result.response;
      const text = response.text();

      const executionTimeMs = Date.now() - startTime;

      return {
        content: text,
        usage: {
          promptTokens: 0, // Gemini doesn't provide token counts in the free tier
          completionTokens: 0,
          totalTokens: 0,
        },
        model: this.config.model,
        finishReason: 'stop',
        executionTimeMs,
      };
    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw LLMError
      }
      throw this.createError(
        `Gemini API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'API_ERROR',
        true
      );
    }
  }

  async generateWithTools(request: LLMToolRequest): Promise<LLMToolResponse> {
    this.validateRequest(request);
    await this.checkRateLimit();

    const startTime = Date.now();

    try {
      // Convert tools to Gemini function declarations
      const functionDeclarations = request.tools.map(tool => ({
        name: tool.function.name,
        description: tool.function.description,
        parameters: {
          type: SchemaType.OBJECT,
          properties: tool.function.parameters.properties || {},
          required: tool.function.parameters.required || [],
        },
      }));

      const model = this.genAI.getGenerativeModel({
        model: this.config.model,
        tools: [{ functionDeclarations }],
        generationConfig: {
          temperature: request.temperature ?? this.config.temperature,
          maxOutputTokens: request.maxTokens ?? this.config.maxTokens,
        }
      });

      // Combine system prompt and messages
      const systemPrompt = request.messages.find(msg => msg.role === 'system')?.content || request.systemPrompt;
      const userMessages = request.messages.filter(msg => msg.role !== 'system');

      let prompt = '';
      if (systemPrompt) {
        prompt += `System: ${systemPrompt}\n\n`;
      }

      // Add conversation history
      userMessages.forEach(msg => {
        if (msg.role === 'user') {
          prompt += `User: ${msg.content}\n`;
        } else if (msg.role === 'assistant') {
          prompt += `Assistant: ${msg.content}\n`;
        }
      });

      const result = await model.generateContent(prompt);
      const response = result.response;

      const executionTimeMs = Date.now() - startTime;

      // Check if there are function calls
      const functionCalls = response.functionCalls();
      const toolCalls = functionCalls?.map((call, index) => ({
        id: `call_${index}`,
        type: 'function' as const,
        function: {
          name: call.name,
          arguments: JSON.stringify(call.args || {}),
        },
      }));

      return {
        content: response.text() || '',
        usage: {
          promptTokens: 0, // Gemini doesn't provide token counts in the free tier
          completionTokens: 0,
          totalTokens: 0,
        },
        model: this.config.model,
        finishReason: toolCalls && toolCalls.length > 0 ? 'tool_calls' : 'stop',
        executionTimeMs,
        toolCalls,
      };
    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw LLMError
      }
      throw this.createError(
        `Gemini API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'API_ERROR',
        true
      );
    }
  }


}
