/**
 * Types and interfaces for LLM integration
 */

export interface LLMProvider {
  name: string;
  type: 'openai' | 'gemini' | 'llama' | 'anthropic' | 'groq';
  isAvailable(): Promise<boolean>;
  generateResponse(request: LLMRequest): Promise<LLMResponse>;
  generateWithTools(request: LLMToolRequest): Promise<LLMToolResponse>;
}

export interface LLMRequest {
  messages: LLMMessage[];
  temperature?: number;
  maxTokens?: number;
  model?: string;
  systemPrompt?: string;
}

export interface LLMToolRequest extends LLMRequest {
  tools: LLMTool[];
  toolChoice?: 'auto' | 'none' | string;
}

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCallId?: string;
  toolCalls?: LLMToolCall[];
}

export interface LLMTool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: Record<string, any>;
  };
}

export interface LLMToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface LLMResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: 'stop' | 'length' | 'tool_calls' | 'content_filter';
  executionTimeMs: number;
}

export interface LLMToolResponse extends LLMResponse {
  toolCalls?: LLMToolCall[];
}

export interface LLMProviderConfig {
  id: string;
  name: string;
  type: 'openai' | 'gemini' | 'llama' | 'anthropic' | 'groq';
  apiKey?: string;
  endpoint: string;
  model: string;
  isActive: boolean;
  rateLimitPerMinute: number;
  maxTokens: number;
  temperature: number;
  timeoutSeconds: number;
  priority: number;
}

export interface LLMError extends Error {
  code: string;
  provider: string;
  retryable: boolean;
  rateLimited?: boolean;
}

export interface RateLimitInfo {
  requestsRemaining: number;
  resetTime: Date;
  retryAfter?: number;
}

export interface LLMUsageStats {
  providerId: string;
  requestCount: number;
  tokenCount: number;
  errorCount: number;
  averageResponseTime: number;
  lastUsed: Date;
}
