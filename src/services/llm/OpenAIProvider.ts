/**
 * OpenAI LLM Provider Implementation
 */

import { LL<PERSON>rovider } from './LLMProvider';
import { 
  LLMRequest, 
  LLMResponse, 
  LLMToolRequest, 
  LLMToolResponse,
  LLMProviderConfig,
  LLMMessage,
  LLMTool
} from './types';

export class OpenAIProvider extends LLMProvider {
  private apiKey: string;

  constructor(config: LLMProviderConfig) {
    super(config);
    this.apiKey = config.apiKey || '';
  }

  get name(): string {
    return this.config.name;
  }

  get type(): 'openai' {
    return 'openai';
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiKey) {
      console.warn('OpenAI API key not configured');
      return false;
    }

    try {
      const response = await fetch(`${this.config.endpoint}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      return response.ok;
    } catch (error) {
      console.error('OpenAI availability check failed:', error);
      return false;
    }
  }

  async generateResponse(request: LLMRequest): Promise<LLMResponse> {
    this.validateRequest(request);
    await this.checkRateLimit();

    const startTime = Date.now();

    try {
      const openaiRequest = this.convertToOpenAIFormat(request);
      
      const response = await fetch(`${this.config.endpoint}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(openaiRequest),
      });

      // Update rate limit info from headers
      this.updateRateLimit(Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw this.createError(
          errorData.error?.message || `HTTP ${response.status}`,
          'API_ERROR',
          response.status >= 500
        );
      }

      const data = await response.json();
      const executionTimeMs = Date.now() - startTime;

      return this.convertFromOpenAIFormat(data, executionTimeMs);
    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw LLMError
      }
      throw this.createError(
        `OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'API_ERROR',
        true
      );
    }
  }

  async generateWithTools(request: LLMToolRequest): Promise<LLMToolResponse> {
    this.validateRequest(request);
    await this.checkRateLimit();

    const startTime = Date.now();

    try {
      const openaiRequest = this.convertToOpenAIFormatWithTools(request);
      
      const response = await fetch(`${this.config.endpoint}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(openaiRequest),
      });

      // Update rate limit info from headers
      this.updateRateLimit(Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw this.createError(
          errorData.error?.message || `HTTP ${response.status}`,
          'API_ERROR',
          response.status >= 500
        );
      }

      const data = await response.json();
      const executionTimeMs = Date.now() - startTime;

      return this.convertFromOpenAIFormatWithTools(data, executionTimeMs);
    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw LLMError
      }
      throw this.createError(
        `OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'API_ERROR',
        true
      );
    }
  }

  private convertToOpenAIFormat(request: LLMRequest): any {
    return {
      model: request.model || this.config.model,
      messages: request.messages,
      temperature: request.temperature ?? this.config.temperature,
      max_tokens: request.maxTokens ?? this.config.maxTokens,
    };
  }

  private convertToOpenAIFormatWithTools(request: LLMToolRequest): any {
    const baseRequest = this.convertToOpenAIFormat(request);
    
    return {
      ...baseRequest,
      tools: request.tools,
      tool_choice: request.toolChoice || 'auto',
    };
  }

  private convertFromOpenAIFormat(data: any, executionTimeMs: number): LLMResponse {
    const choice = data.choices?.[0];
    const content = choice?.message?.content || '';
    
    const usage = {
      promptTokens: data.usage?.prompt_tokens || 0,
      completionTokens: data.usage?.completion_tokens || 0,
      totalTokens: data.usage?.total_tokens || 0,
    };

    return {
      content,
      usage,
      model: data.model || this.config.model,
      finishReason: choice?.finish_reason || 'stop',
      executionTimeMs,
    };
  }

  private convertFromOpenAIFormatWithTools(data: any, executionTimeMs: number): LLMToolResponse {
    const baseResponse = this.convertFromOpenAIFormat(data, executionTimeMs);
    
    const choice = data.choices?.[0];
    const toolCalls = choice?.message?.tool_calls;

    return {
      ...baseResponse,
      toolCalls,
    };
  }
}
