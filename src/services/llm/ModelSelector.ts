/**
 * Intelligent Model Selection Service
 * Selects the best LLM model based on task requirements and provider capabilities
 */

export type TaskType = 
  | 'function_calling'
  | 'reasoning'
  | 'text_generation'
  | 'vision'
  | 'analysis'
  | 'communication'
  | 'general';

export interface ModelCapabilities {
  functionCalling: boolean;
  reasoning: boolean;
  vision: boolean;
  multimodal: boolean;
  speed: 'fast' | 'medium' | 'slow';
  quality: 'high' | 'medium' | 'low';
  contextLength: number;
  costEfficiency: 'high' | 'medium' | 'low';
}

export interface ModelRecommendation {
  providerId: string;
  providerName: string;
  modelName: string;
  confidence: number;
  reasoning: string;
  fallbacks: string[];
}

export class ModelSelector {
  private static modelCapabilities: Record<string, ModelCapabilities> = {
    // Groq Models (using correct API model names)
    'meta-llama/llama-4-scout-17b-16e-instruct': {
      functionCalling: true,
      reasoning: true,
      vision: true,
      multimodal: true,
      speed: 'fast',
      quality: 'high',
      contextLength: 131072,
      costEfficiency: 'high',
    },
    'openai/gpt-oss-120b': {
      functionCalling: true,
      reasoning: true,
      vision: false,
      multimodal: false,
      speed: 'medium',
      quality: 'high',
      contextLength: 131072,
      costEfficiency: 'high',
    },
    'llama-3.3-70b-versatile': {
      functionCalling: false,
      reasoning: true,
      vision: false,
      multimodal: false,
      speed: 'fast',
      quality: 'high',
      contextLength: 131072,
      costEfficiency: 'high',
    },
    'qwen/qwen3-32b': {
      functionCalling: true,
      reasoning: true,
      vision: false,
      multimodal: false,
      speed: 'fast',
      quality: 'medium',
      contextLength: 131072,
      costEfficiency: 'high',
    },

    // Additional Groq Models
    'meta-llama/llama-4-maverick-17b-128e-instruct': {
      functionCalling: true,
      reasoning: true,
      vision: true,
      multimodal: true,
      speed: 'fast',
      quality: 'high',
      contextLength: 131072,
      costEfficiency: 'high',
    },
    'deepseek-r1-distill-llama-70b': {
      functionCalling: false,
      reasoning: true,
      vision: false,
      multimodal: false,
      speed: 'medium',
      quality: 'high',
      contextLength: 131072,
      costEfficiency: 'high',
    },
    'moonshotai/kimi-k2-instruct': {
      functionCalling: true,
      reasoning: true,
      vision: false,
      multimodal: false,
      speed: 'fast',
      quality: 'high',
      contextLength: 131072,
      costEfficiency: 'high',
    },
    'openai/gpt-oss-20b': {
      functionCalling: true,
      reasoning: true,
      vision: false,
      multimodal: false,
      speed: 'fast',
      quality: 'medium',
      contextLength: 131072,
      costEfficiency: 'high',
    },

    // Gemini Models
    'gemini-2.0-flash': {
      functionCalling: true,
      reasoning: true,
      vision: true,
      multimodal: true,
      speed: 'fast',
      quality: 'high',
      contextLength: 8000,
      costEfficiency: 'medium',
    },
    
    // OpenAI Models
    'gpt-4-turbo-preview': {
      functionCalling: true,
      reasoning: true,
      vision: false,
      multimodal: false,
      speed: 'medium',
      quality: 'high',
      contextLength: 4000,
      costEfficiency: 'low',
    },
  };

  private static taskPreferences: Record<TaskType, {
    requiresFunctionCalling?: boolean;
    prefersReasoning?: boolean;
    requiresVision?: boolean;
    speedImportance: 'high' | 'medium' | 'low';
    qualityImportance: 'high' | 'medium' | 'low';
  }> = {
    function_calling: {
      requiresFunctionCalling: true,
      speedImportance: 'high',
      qualityImportance: 'high',
    },
    reasoning: {
      prefersReasoning: true,
      speedImportance: 'medium',
      qualityImportance: 'high',
    },
    text_generation: {
      speedImportance: 'high',
      qualityImportance: 'medium',
    },
    vision: {
      requiresVision: true,
      speedImportance: 'medium',
      qualityImportance: 'high',
    },
    analysis: {
      prefersReasoning: true,
      speedImportance: 'medium',
      qualityImportance: 'high',
    },
    communication: {
      speedImportance: 'high',
      qualityImportance: 'high',
    },
    general: {
      speedImportance: 'medium',
      qualityImportance: 'medium',
    },
  };

  static selectBestModel(
    taskType: TaskType,
    availableProviders: Array<{
      id: string;
      name: string;
      modelName: string;
      isActive: boolean;
      priority: number;
    }>,
    options: {
      preferredProvider?: string;
      requiresFunctionCalling?: boolean;
      maxTokens?: number;
    } = {}
  ): ModelRecommendation | null {
    const taskPrefs = this.taskPreferences[taskType];
    const activeProviders = availableProviders.filter(p => p.isActive);
    
    if (activeProviders.length === 0) {
      return null;
    }

    // Score each provider
    const scoredProviders = activeProviders.map(provider => {
      const capabilities = this.modelCapabilities[provider.modelName];
      if (!capabilities) {
        return { provider, score: 0, reasoning: 'Unknown model capabilities' };
      }

      let score = 0;
      let reasoning: string[] = [];

      // Check hard requirements
      if (taskPrefs.requiresFunctionCalling && !capabilities.functionCalling) {
        return { provider, score: -1, reasoning: 'Does not support function calling' };
      }

      if (taskPrefs.requiresVision && !capabilities.vision) {
        return { provider, score: -1, reasoning: 'Does not support vision' };
      }

      if (options.requiresFunctionCalling && !capabilities.functionCalling) {
        return { provider, score: -1, reasoning: 'Function calling required but not supported' };
      }

      // Preferred provider bonus
      if (options.preferredProvider === provider.id) {
        score += 20;
        reasoning.push('Preferred provider');
      }

      // Function calling capability
      if (capabilities.functionCalling) {
        score += taskPrefs.requiresFunctionCalling ? 15 : 5;
        reasoning.push('Supports function calling');
      }

      // Reasoning capability
      if (capabilities.reasoning && taskPrefs.prefersReasoning) {
        score += 10;
        reasoning.push('Strong reasoning capabilities');
      }

      // Speed scoring
      const speedScore = capabilities.speed === 'fast' ? 10 : capabilities.speed === 'medium' ? 5 : 0;
      const speedWeight = taskPrefs.speedImportance === 'high' ? 1.5 : taskPrefs.speedImportance === 'medium' ? 1 : 0.5;
      score += speedScore * speedWeight;

      // Quality scoring
      const qualityScore = capabilities.quality === 'high' ? 10 : capabilities.quality === 'medium' ? 5 : 0;
      const qualityWeight = taskPrefs.qualityImportance === 'high' ? 1.5 : taskPrefs.qualityImportance === 'medium' ? 1 : 0.5;
      score += qualityScore * qualityWeight;

      // Cost efficiency bonus
      if (capabilities.costEfficiency === 'high') {
        score += 5;
        reasoning.push('Cost efficient');
      }

      // Priority bonus (lower priority number = higher priority)
      score += Math.max(0, 10 - provider.priority);

      // Context length consideration
      if (options.maxTokens && capabilities.contextLength >= options.maxTokens) {
        score += 5;
        reasoning.push('Sufficient context length');
      }

      return {
        provider,
        score,
        reasoning: reasoning.join(', '),
      };
    });

    // Filter out invalid providers and sort by score
    const validProviders = scoredProviders
      .filter(p => p.score >= 0)
      .sort((a, b) => b.score - a.score);

    if (validProviders.length === 0) {
      return null;
    }

    const best = validProviders[0];
    const fallbacks = validProviders.slice(1, 4).map(p => p.provider.name);

    return {
      providerId: best.provider.id,
      providerName: best.provider.name,
      modelName: best.provider.modelName,
      confidence: Math.min(best.score / 50, 1), // Normalize to 0-1
      reasoning: best.reasoning,
      fallbacks,
    };
  }

  static getTaskTypeFromAgentAction(agentAction: string): TaskType {
    if (agentAction.includes('tool_call') || agentAction.includes('function')) {
      return 'function_calling';
    }
    
    if (agentAction.includes('analysis') || agentAction.includes('analyze')) {
      return 'analysis';
    }
    
    if (agentAction.includes('reasoning') || agentAction.includes('decision')) {
      return 'reasoning';
    }
    
    if (agentAction.includes('communication') || agentAction.includes('message') || agentAction.includes('email')) {
      return 'communication';
    }
    
    if (agentAction.includes('vision') || agentAction.includes('image')) {
      return 'vision';
    }
    
    return 'general';
  }

  static getModelCapabilities(modelName: string): ModelCapabilities | null {
    return this.modelCapabilities[modelName] || null;
  }
}
