/**
 * Abstract base class for LLM providers
 */

import { 
  LLMProvider as ILLMProvider, 
  LLMRequest, 
  LLMResponse, 
  LLMToolRequest, 
  LLMToolResponse,
  LLMProviderConfig,
  LLMError,
  RateLimitInfo
} from './types';

export abstract class LL<PERSON>rovider implements ILLMProvider {
  protected config: LLMProviderConfig;
  protected rateLimitInfo: RateLimitInfo | null = null;
  protected lastRequestTime: Date | null = null;

  constructor(config: LLMProviderConfig) {
    this.config = config;
  }

  abstract get name(): string;
  abstract get type(): 'openai' | 'gemini' | 'llama' | 'anthropic' | 'groq';
  
  abstract isAvailable(): Promise<boolean>;
  abstract generateResponse(request: LLMRequest): Promise<LLMResponse>;
  abstract generateWithTools(request: LLMToolRequest): Promise<LLMToolResponse>;

  /**
   * Check if we're within rate limits
   */
  protected async checkRateLimit(): Promise<void> {
    if (!this.rateLimitInfo) return;

    const now = new Date();
    if (now < this.rateLimitInfo.resetTime && this.rateLimitInfo.requestsRemaining <= 0) {
      const waitTime = this.rateLimitInfo.resetTime.getTime() - now.getTime();
      throw this.createError(
        `Rate limit exceeded. Try again in ${Math.ceil(waitTime / 1000)} seconds`,
        'RATE_LIMIT_EXCEEDED',
        true
      );
    }
  }

  /**
   * Update rate limit information
   */
  protected updateRateLimit(headers: Record<string, string>): void {
    const remaining = parseInt(headers['x-ratelimit-remaining'] || '0');
    const resetTime = headers['x-ratelimit-reset'] 
      ? new Date(parseInt(headers['x-ratelimit-reset']) * 1000)
      : new Date(Date.now() + 60000); // Default to 1 minute from now

    this.rateLimitInfo = {
      requestsRemaining: remaining,
      resetTime,
      retryAfter: headers['retry-after'] ? parseInt(headers['retry-after']) : undefined
    };
  }

  /**
   * Create a standardized LLM error
   */
  protected createError(message: string, code: string, retryable: boolean = false): LLMError {
    const error = new Error(message) as LLMError;
    error.code = code;
    error.provider = this.name;
    error.retryable = retryable;
    error.rateLimited = code === 'RATE_LIMIT_EXCEEDED';
    return error;
  }

  /**
   * Validate request parameters
   */
  protected validateRequest(request: LLMRequest): void {
    if (!request.messages || request.messages.length === 0) {
      throw this.createError('Messages array cannot be empty', 'INVALID_REQUEST');
    }

    if (request.maxTokens && request.maxTokens > this.config.maxTokens) {
      throw this.createError(
        `Max tokens (${request.maxTokens}) exceeds provider limit (${this.config.maxTokens})`,
        'INVALID_REQUEST'
      );
    }

    if (request.temperature && (request.temperature < 0 || request.temperature > 2)) {
      throw this.createError('Temperature must be between 0 and 2', 'INVALID_REQUEST');
    }
  }

  /**
   * Get provider configuration
   */
  getConfig(): LLMProviderConfig {
    return { ...this.config };
  }

  /**
   * Update provider configuration
   */
  updateConfig(updates: Partial<LLMProviderConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  /**
   * Get current rate limit status
   */
  getRateLimitInfo(): RateLimitInfo | null {
    return this.rateLimitInfo ? { ...this.rateLimitInfo } : null;
  }

  /**
   * Check if provider is currently available
   */
  async healthCheck(): Promise<boolean> {
    try {
      return await this.isAvailable();
    } catch (error) {
      console.error(`Health check failed for ${this.name}:`, error);
      return false;
    }
  }
}
