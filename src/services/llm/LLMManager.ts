/**
 * LLM Manager - Handles multiple providers with fallback and load balancing
 */

import { supabase } from '@/integrations/supabase/client';
import { GeminiProvider } from './GeminiProvider';
import { OpenAIProvider } from './OpenAIProvider';
import { GroqProvider } from './GroqProvider';
import { LLMProvider } from './LLMProvider';
import { ModelSelector, TaskType } from './ModelSelector';
import { 
  LLMRequest, 
  LLMResponse, 
  LLMToolRequest, 
  LLMToolResponse,
  LLMProviderConfig,
  LLMError,
  LLMUsageStats
} from './types';

export class LLMManager {
  private providers: Map<string, LLMProvider> = new Map();
  private usageStats: Map<string, LLMUsageStats> = new Map();
  private initialized = false;

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load provider configurations from database
      const { data: configs, error } = await supabase
        .from('ai_providers')
        .select('*')
        .eq('is_active', true)
        .order('priority');

      if (error) {
        console.error('Failed to load AI provider configs:', error);
        return;
      }

      // Initialize providers
      for (const config of configs || []) {
        await this.initializeProvider(config);
      }

      this.initialized = true;
      console.log(`Initialized ${this.providers.size} LLM providers`);
    } catch (error) {
      console.error('Failed to initialize LLM Manager:', error);
    }
  }

  private async initializeProvider(config: any): Promise<void> {
    try {
      const providerConfig: LLMProviderConfig = {
        id: config.id,
        name: config.name,
        type: config.provider_type,
        apiKey: await this.getApiKey(config.provider_type),
        endpoint: config.endpoint_url,
        model: config.model_name,
        isActive: config.is_active,
        rateLimitPerMinute: config.rate_limit_per_minute,
        maxTokens: config.max_tokens,
        temperature: config.temperature,
        timeoutSeconds: config.timeout_seconds,
        priority: config.priority,
      };

      let provider: LLMProvider;

      switch (config.provider_type) {
        case 'gemini':
          provider = new GeminiProvider(providerConfig);
          break;
        case 'openai':
          provider = new OpenAIProvider(providerConfig);
          break;
        case 'groq':
          provider = new GroqProvider(providerConfig);
          break;
        default:
          console.warn(`Unsupported provider type: ${config.provider_type}`);
          return;
      }

      // Test provider availability
      const isAvailable = await provider.isAvailable();
      if (isAvailable) {
        this.providers.set(config.id, provider);
        this.initializeUsageStats(config.id);
        console.log(`Initialized provider: ${config.name}`);
      } else {
        console.warn(`Provider ${config.name} is not available`);
      }
    } catch (error) {
      console.error(`Failed to initialize provider ${config.name}:`, error);
    }
  }

  private async getApiKey(providerType: string): Promise<string> {
    // In a real implementation, these would be securely stored and encrypted
    // For now, we'll use environment variables
    switch (providerType) {
      case 'gemini':
        return import.meta.env.VITE_GEMINI_API_KEY || '';
      case 'openai':
        return import.meta.env.VITE_OPENAI_API_KEY || '';
      case 'groq':
        return import.meta.env.VITE_GROQ_API_KEY || '';
      default:
        return '';
    }
  }

  private initializeUsageStats(providerId: string): void {
    this.usageStats.set(providerId, {
      providerId,
      requestCount: 0,
      tokenCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      lastUsed: new Date(),
    });
  }

  async generateResponse(request: LLMRequest, preferredProviderId?: string, taskType?: TaskType): Promise<LLMResponse> {
    await this.initialize();

    const providers = this.getAvailableProviders(preferredProviderId, taskType, false);

    if (providers.length === 0) {
      throw new Error('No available LLM providers');
    }

    let lastError: LLMError | null = null;

    for (const provider of providers) {
      try {
        const response = await provider.generateResponse(request);
        this.updateUsageStats(provider.getConfig().id, response);
        return response;
      } catch (error) {
        lastError = error as LLMError;
        this.updateErrorStats(provider.getConfig().id);

        console.warn(`Provider ${provider.name} failed:`, error);

        // If error is not retryable, don't try other providers
        if (!lastError.retryable) {
          throw lastError;
        }
      }
    }

    throw lastError || new Error('All providers failed');
  }

  async generateWithTools(request: LLMToolRequest, preferredProviderId?: string): Promise<LLMToolResponse> {
    await this.initialize();

    // Function calling tasks require function calling capability
    const providers = this.getAvailableProviders(preferredProviderId, 'function_calling', true);

    if (providers.length === 0) {
      throw new Error('No available LLM providers with function calling support');
    }

    let lastError: LLMError | null = null;

    for (const provider of providers) {
      try {
        const response = await provider.generateWithTools(request);
        this.updateUsageStats(provider.getConfig().id, response);
        return response;
      } catch (error) {
        lastError = error as LLMError;
        this.updateErrorStats(provider.getConfig().id);

        console.warn(`Provider ${provider.name} failed:`, error);

        // If error is not retryable, don't try other providers
        if (!lastError.retryable) {
          throw lastError;
        }
      }
    }

    throw lastError || new Error('All providers failed');
  }

  private getAvailableProviders(
    preferredProviderId?: string,
    taskType?: TaskType,
    requiresFunctionCalling?: boolean
  ): LLMProvider[] {
    const allProviders = Array.from(this.providers.values());

    // If we have task type, use intelligent model selection
    if (taskType) {
      const providerConfigs = allProviders.map(p => ({
        id: p.getConfig().id,
        name: p.getConfig().name,
        modelName: p.getConfig().model,
        isActive: p.getConfig().isActive,
        priority: p.getConfig().priority,
      }));

      const recommendation = ModelSelector.selectBestModel(taskType, providerConfigs, {
        preferredProvider: preferredProviderId,
        requiresFunctionCalling,
      });

      if (recommendation) {
        // Reorder providers based on recommendation
        const recommendedProvider = this.providers.get(recommendation.providerId);
        if (recommendedProvider) {
          const others = allProviders.filter(p => p.getConfig().id !== recommendation.providerId);
          return [recommendedProvider, ...others.sort((a, b) => a.getConfig().priority - b.getConfig().priority)];
        }
      }
    }

    // Fallback to original logic
    if (preferredProviderId && this.providers.has(preferredProviderId)) {
      const preferred = this.providers.get(preferredProviderId)!;
      const others = allProviders.filter(p => p.getConfig().id !== preferredProviderId);
      return [preferred, ...others.sort((a, b) => a.getConfig().priority - b.getConfig().priority)];
    }

    return allProviders.sort((a, b) => a.getConfig().priority - b.getConfig().priority);
  }

  private updateUsageStats(providerId: string, response: LLMResponse): void {
    const stats = this.usageStats.get(providerId);
    if (!stats) return;

    stats.requestCount++;
    stats.tokenCount += response.usage.totalTokens;
    stats.averageResponseTime = (stats.averageResponseTime * (stats.requestCount - 1) + response.executionTimeMs) / stats.requestCount;
    stats.lastUsed = new Date();
  }

  private updateErrorStats(providerId: string): void {
    const stats = this.usageStats.get(providerId);
    if (!stats) return;

    stats.errorCount++;
  }

  getUsageStats(): LLMUsageStats[] {
    return Array.from(this.usageStats.values());
  }

  getProviderStatus(): Array<{ id: string; name: string; available: boolean; stats: LLMUsageStats }> {
    return Array.from(this.providers.entries()).map(([id, provider]) => ({
      id,
      name: provider.name,
      available: true, // Could add real-time health check
      stats: this.usageStats.get(id)!,
    }));
  }

  async refreshProviders(): Promise<void> {
    this.initialized = false;
    this.providers.clear();
    this.usageStats.clear();
    await this.initialize();
  }
}

// Singleton instance
export const llmManager = new LLMManager();
