/**
 * Groq LLM Provider Implementation
 */

import { LLMProvider } from './LLMProvider';
import { 
  LLMRequest, 
  LLMResponse, 
  LLMToolRequest, 
  LLMToolResponse,
  LLMProviderConfig
} from './types';

export class GroqProvider extends LLMProvider {
  private apiKey: string;

  constructor(config: LLMProviderConfig) {
    super(config);
    this.apiKey = config.apiKey || import.meta.env.VITE_GROQ_API_KEY || '';
    
    if (!this.apiKey) {
      throw new Error('Groq API key is required');
    }
  }

  get name(): string {
    return this.config.name;
  }

  get type(): 'groq' {
    return 'groq';
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiKey) {
      console.warn('Groq API key not configured');
      return false;
    }

    try {
      // Test API availability with a simple request
      const response = await fetch('https://api.groq.com/openai/v1/models', {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      return response.ok;
    } catch (error) {
      console.error('Groq availability check failed:', error);
      return false;
    }
  }

  async generateResponse(request: LLMRequest): Promise<LLMResponse> {
    this.validateRequest(request);
    await this.checkRateLimit();

    const startTime = Date.now();

    try {
      const groqRequest = this.convertToGroqFormat(request);
      
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(groqRequest),
      });

      // Update rate limit info from headers
      this.updateRateLimit(Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw this.createError(
          errorData.error?.message || `HTTP ${response.status}`,
          'API_ERROR',
          response.status >= 500
        );
      }

      const data = await response.json();
      const executionTimeMs = Date.now() - startTime;

      return this.convertFromGroqFormat(data, executionTimeMs);
    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw LLMError
      }
      throw this.createError(
        `Groq API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'API_ERROR',
        true
      );
    }
  }

  async generateWithTools(request: LLMToolRequest): Promise<LLMToolResponse> {
    this.validateRequest(request);
    await this.checkRateLimit();

    const startTime = Date.now();

    try {
      const groqRequest = this.convertToGroqFormatWithTools(request);
      
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(groqRequest),
      });

      // Update rate limit info from headers
      this.updateRateLimit(Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw this.createError(
          errorData.error?.message || `HTTP ${response.status}`,
          'API_ERROR',
          response.status >= 500
        );
      }

      const data = await response.json();
      const executionTimeMs = Date.now() - startTime;

      return this.convertFromGroqFormatWithTools(data, executionTimeMs);
    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw LLMError
      }
      throw this.createError(
        `Groq API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'API_ERROR',
        true
      );
    }
  }

  private convertToGroqFormat(request: LLMRequest): any {
    return {
      model: request.model || this.config.model,
      messages: request.messages,
      temperature: request.temperature ?? this.config.temperature,
      max_tokens: request.maxTokens ?? this.config.maxTokens,
      stream: false,
    };
  }

  private convertToGroqFormatWithTools(request: LLMToolRequest): any {
    const baseRequest = this.convertToGroqFormat(request);
    
    return {
      ...baseRequest,
      tools: request.tools,
      tool_choice: request.toolChoice || 'auto',
    };
  }

  private convertFromGroqFormat(data: any, executionTimeMs: number): LLMResponse {
    const choice = data.choices?.[0];
    const content = choice?.message?.content || '';
    
    const usage = {
      promptTokens: data.usage?.prompt_tokens || 0,
      completionTokens: data.usage?.completion_tokens || 0,
      totalTokens: data.usage?.total_tokens || 0,
    };

    return {
      content,
      usage,
      model: data.model || this.config.model,
      finishReason: choice?.finish_reason || 'stop',
      executionTimeMs,
    };
  }

  private convertFromGroqFormatWithTools(data: any, executionTimeMs: number): LLMToolResponse {
    const baseResponse = this.convertFromGroqFormat(data, executionTimeMs);
    
    const choice = data.choices?.[0];
    const toolCalls = choice?.message?.tool_calls;

    return {
      ...baseResponse,
      toolCalls,
    };
  }
}
