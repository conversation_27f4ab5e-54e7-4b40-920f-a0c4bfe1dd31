/**
 * Intelligent Decision Engine - Combines AI analysis with human oversight, risk assessment, and approval workflows
 */

import { llmManager } from '@/services/llm/LLMManager';
import { RiskAssessmentEngine } from '@/services/risk/RiskAssessmentEngine';
import { AdvancedAuditTrail } from '@/services/audit/AdvancedAuditTrail';
import { supabase } from '@/integrations/supabase/client';

export interface DecisionContext {
  workflowId: string;
  executionId: string;
  nodeId: string;
  userId: string;
  entityType: 'candidate' | 'job' | 'communication' | 'workflow';
  entityId: string;
  data: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface DecisionCriteria {
  riskThreshold: number; // 0-100, higher means more risk tolerance
  confidenceThreshold: number; // 0-1, minimum confidence for autonomous decision
  requiresApproval: boolean;
  approvalType: 'single' | 'multi' | 'consensus';
  approvers: string[];
  escalationRules: EscalationRule[];
  timeoutMinutes?: number;
}

export interface EscalationRule {
  condition: string; // e.g., "riskScore > 80" or "confidence < 0.7"
  action: 'require_approval' | 'escalate_to_manager' | 'pause_workflow' | 'notify_admin';
  target?: string; // user ID or role for escalation
}

export interface DecisionResult {
  decision: 'approve' | 'reject' | 'escalate' | 'require_approval';
  confidence: number;
  riskScore: number;
  reasoning: string;
  approvalRequired: boolean;
  approvalId?: string;
  escalationReason?: string;
  recommendedActions: string[];
  auditEventId: string;
}

export class IntelligentDecisionEngine {
  private static instance: IntelligentDecisionEngine;
  private riskEngine: RiskAssessmentEngine;
  private auditTrail: AdvancedAuditTrail;

  private constructor() {
    this.riskEngine = RiskAssessmentEngine.getInstance();
    this.auditTrail = AdvancedAuditTrail.getInstance();
  }

  public static getInstance(): IntelligentDecisionEngine {
    if (!IntelligentDecisionEngine.instance) {
      IntelligentDecisionEngine.instance = new IntelligentDecisionEngine();
    }
    return IntelligentDecisionEngine.instance;
  }

  /**
   * Make an intelligent decision combining AI analysis, risk assessment, and approval workflows
   */
  async makeDecision(
    context: DecisionContext,
    criteria: DecisionCriteria,
    aiAnalysis?: any
  ): Promise<DecisionResult> {
    try {
      // Step 1: Perform AI analysis if not provided
      const analysis = aiAnalysis || await this.performAIAnalysis(context);
      
      // Step 2: Assess risk
      const riskAssessment = await this.riskEngine.assessRisk({
        entityType: context.entityType,
        entityId: context.entityId,
        context: context.data,
        metadata: context.metadata || {},
      });

      // Step 3: Make initial decision based on AI analysis and risk
      const initialDecision = this.evaluateInitialDecision(analysis, riskAssessment, criteria);

      // Step 4: Check escalation rules
      const escalationResult = this.checkEscalationRules(
        initialDecision,
        riskAssessment,
        analysis,
        criteria.escalationRules
      );

      // Step 5: Determine if approval is required
      const approvalRequired = this.shouldRequireApproval(
        initialDecision,
        riskAssessment,
        criteria,
        escalationResult
      );

      // Step 6: Create approval request if needed
      let approvalId: string | undefined;
      if (approvalRequired) {
        approvalId = await this.createApprovalRequest(context, criteria, analysis, riskAssessment);
      }

      // Step 7: Generate final decision result
      const decisionResult: DecisionResult = {
        decision: approvalRequired ? 'require_approval' : 
                 escalationResult.shouldEscalate ? 'escalate' : 
                 initialDecision.decision,
        confidence: analysis.confidence || 0.8,
        riskScore: riskAssessment.riskScore * 100, // Convert 0-1 to 0-100
        reasoning: this.generateReasoning(analysis, riskAssessment, escalationResult),
        approvalRequired,
        approvalId,
        escalationReason: escalationResult.reason,
        recommendedActions: this.generateRecommendedActions(analysis, riskAssessment),
        auditEventId: '', // Will be set after audit logging
      };

      // Step 8: Log decision to audit trail
      const auditEventId = await this.logDecisionToAudit(context, decisionResult, analysis, riskAssessment);
      decisionResult.auditEventId = auditEventId;

      return decisionResult;

    } catch (error) {
      console.error('Error in intelligent decision making:', error);
      
      // Fallback to safe decision
      const fallbackResult: DecisionResult = {
        decision: 'require_approval',
        confidence: 0,
        riskScore: 100,
        reasoning: `Error occurred during decision making: ${error}. Defaulting to human approval for safety.`,
        approvalRequired: true,
        recommendedActions: ['Review manually', 'Check system logs'],
        auditEventId: '',
      };

      // Log error to audit trail
      fallbackResult.auditEventId = await this.logDecisionToAudit(context, fallbackResult, null, null);
      
      return fallbackResult;
    }
  }

  /**
   * Perform AI analysis of the decision context
   */
  private async performAIAnalysis(context: DecisionContext): Promise<any> {
    const prompt = this.createAnalysisPrompt(context);
    
    const response = await llmManager.generateResponse({
      messages: [
        {
          role: 'system',
          content: this.getAnalysisSystemPrompt()
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      maxTokens: 2000,
    }, undefined, 'reasoning');

    return this.parseAnalysisResponse(response.content);
  }

  /**
   * Evaluate initial decision based on AI analysis and risk assessment
   */
  private evaluateInitialDecision(
    analysis: any,
    riskAssessment: any,
    criteria: DecisionCriteria
  ): { decision: 'approve' | 'reject'; confidence: number } {
    const confidence = analysis.confidence || 0.5;
    const riskScore = (riskAssessment.riskScore * 100) || 50;

    // Decision logic based on confidence and risk
    if (confidence >= criteria.confidenceThreshold && riskScore <= criteria.riskThreshold) {
      return { decision: 'approve', confidence };
    } else if (confidence < 0.3 || riskScore > 90) {
      return { decision: 'reject', confidence };
    } else {
      return { decision: 'approve', confidence }; // Default to approve but may require approval
    }
  }

  /**
   * Check if any escalation rules are triggered
   */
  private checkEscalationRules(
    initialDecision: any,
    riskAssessment: any,
    analysis: any,
    escalationRules: EscalationRule[]
  ): { shouldEscalate: boolean; reason?: string; action?: string } {
    for (const rule of escalationRules) {
      if (this.evaluateCondition(rule.condition, { 
        riskScore: riskAssessment.riskScore * 100,
        confidence: analysis.confidence,
        decision: initialDecision.decision 
      })) {
        return {
          shouldEscalate: true,
          reason: `Escalation rule triggered: ${rule.condition}`,
          action: rule.action
        };
      }
    }
    return { shouldEscalate: false };
  }

  /**
   * Determine if human approval is required
   */
  private shouldRequireApproval(
    initialDecision: any,
    riskAssessment: any,
    criteria: DecisionCriteria,
    escalationResult: any
  ): boolean {
    return criteria.requiresApproval || 
           escalationResult.shouldEscalate ||
           (riskAssessment.riskScore * 100) > criteria.riskThreshold ||
           initialDecision.confidence < criteria.confidenceThreshold;
  }

  /**
   * Create approval request in the database
   */
  private async createApprovalRequest(
    context: DecisionContext,
    criteria: DecisionCriteria,
    analysis: any,
    riskAssessment: any
  ): Promise<string> {
    const approvalMessage = this.generateApprovalMessage(context, analysis, riskAssessment);
    
    const { data, error } = await supabase
      .from('workflow_approvals')
      .insert({
        workflow_id: context.workflowId,
        execution_id: context.executionId,
        node_id: context.nodeId,
        requester_id: context.userId,
        approvers: criteria.approvers,
        approval_type: criteria.approvalType,
        approval_message: approvalMessage,
        context_data: {
          entityType: context.entityType,
          entityId: context.entityId,
          aiAnalysis: analysis,
          riskAssessment: riskAssessment,
          originalData: context.data,
        },
        priority: (riskAssessment.riskScore * 100) > 80 ? 'high' :
                 (riskAssessment.riskScore * 100) > 60 ? 'medium' : 'low',
        timeout_at: criteria.timeoutMinutes ? 
          new Date(Date.now() + criteria.timeoutMinutes * 60 * 1000) : null,
        escalation_at: criteria.timeoutMinutes ? 
          new Date(Date.now() + (criteria.timeoutMinutes * 0.75) * 60 * 1000) : null,
      })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create approval request: ${error.message}`);
    }

    return data.id;
  }

  /**
   * Generate reasoning for the decision
   */
  private generateReasoning(analysis: any, riskAssessment: any, escalationResult: any): string {
    const parts = [];
    
    if (analysis.reasoning) {
      parts.push(`AI Analysis: ${analysis.reasoning}`);
    }
    
    parts.push(`Risk Score: ${(riskAssessment.riskScore * 100).toFixed(1)}/100`);
    
    if (riskAssessment.riskFactors && riskAssessment.riskFactors.length > 0) {
      parts.push(`Risk Factors: ${riskAssessment.riskFactors.join(', ')}`);
    }
    
    if (escalationResult.shouldEscalate) {
      parts.push(`Escalation: ${escalationResult.reason}`);
    }
    
    return parts.join('. ');
  }

  /**
   * Generate recommended actions based on analysis and risk
   */
  private generateRecommendedActions(analysis: any, riskAssessment: any): string[] {
    const actions = [];
    
    if ((riskAssessment.riskScore * 100) > 70) {
      actions.push('Review risk factors carefully');
      actions.push('Consider additional verification steps');
    }
    
    if (analysis.confidence < 0.7) {
      actions.push('Gather additional information');
      actions.push('Consult with subject matter expert');
    }
    
    if (riskAssessment.riskFactors?.includes('data_quality')) {
      actions.push('Verify data accuracy and completeness');
    }
    
    if (actions.length === 0) {
      actions.push('Proceed with standard process');
    }
    
    return actions;
  }

  /**
   * Log decision to audit trail
   */
  private async logDecisionToAudit(
    context: DecisionContext,
    decision: DecisionResult,
    analysis: any,
    riskAssessment: any
  ): Promise<string> {
    const auditEventId = await this.auditTrail.logEvent({
      eventType: 'decision',
      entityType: context.entityType,
      entityId: context.entityId,
      userId: context.userId,
      workflowId: context.workflowId,
      executionId: context.executionId,
      nodeId: context.nodeId,
      decisionPoint: {
        nodeType: 'intelligent_decision',
        condition: `riskScore: ${decision.riskScore}, confidence: ${decision.confidence}`,
        result: decision.decision === 'approve',
        confidence: decision.confidence,
        reasoning: decision.reasoning,
      },
      context: context.data,
      metadata: {
        timestamp: new Date(),
        success: true,
      },
      compliance: {
        sensitivityLevel: decision.riskScore > 70 ? 'high' : 'medium',
        gdprRelevant: context.entityType === 'candidate',
        retentionPeriod: 2555, // 7 years
      },
    });

    return auditEventId;
  }

  // Helper methods
  private createAnalysisPrompt(context: DecisionContext): string {
    return `Analyze the following ${context.entityType} data and provide a decision recommendation:

Entity Type: ${context.entityType}
Entity ID: ${context.entityId}
Context Data: ${JSON.stringify(context.data, null, 2)}

Please provide:
1. A recommendation (approve/reject)
2. Confidence level (0-1)
3. Key factors that influenced your decision
4. Any concerns or risks identified
5. Reasoning for your recommendation

Format your response as JSON with the following structure:
{
  "recommendation": "approve|reject",
  "confidence": 0.85,
  "keyFactors": ["factor1", "factor2"],
  "concerns": ["concern1", "concern2"],
  "reasoning": "Detailed explanation of the decision"
}`;
  }

  private getAnalysisSystemPrompt(): string {
    return `You are an intelligent decision analysis system for a recruitment management platform. Your role is to analyze data and provide recommendations for hiring decisions, candidate communications, and workflow optimizations.

Key principles:
1. Be thorough but concise in your analysis
2. Consider both positive and negative factors
3. Provide clear reasoning for your recommendations
4. Identify potential risks or concerns
5. Be honest about confidence levels
6. Focus on objective criteria when possible

Always respond in valid JSON format as specified in the user prompt.`;
  }

  private parseAnalysisResponse(response: string): any {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback parsing if JSON is not found
      return {
        recommendation: response.toLowerCase().includes('approve') ? 'approve' : 'reject',
        confidence: 0.5,
        keyFactors: ['AI analysis completed'],
        concerns: ['Unable to parse detailed analysis'],
        reasoning: response.substring(0, 500),
      };
    } catch (error) {
      console.error('Error parsing AI analysis response:', error);
      return {
        recommendation: 'reject',
        confidence: 0.3,
        keyFactors: ['Parsing error'],
        concerns: ['Unable to analyze properly'],
        reasoning: 'Error occurred during analysis parsing',
      };
    }
  }

  private generateApprovalMessage(
    context: DecisionContext,
    analysis: any,
    riskAssessment: any
  ): string {
    return `Approval required for ${context.entityType} decision (ID: ${context.entityId}).

AI Analysis: ${analysis.reasoning || 'Analysis completed'}
Risk Score: ${(riskAssessment.riskScore * 100).toFixed(1)}/100
Confidence: ${(analysis.confidence * 100).toFixed(1)}%

Please review the context data and provide your approval decision.`;
  }

  private evaluateCondition(condition: string, variables: Record<string, any>): boolean {
    try {
      // Simple condition evaluation (in production, use a proper expression evaluator)
      const normalizedCondition = condition
        .replace(/riskScore/g, variables.riskScore?.toString() || '0')
        .replace(/confidence/g, variables.confidence?.toString() || '0')
        .replace(/decision/g, `"${variables.decision}"`);
      
      // Basic evaluation for common patterns
      if (normalizedCondition.includes('>')) {
        const [left, right] = normalizedCondition.split('>').map(s => s.trim());
        return parseFloat(left) > parseFloat(right);
      }
      if (normalizedCondition.includes('<')) {
        const [left, right] = normalizedCondition.split('<').map(s => s.trim());
        return parseFloat(left) < parseFloat(right);
      }
      if (normalizedCondition.includes('==')) {
        const [left, right] = normalizedCondition.split('==').map(s => s.trim());
        return left === right;
      }
      
      return false;
    } catch (error) {
      console.error('Error evaluating condition:', condition, error);
      return false;
    }
  }
}

export const intelligentDecisionEngine = IntelligentDecisionEngine.getInstance();
