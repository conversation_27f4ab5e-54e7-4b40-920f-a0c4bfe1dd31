import { supabase } from "@/integrations/supabase/client";
import { workflowLogger, LogLevel } from "@/services/WorkflowLogger";

export interface AuditEvent {
  id: string;
  eventType: 'decision' | 'approval' | 'escalation' | 'override' | 'execution' | 'error';
  entityType: 'workflow' | 'candidate' | 'job' | 'communication' | 'approval';
  entityId: string;
  userId: string;
  workflowId?: string;
  executionId?: string;
  nodeId?: string;
  
  // Decision tracking
  decisionPoint?: {
    nodeType: string;
    condition: string;
    result: boolean;
    confidence?: number;
    aiProvider?: string;
    reasoning?: string;
  };
  
  // Approval tracking
  approvalChain?: {
    requestId: string;
    approverLevel: number;
    decision: 'approve' | 'reject' | 'escalate';
    comment?: string;
    timeToDecision?: number; // milliseconds
  };
  
  // Human override tracking
  humanOverride?: {
    originalDecision: any;
    newDecision: any;
    reason: string;
    overrideType: 'manual' | 'escalation' | 'correction';
  };
  
  // Context and metadata
  context: Record<string, any>;
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
    timestamp: Date;
    duration?: number;
    success: boolean;
    errorMessage?: string;
  };
  
  // Compliance fields
  compliance: {
    dataProcessed?: string[];
    legalBasis?: string;
    retentionPeriod?: number; // days
    sensitivityLevel: 'low' | 'medium' | 'high' | 'critical';
    gdprRelevant: boolean;
  };
  
  createdAt: Date;
}

export interface AuditQuery {
  entityType?: string;
  entityId?: string;
  userId?: string;
  workflowId?: string;
  eventType?: string;
  dateFrom?: Date;
  dateTo?: Date;
  sensitivityLevel?: string;
  limit?: number;
  offset?: number;
}

export interface ComplianceReport {
  reportId: string;
  reportType: 'gdpr' | 'audit' | 'decision_log' | 'approval_chain';
  generatedAt: Date;
  generatedBy: string;
  period: {
    from: Date;
    to: Date;
  };
  summary: {
    totalEvents: number;
    decisionEvents: number;
    approvalEvents: number;
    humanOverrides: number;
    errorEvents: number;
    gdprEvents: number;
  };
  data: AuditEvent[];
  metadata: Record<string, any>;
}

export class AdvancedAuditTrail {
  private static instance: AdvancedAuditTrail;
  
  public static getInstance(): AdvancedAuditTrail {
    if (!AdvancedAuditTrail.instance) {
      AdvancedAuditTrail.instance = new AdvancedAuditTrail();
    }
    return AdvancedAuditTrail.instance;
  }

  async logEvent(event: Omit<AuditEvent, 'id' | 'createdAt'>): Promise<string> {
    try {
      const auditEvent: AuditEvent = {
        ...event,
        id: crypto.randomUUID(),
        createdAt: new Date(),
      };

      // Store in database
      const { data, error } = await supabase
        .from('audit_events')
        .insert({
          id: auditEvent.id,
          event_type: auditEvent.eventType,
          entity_type: auditEvent.entityType,
          entity_id: auditEvent.entityId,
          user_id: auditEvent.userId,
          workflow_id: auditEvent.workflowId,
          execution_id: auditEvent.executionId,
          node_id: auditEvent.nodeId,
          decision_point: auditEvent.decisionPoint,
          approval_chain: auditEvent.approvalChain,
          human_override: auditEvent.humanOverride,
          context: auditEvent.context,
          metadata: auditEvent.metadata,
          compliance: auditEvent.compliance,
          created_at: auditEvent.createdAt.toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      // Also log to workflow logger for immediate access
      await workflowLogger.log({
        level: LogLevel.INFO,
        message: `Audit event: ${auditEvent.eventType}`,
        workflowId: auditEvent.workflowId,
        executionId: auditEvent.executionId,
        nodeId: auditEvent.nodeId,
        metadata: {
          auditEventId: auditEvent.id,
          entityType: auditEvent.entityType,
          entityId: auditEvent.entityId,
        },
      });

      return auditEvent.id;
    } catch (error) {
      console.error('Failed to log audit event:', error);
      throw error;
    }
  }

  async logDecision(params: {
    workflowId: string;
    executionId: string;
    nodeId: string;
    userId: string;
    entityType: string;
    entityId: string;
    nodeType: string;
    condition: string;
    result: boolean;
    confidence?: number;
    aiProvider?: string;
    reasoning?: string;
    context?: Record<string, any>;
  }): Promise<string> {
    return this.logEvent({
      eventType: 'decision',
      entityType: params.entityType as any,
      entityId: params.entityId,
      userId: params.userId,
      workflowId: params.workflowId,
      executionId: params.executionId,
      nodeId: params.nodeId,
      decisionPoint: {
        nodeType: params.nodeType,
        condition: params.condition,
        result: params.result,
        confidence: params.confidence,
        aiProvider: params.aiProvider,
        reasoning: params.reasoning,
      },
      context: params.context || {},
      metadata: {
        timestamp: new Date(),
        success: true,
      },
      compliance: {
        sensitivityLevel: 'medium',
        gdprRelevant: true,
        retentionPeriod: 2555, // 7 years
      },
    });
  }

  async logApproval(params: {
    requestId: string;
    workflowId: string;
    executionId: string;
    nodeId: string;
    userId: string;
    entityType: string;
    entityId: string;
    approverLevel: number;
    decision: 'approve' | 'reject' | 'escalate';
    comment?: string;
    timeToDecision?: number;
    context?: Record<string, any>;
  }): Promise<string> {
    return this.logEvent({
      eventType: 'approval',
      entityType: params.entityType as any,
      entityId: params.entityId,
      userId: params.userId,
      workflowId: params.workflowId,
      executionId: params.executionId,
      nodeId: params.nodeId,
      approvalChain: {
        requestId: params.requestId,
        approverLevel: params.approverLevel,
        decision: params.decision,
        comment: params.comment,
        timeToDecision: params.timeToDecision,
      },
      context: params.context || {},
      metadata: {
        timestamp: new Date(),
        success: true,
      },
      compliance: {
        sensitivityLevel: 'high',
        gdprRelevant: true,
        retentionPeriod: 2555, // 7 years
      },
    });
  }

  async logHumanOverride(params: {
    workflowId: string;
    executionId: string;
    nodeId: string;
    userId: string;
    entityType: string;
    entityId: string;
    originalDecision: any;
    newDecision: any;
    reason: string;
    overrideType: 'manual' | 'escalation' | 'correction';
    context?: Record<string, any>;
  }): Promise<string> {
    return this.logEvent({
      eventType: 'override',
      entityType: params.entityType as any,
      entityId: params.entityId,
      userId: params.userId,
      workflowId: params.workflowId,
      executionId: params.executionId,
      nodeId: params.nodeId,
      humanOverride: {
        originalDecision: params.originalDecision,
        newDecision: params.newDecision,
        reason: params.reason,
        overrideType: params.overrideType,
      },
      context: params.context || {},
      metadata: {
        timestamp: new Date(),
        success: true,
      },
      compliance: {
        sensitivityLevel: 'critical',
        gdprRelevant: true,
        retentionPeriod: 2555, // 7 years
      },
    });
  }

  async queryEvents(query: AuditQuery): Promise<AuditEvent[]> {
    try {
      let supabaseQuery = supabase
        .from('audit_events')
        .select('*')
        .order('created_at', { ascending: false });

      if (query.entityType) {
        supabaseQuery = supabaseQuery.eq('entity_type', query.entityType);
      }
      if (query.entityId) {
        supabaseQuery = supabaseQuery.eq('entity_id', query.entityId);
      }
      if (query.userId) {
        supabaseQuery = supabaseQuery.eq('user_id', query.userId);
      }
      if (query.workflowId) {
        supabaseQuery = supabaseQuery.eq('workflow_id', query.workflowId);
      }
      if (query.eventType) {
        supabaseQuery = supabaseQuery.eq('event_type', query.eventType);
      }
      if (query.dateFrom) {
        supabaseQuery = supabaseQuery.gte('created_at', query.dateFrom.toISOString());
      }
      if (query.dateTo) {
        supabaseQuery = supabaseQuery.lte('created_at', query.dateTo.toISOString());
      }
      if (query.sensitivityLevel) {
        supabaseQuery = supabaseQuery.eq('compliance->>sensitivityLevel', query.sensitivityLevel);
      }

      if (query.limit) {
        supabaseQuery = supabaseQuery.limit(query.limit);
      }
      if (query.offset) {
        supabaseQuery = supabaseQuery.range(query.offset, query.offset + (query.limit || 100) - 1);
      }

      const { data, error } = await supabaseQuery;
      if (error) throw error;

      return data?.map(this.mapDatabaseToAuditEvent) || [];
    } catch (error) {
      console.error('Failed to query audit events:', error);
      throw error;
    }
  }

  async generateComplianceReport(
    reportType: ComplianceReport['reportType'],
    period: { from: Date; to: Date },
    userId: string
  ): Promise<ComplianceReport> {
    try {
      const events = await this.queryEvents({
        dateFrom: period.from,
        dateTo: period.to,
        limit: 10000, // Large limit for comprehensive reports
      });

      const summary = {
        totalEvents: events.length,
        decisionEvents: events.filter(e => e.eventType === 'decision').length,
        approvalEvents: events.filter(e => e.eventType === 'approval').length,
        humanOverrides: events.filter(e => e.eventType === 'override').length,
        errorEvents: events.filter(e => e.eventType === 'error').length,
        gdprEvents: events.filter(e => e.compliance.gdprRelevant).length,
      };

      const report: ComplianceReport = {
        reportId: crypto.randomUUID(),
        reportType,
        generatedAt: new Date(),
        generatedBy: userId,
        period,
        summary,
        data: events,
        metadata: {
          generationDuration: 0, // Will be calculated
          filters: {},
          version: '1.0',
        },
      };

      // Store report in database for future reference
      await supabase.from('compliance_reports').insert({
        id: report.reportId,
        report_type: report.reportType,
        generated_by: report.generatedBy,
        period_from: report.period.from.toISOString(),
        period_to: report.period.to.toISOString(),
        summary: report.summary,
        metadata: report.metadata,
        created_at: report.generatedAt.toISOString(),
      });

      return report;
    } catch (error) {
      console.error('Failed to generate compliance report:', error);
      throw error;
    }
  }

  private mapDatabaseToAuditEvent(dbRecord: any): AuditEvent {
    return {
      id: dbRecord.id,
      eventType: dbRecord.event_type,
      entityType: dbRecord.entity_type,
      entityId: dbRecord.entity_id,
      userId: dbRecord.user_id,
      workflowId: dbRecord.workflow_id,
      executionId: dbRecord.execution_id,
      nodeId: dbRecord.node_id,
      decisionPoint: dbRecord.decision_point,
      approvalChain: dbRecord.approval_chain,
      humanOverride: dbRecord.human_override,
      context: dbRecord.context || {},
      metadata: dbRecord.metadata || {},
      compliance: dbRecord.compliance || {},
      createdAt: new Date(dbRecord.created_at),
    };
  }
}
