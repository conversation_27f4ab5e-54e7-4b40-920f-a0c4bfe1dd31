/**
 * Types for agent tool calling system
 */

export interface AgentTool {
  id: string;
  name: string;
  description: string;
  functionSchema: {
    name: string;
    description: string;
    parameters: Record<string, any>;
  };
  securityLevel: 'safe' | 'restricted' | 'dangerous';
  category: 'general' | 'database' | 'communication' | 'analysis' | 'integration';
  requiresApproval: boolean;
  rateLimitPerHour: number;
  isActive: boolean;
}

export interface ToolCallRequest {
  toolName: string;
  arguments: Record<string, any>;
  requestId: string;
  workflowId?: string;
  userId: string;
  context?: Record<string, any>;
}

export interface ToolCallResponse {
  requestId: string;
  success: boolean;
  result?: any;
  error?: string;
  executionTimeMs: number;
  tokensUsed?: number;
  requiresHumanApproval?: boolean;
  approvalRequestId?: string;
}

export interface ToolExecutionContext {
  userId: string;
  workflowId?: string;
  executionId?: string;
  permissions: string[];
  rateLimits: Map<string, RateLimitStatus>;
  securityLevel: 'safe' | 'restricted' | 'dangerous';
}

export interface RateLimitStatus {
  toolName: string;
  requestCount: number;
  windowStart: Date;
  windowDurationMs: number;
  maxRequests: number;
}

export interface ToolApprovalRequest {
  id: string;
  toolName: string;
  arguments: Record<string, any>;
  requestedBy: string;
  workflowId?: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedAt?: Date;
  expiresAt: Date;
  createdAt: Date;
}

export interface AgentExecutionLog {
  id: string;
  workflowId?: string;
  executionId?: string;
  agentAction: string;
  toolName?: string;
  inputData?: Record<string, any>;
  outputData?: Record<string, any>;
  llmProvider?: string;
  llmModel?: string;
  executionTimeMs: number;
  tokensUsed?: number;
  costEstimate?: number;
  success: boolean;
  errorMessage?: string;
  errorCode?: string;
  humanOverride: boolean;
  overrideReason?: string;
  confidenceScore?: number;
  createdBy: string;
  createdAt: Date;
}

export interface ToolHandler {
  name: string;
  execute(request: ToolCallRequest, context: ToolExecutionContext): Promise<ToolCallResponse>;
  validate(args: Record<string, any>): boolean;
  getRequiredPermissions(): string[];
}

export interface SecurityPolicy {
  allowedTools: string[];
  maxExecutionTime: number;
  maxTokensPerRequest: number;
  requireApprovalFor: string[];
  rateLimits: Record<string, { requests: number; windowMs: number }>;
}

export interface AgentCapabilities {
  availableTools: AgentTool[];
  securityPolicy: SecurityPolicy;
  permissions: string[];
  autonomyLevel: 'supervised' | 'semi-autonomous' | 'autonomous';
}
