/**
 * Agent Logger - Comprehensive logging system for agent actions and decisions
 */

import { supabase } from '@/integrations/supabase/client';
import { AgentExecutionLog } from './types';

export interface LogEntry {
  workflowId?: string;
  executionId?: string;
  agentAction: string;
  toolName?: string;
  inputData?: Record<string, any>;
  outputData?: Record<string, any>;
  llmProvider?: string;
  llmModel?: string;
  executionTimeMs: number;
  tokensUsed?: number;
  costEstimate?: number;
  success: boolean;
  errorMessage?: string;
  errorCode?: string;
  humanOverride?: boolean;
  overrideReason?: string;
  confidenceScore?: number;
  createdBy: string;
  metadata?: Record<string, any>;
}

export interface LogQuery {
  workflowId?: string;
  executionId?: string;
  userId?: string;
  agentAction?: string;
  toolName?: string;
  success?: boolean;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export interface LogAnalytics {
  totalExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  totalTokensUsed: number;
  totalCostEstimate: number;
  topTools: Array<{ toolName: string; count: number; successRate: number }>;
  topErrors: Array<{ errorCode: string; count: number; message: string }>;
  executionTrends: Array<{ date: string; count: number; successRate: number }>;
  performanceMetrics: {
    p50ExecutionTime: number;
    p95ExecutionTime: number;
    p99ExecutionTime: number;
  };
}

export class AgentLogger {
  private logBuffer: LogEntry[] = [];
  private bufferSize = 100;
  private flushInterval = 5000; // 5 seconds
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.startPeriodicFlush();
  }

  /**
   * Log an agent action
   */
  async log(entry: LogEntry): Promise<void> {
    // Add to buffer
    this.logBuffer.push({
      ...entry,
      metadata: {
        ...entry.metadata,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        sessionId: this.getSessionId(),
      }
    });

    // Flush if buffer is full
    if (this.logBuffer.length >= this.bufferSize) {
      await this.flush();
    }
  }

  /**
   * Log a successful agent action
   */
  async logSuccess(
    agentAction: string,
    result: any,
    executionTimeMs: number,
    createdBy: string,
    options: Partial<LogEntry> = {}
  ): Promise<void> {
    await this.log({
      agentAction,
      outputData: result,
      executionTimeMs,
      success: true,
      createdBy,
      ...options,
    });
  }

  /**
   * Log a failed agent action
   */
  async logError(
    agentAction: string,
    error: Error,
    executionTimeMs: number,
    createdBy: string,
    options: Partial<LogEntry> = {}
  ): Promise<void> {
    await this.log({
      agentAction,
      executionTimeMs,
      success: false,
      errorMessage: error.message,
      errorCode: (error as any).code || 'UNKNOWN_ERROR',
      createdBy,
      ...options,
    });
  }

  /**
   * Log a tool call
   */
  async logToolCall(
    toolName: string,
    inputData: Record<string, any>,
    outputData: any,
    executionTimeMs: number,
    success: boolean,
    createdBy: string,
    options: Partial<LogEntry> = {}
  ): Promise<void> {
    await this.log({
      agentAction: `tool_call:${toolName}`,
      toolName,
      inputData,
      outputData,
      executionTimeMs,
      success,
      createdBy,
      ...options,
    });
  }

  /**
   * Log an LLM interaction
   */
  async logLLMInteraction(
    provider: string,
    model: string,
    inputData: Record<string, any>,
    outputData: any,
    executionTimeMs: number,
    tokensUsed: number,
    costEstimate: number,
    success: boolean,
    createdBy: string,
    options: Partial<LogEntry> = {}
  ): Promise<void> {
    await this.log({
      agentAction: 'llm_interaction',
      llmProvider: provider,
      llmModel: model,
      inputData,
      outputData,
      executionTimeMs,
      tokensUsed,
      costEstimate,
      success,
      createdBy,
      ...options,
    });
  }

  /**
   * Log an agent execution (maps to existing log methods for compatibility)
   */
  async logExecution(entry: {
    userId: string;
    agentType: string;
    action: string;
    status: 'started' | 'completed' | 'failed';
    context: Record<string, any>;
    executionTimeMs: number;
    result?: {
      success: boolean;
      data?: any;
    };
  }): Promise<void> {
    const agentAction = `${entry.agentType}:${entry.action}`;

    if (entry.status === 'completed' && entry.result?.success) {
      await this.logSuccess(
        agentAction,
        entry.result.data,
        entry.executionTimeMs,
        entry.userId,
        {
          metadata: {
            agentType: entry.agentType,
            status: entry.status,
            context: entry.context,
          }
        }
      );
    } else if (entry.status === 'failed') {
      const error = new Error(entry.context.error || 'Agent execution failed');
      await this.logError(
        agentAction,
        error,
        entry.executionTimeMs,
        entry.userId,
        {
          metadata: {
            agentType: entry.agentType,
            status: entry.status,
            context: entry.context,
          }
        }
      );
    } else {
      // For 'started' status or other cases, use generic log
      await this.log({
        agentAction,
        executionTimeMs: entry.executionTimeMs,
        success: entry.status === 'completed',
        createdBy: entry.userId,
        metadata: {
          agentType: entry.agentType,
          status: entry.status,
          context: entry.context,
        }
      });
    }
  }

  /**
   * Log a human override
   */
  async logHumanOverride(
    originalAction: string,
    overrideReason: string,
    createdBy: string,
    options: Partial<LogEntry> = {}
  ): Promise<void> {
    await this.log({
      agentAction: `human_override:${originalAction}`,
      humanOverride: true,
      overrideReason,
      executionTimeMs: 0,
      success: true,
      createdBy,
      ...options,
    });
  }

  /**
   * Flush buffered logs to database
   */
  async flush(): Promise<void> {
    if (this.logBuffer.length === 0) return;

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      const { error } = await supabase
        .from('agent_execution_logs')
        .insert(logsToFlush.map(entry => ({
          workflow_id: entry.workflowId,
          execution_id: entry.executionId,
          agent_action: entry.agentAction,
          tool_name: entry.toolName,
          input_data: entry.inputData,
          output_data: entry.outputData,
          llm_provider: entry.llmProvider,
          llm_model: entry.llmModel,
          execution_time_ms: entry.executionTimeMs,
          tokens_used: entry.tokensUsed,
          cost_estimate: entry.costEstimate,
          success: entry.success,
          error_message: entry.errorMessage,
          error_code: entry.errorCode,
          human_override: entry.humanOverride || false,
          override_reason: entry.overrideReason,
          confidence_score: entry.confidenceScore,
          created_by: entry.createdBy,
        })));

      if (error) {
        console.error('Failed to flush agent logs:', error);
        // Re-add failed logs to buffer for retry
        this.logBuffer.unshift(...logsToFlush);
      }
    } catch (error) {
      console.error('Error flushing agent logs:', error);
      // Re-add failed logs to buffer for retry
      this.logBuffer.unshift(...logsToFlush);
    }
  }

  /**
   * Query execution logs
   */
  async queryLogs(query: LogQuery): Promise<AgentExecutionLog[]> {
    let queryBuilder = supabase
      .from('agent_execution_logs')
      .select('*')
      .order('created_at', { ascending: false });

    if (query.workflowId) {
      queryBuilder = queryBuilder.eq('workflow_id', query.workflowId);
    }

    if (query.executionId) {
      queryBuilder = queryBuilder.eq('execution_id', query.executionId);
    }

    if (query.userId) {
      queryBuilder = queryBuilder.eq('created_by', query.userId);
    }

    if (query.agentAction) {
      queryBuilder = queryBuilder.eq('agent_action', query.agentAction);
    }

    if (query.toolName) {
      queryBuilder = queryBuilder.eq('tool_name', query.toolName);
    }

    if (query.success !== undefined) {
      queryBuilder = queryBuilder.eq('success', query.success);
    }

    if (query.startDate) {
      queryBuilder = queryBuilder.gte('created_at', query.startDate.toISOString());
    }

    if (query.endDate) {
      queryBuilder = queryBuilder.lte('created_at', query.endDate.toISOString());
    }

    if (query.limit) {
      queryBuilder = queryBuilder.limit(query.limit);
    }

    if (query.offset) {
      queryBuilder = queryBuilder.range(query.offset, query.offset + (query.limit || 50) - 1);
    }

    const { data, error } = await queryBuilder;

    if (error) {
      throw new Error(`Failed to query logs: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get analytics for agent execution logs
   */
  async getAnalytics(query: Omit<LogQuery, 'limit' | 'offset'> = {}): Promise<LogAnalytics> {
    const logs = await this.queryLogs({ ...query, limit: 10000 }); // Get more data for analytics

    const totalExecutions = logs.length;
    const successfulExecutions = logs.filter(log => log.success).length;
    const successRate = totalExecutions > 0 ? successfulExecutions / totalExecutions : 0;

    const executionTimes = logs.map(log => (log as any).execution_time_ms).filter(time => time > 0);
    const averageExecutionTime = executionTimes.length > 0
      ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length
      : 0;

    const totalTokensUsed = logs.reduce((sum, log) => sum + ((log as any).tokens_used || 0), 0);
    const totalCostEstimate = logs.reduce((sum, log) => sum + ((log as any).cost_estimate || 0), 0);

    // Calculate performance percentiles
    const sortedTimes = executionTimes.sort((a, b) => a - b);
    const p50ExecutionTime = sortedTimes[Math.floor(sortedTimes.length * 0.5)] || 0;
    const p95ExecutionTime = sortedTimes[Math.floor(sortedTimes.length * 0.95)] || 0;
    const p99ExecutionTime = sortedTimes[Math.floor(sortedTimes.length * 0.99)] || 0;

    // Top tools analysis
    const toolStats = new Map<string, { count: number; successes: number }>();
    logs.forEach(log => {
      const toolName = (log as any).tool_name;
      if (toolName) {
        const stats = toolStats.get(toolName) || { count: 0, successes: 0 };
        stats.count++;
        if (log.success) stats.successes++;
        toolStats.set(toolName, stats);
      }
    });

    const topTools = Array.from(toolStats.entries())
      .map(([toolName, stats]) => ({
        toolName,
        count: stats.count,
        successRate: stats.count > 0 ? stats.successes / stats.count : 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Top errors analysis
    const errorStats = new Map<string, { count: number; message: string }>();
    logs.filter(log => !log.success && (log as any).error_code).forEach(log => {
      const code = (log as any).error_code!;
      const stats = errorStats.get(code) || { count: 0, message: (log as any).error_message || '' };
      stats.count++;
      errorStats.set(code, stats);
    });

    const topErrors = Array.from(errorStats.entries())
      .map(([errorCode, stats]) => ({
        errorCode,
        count: stats.count,
        message: stats.message,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Execution trends (daily)
    const trendMap = new Map<string, { count: number; successes: number }>();
    logs.forEach(log => {
      const date = new Date((log as any).created_at).toISOString().split('T')[0];
      const stats = trendMap.get(date) || { count: 0, successes: 0 };
      stats.count++;
      if (log.success) stats.successes++;
      trendMap.set(date, stats);
    });

    const executionTrends = Array.from(trendMap.entries())
      .map(([date, stats]) => ({
        date,
        count: stats.count,
        successRate: stats.count > 0 ? stats.successes / stats.count : 0,
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalExecutions,
      successRate,
      averageExecutionTime,
      totalTokensUsed,
      totalCostEstimate,
      topTools,
      topErrors,
      executionTrends,
      performanceMetrics: {
        p50ExecutionTime,
        p95ExecutionTime,
        p99ExecutionTime,
      },
    };
  }

  private startPeriodicFlush(): void {
    this.flushTimer = setInterval(() => {
      this.flush().catch(console.error);
    }, this.flushInterval);
  }

  private getSessionId(): string {
    // Simple session ID generation
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    this.flush().catch(console.error);
  }
}

// Singleton instance
export const agentLogger = new AgentLogger();
