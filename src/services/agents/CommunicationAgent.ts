/**
 * Advanced Communication Agent
 * Handles intelligent communication with sentiment analysis and automated responses
 */

import { llmManager } from '@/services/llm/LLMManager';
import { agentLogger } from './AgentLogger';
import { CandidateType } from '@/types/candidate';

export interface CommunicationContext {
  userId: string;
  candidateId?: string;
  jobId?: string;
  workflowId?: string;
  communicationType: 'email' | 'sms' | 'call_notes' | 'interview_feedback';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tone: 'professional' | 'friendly' | 'formal' | 'casual';
  includePersonalization?: boolean;
  includeSentimentAnalysis?: boolean;
}

export interface SentimentAnalysis {
  sentiment: 'positive' | 'negative' | 'neutral' | 'mixed';
  confidence: number;
  emotions: {
    joy: number;
    anger: number;
    fear: number;
    sadness: number;
    surprise: number;
    trust: number;
  };
  keyPhrases: string[];
  concerns: string[];
  opportunities: string[];
  recommendedResponse: 'immediate' | 'scheduled' | 'escalate' | 'standard';
}

export interface CommunicationRequest {
  type: 'generate_email' | 'analyze_sentiment' | 'suggest_response' | 'schedule_followup';
  content?: string;
  subject?: string;
  recipient?: CandidateType;
  template?: string;
  customInstructions?: string;
  previousMessages?: Array<{
    content: string;
    sender: 'recruiter' | 'candidate';
    timestamp: string;
  }>;
}

export interface CommunicationResult {
  success: boolean;
  content?: string;
  subject?: string;
  sentimentAnalysis?: SentimentAnalysis;
  suggestions?: string[];
  followUpActions?: string[];
  executionTimeMs: number;
  confidence: number;
  reasoning: string;
  metadata?: Record<string, any>;
}

export class CommunicationAgent {
  private context: CommunicationContext;

  constructor(context: CommunicationContext) {
    this.context = context;
  }

  async processRequest(request: CommunicationRequest): Promise<CommunicationResult> {
    const startTime = Date.now();

    try {
      // Log the communication start
      await agentLogger.logExecution({
        userId: this.context.userId,
        agentType: 'communication',
        action: request.type,
        status: 'started',
        context: {
          candidateId: this.context.candidateId,
          communicationType: this.context.communicationType,
          priority: this.context.priority,
        },
        executionTimeMs: 0,
      });

      let result: CommunicationResult;

      switch (request.type) {
        case 'generate_email':
          result = await this.generateEmail(request);
          break;
        case 'analyze_sentiment':
          result = await this.analyzeSentiment(request);
          break;
        case 'suggest_response':
          result = await this.suggestResponse(request);
          break;
        case 'schedule_followup':
          result = await this.scheduleFollowup(request);
          break;
        default:
          throw new Error(`Unsupported request type: ${request.type}`);
      }

      result.executionTimeMs = Date.now() - startTime;

      // Log successful completion
      await agentLogger.logExecution({
        userId: this.context.userId,
        agentType: 'communication',
        action: request.type,
        status: 'completed',
        context: {
          candidateId: this.context.candidateId,
          success: result.success,
          confidence: result.confidence,
        },
        executionTimeMs: result.executionTimeMs,
        result: {
          success: result.success,
          data: {
            confidence: result.confidence,
            hasContent: !!result.content,
            hasSentiment: !!result.sentimentAnalysis,
          }
        }
      });

      return result;

    } catch (error) {
      const executionTimeMs = Date.now() - startTime;
      
      // Log the error
      await agentLogger.logExecution({
        userId: this.context.userId,
        agentType: 'communication',
        action: request.type,
        status: 'failed',
        context: {
          candidateId: this.context.candidateId,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        executionTimeMs,
      });

      throw error;
    }
  }

  private async generateEmail(request: CommunicationRequest): Promise<CommunicationResult> {
    const prompt = this.createEmailGenerationPrompt(request);
    
    const response = await llmManager.generateResponse({
      messages: [
        {
          role: 'system',
          content: this.getEmailSystemPrompt()
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      maxTokens: 2000,
    }, undefined, 'text_generation');

    return this.parseEmailResponse(response.content);
  }

  private async analyzeSentiment(request: CommunicationRequest): Promise<CommunicationResult> {
    if (!request.content) {
      throw new Error('Content is required for sentiment analysis');
    }

    const prompt = this.createSentimentAnalysisPrompt(request.content);
    
    const response = await llmManager.generateResponse({
      messages: [
        {
          role: 'system',
          content: this.getSentimentSystemPrompt()
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      maxTokens: 1500,
    }, undefined, 'analysis');

    return this.parseSentimentResponse(response.content);
  }

  private async suggestResponse(request: CommunicationRequest): Promise<CommunicationResult> {
    const prompt = this.createResponseSuggestionPrompt(request);
    
    const response = await llmManager.generateResponse({
      messages: [
        {
          role: 'system',
          content: this.getResponseSystemPrompt()
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.6,
      maxTokens: 1500,
    }, undefined, 'text_generation');

    return this.parseResponseSuggestion(response.content);
  }

  private async scheduleFollowup(request: CommunicationRequest): Promise<CommunicationResult> {
    const prompt = this.createFollowupPrompt(request);
    
    const response = await llmManager.generateResponse({
      messages: [
        {
          role: 'system',
          content: this.getFollowupSystemPrompt()
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.5,
      maxTokens: 1000,
    }, undefined, 'reasoning');

    return this.parseFollowupResponse(response.content);
  }

  private getEmailSystemPrompt(): string {
    return `You are an expert recruitment communication specialist. Create professional, engaging emails that:
- Match the specified tone and communication style
- Include appropriate personalization when candidate information is provided
- Follow email best practices for recruitment
- Are clear, concise, and action-oriented
- Maintain a professional yet human touch

Always consider the context of the recruitment process and the candidate's stage in the pipeline.`;
  }

  private getSentimentSystemPrompt(): string {
    return `You are an expert sentiment analysis specialist for recruitment communications. Analyze text to:
- Identify overall sentiment and emotional tone
- Detect specific emotions and their intensity
- Identify key phrases that indicate candidate interest, concerns, or objections
- Recommend appropriate response strategies
- Flag urgent situations that need immediate attention

Provide detailed, actionable insights for recruitment teams.`;
  }

  private getResponseSystemPrompt(): string {
    return `You are an expert recruitment communication advisor. Suggest appropriate responses that:
- Address the candidate's specific concerns or questions
- Maintain the recruitment relationship
- Move the process forward appropriately
- Match the communication tone and style
- Include specific next steps when relevant

Focus on building trust and maintaining candidate engagement.`;
  }

  private getFollowupSystemPrompt(): string {
    return `You are an expert recruitment workflow specialist. Analyze communication patterns to:
- Determine optimal follow-up timing
- Suggest appropriate follow-up actions
- Identify when escalation is needed
- Recommend communication channels
- Prioritize follow-up tasks

Ensure no candidates fall through the cracks while respecting their time and preferences.`;
  }

  private createEmailGenerationPrompt(request: CommunicationRequest): string {
    const candidateInfo = request.recipient ? this.formatCandidateInfo(request.recipient) : 'No candidate information provided';
    
    return `Generate a professional recruitment email with the following requirements:

**Communication Context:**
- Type: ${this.context.communicationType}
- Tone: ${this.context.tone}
- Priority: ${this.context.priority}
- Personalization: ${this.context.includePersonalization ? 'Yes' : 'No'}

**Recipient Information:**
${candidateInfo}

**Email Requirements:**
- Subject: ${request.subject || 'Please generate an appropriate subject line'}
- Template: ${request.template || 'Standard recruitment email'}
- Custom Instructions: ${request.customInstructions || 'None'}

**Previous Context:**
${request.previousMessages ? this.formatPreviousMessages(request.previousMessages) : 'No previous messages'}

Please provide the email in this JSON format:
{
  "subject": "string",
  "content": "string",
  "tone": "string",
  "keyPoints": ["string"],
  "callToAction": "string",
  "followUpSuggestions": ["string"]
}`;
  }

  private createSentimentAnalysisPrompt(content: string): string {
    return `Analyze the sentiment and emotional content of this recruitment-related communication:

**Message Content:**
"${content}"

**Analysis Required:**
Provide a comprehensive sentiment analysis including:
1. Overall sentiment classification
2. Confidence level in the analysis
3. Specific emotions detected and their intensity (0-1 scale)
4. Key phrases that indicate sentiment
5. Potential concerns or red flags
6. Opportunities for positive engagement
7. Recommended response urgency

Format as JSON:
{
  "sentiment": "positive|negative|neutral|mixed",
  "confidence": number,
  "emotions": {
    "joy": number,
    "anger": number,
    "fear": number,
    "sadness": number,
    "surprise": number,
    "trust": number
  },
  "keyPhrases": ["string"],
  "concerns": ["string"],
  "opportunities": ["string"],
  "recommendedResponse": "immediate|scheduled|escalate|standard",
  "reasoning": "string"
}`;
  }

  private createResponseSuggestionPrompt(request: CommunicationRequest): string {
    return `Suggest an appropriate response to this recruitment communication:

**Original Message:**
"${request.content || 'No content provided'}"

**Context:**
- Communication Type: ${this.context.communicationType}
- Priority: ${this.context.priority}
- Tone: ${this.context.tone}

**Previous Messages:**
${request.previousMessages ? this.formatPreviousMessages(request.previousMessages) : 'No previous context'}

**Custom Instructions:**
${request.customInstructions || 'None'}

Provide response suggestions in JSON format:
{
  "primaryResponse": "string",
  "alternativeResponses": ["string"],
  "keyPoints": ["string"],
  "nextSteps": ["string"],
  "urgency": "low|medium|high",
  "reasoning": "string"
}`;
  }

  private createFollowupPrompt(request: CommunicationRequest): string {
    return `Analyze this communication thread and recommend follow-up actions:

**Latest Message:**
"${request.content || 'No content provided'}"

**Communication History:**
${request.previousMessages ? this.formatPreviousMessages(request.previousMessages) : 'No previous messages'}

**Context:**
- Type: ${this.context.communicationType}
- Priority: ${this.context.priority}

Recommend follow-up strategy in JSON format:
{
  "followUpActions": ["string"],
  "timing": "immediate|1day|3days|1week|2weeks",
  "priority": "low|medium|high|urgent",
  "channel": "email|phone|sms|in_person",
  "escalation": boolean,
  "reasoning": "string"
}`;
  }

  private formatCandidateInfo(candidate: CandidateType): string {
    return `
**Candidate:** ${candidate.name}
**Role:** ${candidate.role}
**Email:** ${candidate.email}
**Location:** ${candidate.location || 'Not specified'}
**Experience:** ${candidate.experience}
**Skills:** ${candidate.skills?.map(s => typeof s === 'string' ? s : s.name).join(', ') || 'Not specified'}
    `.trim();
  }

  private formatPreviousMessages(messages: Array<{content: string; sender: string; timestamp: string}>): string {
    return messages.map(msg => 
      `[${msg.timestamp}] ${msg.sender}: ${msg.content}`
    ).join('\n');
  }

  private parseEmailResponse(content: string): CommunicationResult {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          content: parsed.content,
          subject: parsed.subject,
          suggestions: parsed.followUpSuggestions || [],
          confidence: 0.8,
          reasoning: `Generated ${parsed.tone || 'professional'} email with key points: ${parsed.keyPoints?.join(', ') || 'standard content'}`,
          executionTimeMs: 0,
        };
      }
    } catch (error) {
      console.error('Error parsing email response:', error);
    }

    return {
      success: true,
      content: content,
      confidence: 0.6,
      reasoning: 'Generated email content (fallback parsing)',
      executionTimeMs: 0,
    };
  }

  private parseSentimentResponse(content: string): CommunicationResult {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          sentimentAnalysis: parsed,
          confidence: parsed.confidence || 0.7,
          reasoning: parsed.reasoning || 'Sentiment analysis completed',
          executionTimeMs: 0,
        };
      }
    } catch (error) {
      console.error('Error parsing sentiment response:', error);
    }

    return {
      success: false,
      confidence: 0.3,
      reasoning: 'Failed to parse sentiment analysis',
      executionTimeMs: 0,
    };
  }

  private parseResponseSuggestion(content: string): CommunicationResult {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          content: parsed.primaryResponse,
          suggestions: [parsed.primaryResponse, ...(parsed.alternativeResponses || [])],
          followUpActions: parsed.nextSteps || [],
          confidence: 0.8,
          reasoning: parsed.reasoning || 'Response suggestions generated',
          executionTimeMs: 0,
        };
      }
    } catch (error) {
      console.error('Error parsing response suggestion:', error);
    }

    return {
      success: false,
      confidence: 0.3,
      reasoning: 'Failed to generate response suggestions',
      executionTimeMs: 0,
    };
  }

  private parseFollowupResponse(content: string): CommunicationResult {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          followUpActions: parsed.followUpActions || [],
          suggestions: [`Follow up via ${parsed.channel} in ${parsed.timing}`],
          confidence: 0.8,
          reasoning: parsed.reasoning || 'Follow-up strategy generated',
          executionTimeMs: 0,
        };
      }
    } catch (error) {
      console.error('Error parsing followup response:', error);
    }

    return {
      success: false,
      confidence: 0.3,
      reasoning: 'Failed to generate follow-up strategy',
      executionTimeMs: 0,
    };
  }
}
