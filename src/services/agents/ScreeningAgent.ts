/**
 * Enhanced Candidate Screening Agent
 * Leverages Groq models for intelligent candidate analysis and scoring
 */

import { llmManager } from '@/services/llm/LLMManager';
import { agentLogger } from './AgentLogger';
import { CandidateType } from '@/types/candidate';

export interface ScreeningCriteria {
  jobTitle: string;
  requiredSkills: string[];
  experienceLevel: 'junior' | 'mid' | 'senior' | 'lead' | 'executive';
  department: string;
  location?: string;
  remoteOk?: boolean;
  salaryRange?: {
    min: number;
    max: number;
  };
  customCriteria?: Record<string, any>;
}

export interface ScreeningResult {
  overallScore: number;
  confidenceLevel: number;
  criteriaScores: {
    technicalFit: number;
    experienceMatch: number;
    culturalFit: number;
    compensationAlignment: number;
    locationCompatibility: number;
    skillsMatch: number;
  };
  analysis: {
    strengths: string[];
    concerns: string[];
    recommendations: string[];
    keyInsights: string[];
  };
  reasoning: string;
  nextSteps: string[];
  interviewQuestions: string[];
  riskFactors: string[];
  executionTimeMs: number;
}

export interface ScreeningContext {
  userId: string;
  jobId?: string;
  workflowId?: string;
  priority: 'low' | 'medium' | 'high';
  includeInterviewQuestions?: boolean;
  includeRiskAssessment?: boolean;
}

export class ScreeningAgent {
  private userId: string;
  private context: ScreeningContext;

  constructor(userId: string, context: ScreeningContext) {
    this.userId = userId;
    this.context = context;
  }

  async screenCandidate(
    candidate: CandidateType,
    criteria: ScreeningCriteria
  ): Promise<ScreeningResult> {
    const startTime = Date.now();
    
    try {
      // Log the screening start
      await agentLogger.logExecution({
        userId: this.userId,
        agentType: 'screening',
        action: 'screen_candidate',
        status: 'started',
        context: {
          candidateId: candidate.id,
          jobTitle: criteria.jobTitle,
          priority: this.context.priority,
        },
        executionTimeMs: 0,
      });

      // Determine the best model for this screening task
      const taskType = this.getTaskType(criteria);
      
      // Create comprehensive screening prompt
      const prompt = this.createScreeningPrompt(candidate, criteria);
      
      // Generate screening analysis using the best available model
      const response = await llmManager.generateResponse({
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // Lower temperature for more consistent analysis
        maxTokens: 4000,
      }, undefined, taskType);

      // Parse the response
      const result = this.parseScreeningResponse(response.content, startTime);

      // Generate interview questions if requested
      if (this.context.includeInterviewQuestions) {
        result.interviewQuestions = await this.generateInterviewQuestions(candidate, criteria);
      }

      // Assess risk factors if requested
      if (this.context.includeRiskAssessment) {
        result.riskFactors = await this.assessRiskFactors(candidate, criteria);
      }

      // Log successful completion
      await agentLogger.logExecution({
        userId: this.userId,
        agentType: 'screening',
        action: 'screen_candidate',
        status: 'completed',
        context: {
          candidateId: candidate.id,
          overallScore: result.overallScore,
          confidenceLevel: result.confidenceLevel,
        },
        executionTimeMs: result.executionTimeMs,
        result: {
          success: true,
          data: {
            score: result.overallScore,
            confidence: result.confidenceLevel,
          }
        }
      });

      return result;

    } catch (error) {
      const executionTimeMs = Date.now() - startTime;
      
      // Log the error
      await agentLogger.logExecution({
        userId: this.userId,
        agentType: 'screening',
        action: 'screen_candidate',
        status: 'failed',
        context: {
          candidateId: candidate.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        executionTimeMs,
      });

      throw error;
    }
  }

  private getTaskType(criteria: ScreeningCriteria): 'analysis' | 'reasoning' {
    // Use reasoning models for complex screening criteria
    if (criteria.customCriteria || criteria.experienceLevel === 'executive') {
      return 'reasoning';
    }
    return 'analysis';
  }

  private getSystemPrompt(): string {
    return `You are an expert AI recruitment analyst specializing in candidate screening and assessment. Your role is to provide objective, data-driven analysis of candidate fit for specific positions.

Key responsibilities:
- Analyze candidate qualifications against job requirements
- Provide detailed scoring across multiple criteria
- Identify strengths, concerns, and recommendations
- Maintain objectivity and avoid bias
- Focus on skills, experience, and role alignment

Always provide structured, actionable insights that help recruitment teams make informed decisions.`;
  }

  private createScreeningPrompt(candidate: CandidateType, criteria: ScreeningCriteria): string {
    const candidateInfo = this.formatCandidateInfo(candidate);
    const criteriaInfo = this.formatCriteria(criteria);

    return `Please conduct a comprehensive screening analysis for this candidate:

## CANDIDATE PROFILE:
${candidateInfo}

## JOB REQUIREMENTS:
${criteriaInfo}

## ANALYSIS REQUIRED:
Provide a detailed screening assessment with the following structure:

1. **Overall Score** (0-100): Comprehensive fit assessment
2. **Confidence Level** (0-1): How confident you are in this assessment
3. **Criteria Scores** (0-100 each):
   - Technical Fit: Skills and technical qualifications match
   - Experience Match: Years and type of experience alignment
   - Cultural Fit: Based on available information and role requirements
   - Compensation Alignment: Salary expectations vs role level
   - Location Compatibility: Location preferences vs job requirements
   - Skills Match: Specific skills alignment with requirements

4. **Analysis**:
   - Strengths: Top 3-5 candidate strengths for this role
   - Concerns: Potential issues or gaps to address
   - Recommendations: Specific next steps for this candidate
   - Key Insights: Important observations about the candidate

5. **Reasoning**: Detailed explanation of the scoring and assessment
6. **Next Steps**: Recommended actions for the recruitment team

Format your response as valid JSON with this exact structure:
{
  "overallScore": number,
  "confidenceLevel": number,
  "criteriaScores": {
    "technicalFit": number,
    "experienceMatch": number,
    "culturalFit": number,
    "compensationAlignment": number,
    "locationCompatibility": number,
    "skillsMatch": number
  },
  "analysis": {
    "strengths": ["string"],
    "concerns": ["string"],
    "recommendations": ["string"],
    "keyInsights": ["string"]
  },
  "reasoning": "string",
  "nextSteps": ["string"]
}`;
  }

  private formatCandidateInfo(candidate: CandidateType): string {
    const skills = candidate.skills?.map(skill => 
      typeof skill === 'string' ? skill : `${skill.name} (${skill.level}, ${skill.years}y)`
    ).join(', ') || 'Not specified';

    return `
**Name**: ${candidate.name}
**Role**: ${candidate.role}
**Experience**: ${candidate.experience}
**Industry**: ${candidate.industry || 'Not specified'}
**Skills**: ${skills}
**Location**: ${candidate.location || 'Not specified'}
**Remote Preference**: ${candidate.remotePreference || 'Not specified'}
**Visa Status**: ${candidate.visaStatus || 'Not specified'}
**Expected Salary**: ${candidate.screening?.compensation?.expectedSalaryMin && candidate.screening?.compensation?.expectedSalaryMax
      ? `$${candidate.screening.compensation.expectedSalaryMin} - $${candidate.screening.compensation.expectedSalaryMax}`
      : 'Not specified'}
**AI Summary**: ${candidate.aiSummary || 'Not available'}
**Relationship Score**: ${candidate.relationshipScore || 'Not available'}
    `.trim();
  }

  private formatCriteria(criteria: ScreeningCriteria): string {
    return `
**Job Title**: ${criteria.jobTitle}
**Department**: ${criteria.department}
**Experience Level**: ${criteria.experienceLevel}
**Required Skills**: ${criteria.requiredSkills.join(', ')}
**Location**: ${criteria.location || 'Not specified'}
**Remote Work**: ${criteria.remoteOk ? 'Yes' : 'No'}
**Salary Range**: ${criteria.salaryRange ? `$${criteria.salaryRange.min} - $${criteria.salaryRange.max}` : 'Not specified'}
**Custom Criteria**: ${criteria.customCriteria ? JSON.stringify(criteria.customCriteria, null, 2) : 'None'}
    `.trim();
  }

  private parseScreeningResponse(content: string, startTime: number): ScreeningResult {
    try {
      // Clean the response to extract JSON
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        ...parsed,
        interviewQuestions: [],
        riskFactors: [],
        executionTimeMs: Date.now() - startTime,
      };
    } catch (error) {
      // Fallback parsing if JSON parsing fails
      return this.createFallbackResult(content, startTime);
    }
  }

  private createFallbackResult(content: string, startTime: number): ScreeningResult {
    return {
      overallScore: 50,
      confidenceLevel: 0.3,
      criteriaScores: {
        technicalFit: 50,
        experienceMatch: 50,
        culturalFit: 50,
        compensationAlignment: 50,
        locationCompatibility: 50,
        skillsMatch: 50,
      },
      analysis: {
        strengths: ['Analysis available in reasoning section'],
        concerns: ['Unable to parse detailed analysis'],
        recommendations: ['Manual review recommended'],
        keyInsights: ['Detailed analysis available in reasoning'],
      },
      reasoning: content,
      nextSteps: ['Manual review of candidate', 'Conduct detailed interview'],
      interviewQuestions: [],
      riskFactors: [],
      executionTimeMs: Date.now() - startTime,
    };
  }

  private async generateInterviewQuestions(
    candidate: CandidateType,
    criteria: ScreeningCriteria
  ): Promise<string[]> {
    try {
      const response = await llmManager.generateResponse({
        messages: [
          {
            role: 'system',
            content: 'You are an expert interview designer. Create targeted interview questions based on candidate profile and job requirements.'
          },
          {
            role: 'user',
            content: `Generate 5-7 targeted interview questions for this candidate:

Candidate: ${candidate.name} - ${candidate.role}
Skills: ${candidate.skills?.map(s => typeof s === 'string' ? s : s.name).join(', ')}
Job: ${criteria.jobTitle} (${criteria.experienceLevel} level)

Focus on areas that need clarification or deeper exploration based on the candidate's profile.
Return as a JSON array of strings.`
          }
        ],
        temperature: 0.7,
        maxTokens: 1000,
      }, undefined, 'text_generation');

      const questions = JSON.parse(response.content);
      return Array.isArray(questions) ? questions : [];
    } catch (error) {
      console.error('Error generating interview questions:', error);
      return [
        'Tell me about your experience with the key technologies for this role.',
        'Describe a challenging project you worked on recently.',
        'How do you approach problem-solving in your work?',
        'What interests you most about this position?',
        'How do you stay current with industry trends?'
      ];
    }
  }

  private async assessRiskFactors(
    candidate: CandidateType,
    criteria: ScreeningCriteria
  ): Promise<string[]> {
    try {
      const response = await llmManager.generateResponse({
        messages: [
          {
            role: 'system',
            content: 'You are a risk assessment specialist. Identify potential hiring risks based on candidate profile and job requirements.'
          },
          {
            role: 'user',
            content: `Assess potential risks for hiring this candidate:

Candidate: ${candidate.name}
Experience: ${candidate.experience}
Location: ${candidate.location} (Remote: ${candidate.remotePreference})
Visa Status: ${candidate.visaStatus}
Expected Salary: ${candidate.screening?.compensation?.expectedSalaryMin && candidate.screening?.compensation?.expectedSalaryMax
      ? `$${candidate.screening.compensation.expectedSalaryMin} - $${candidate.screening.compensation.expectedSalaryMax}`
      : 'Not specified'}

Job Requirements:
- Title: ${criteria.jobTitle}
- Level: ${criteria.experienceLevel}
- Location: ${criteria.location}
- Remote: ${criteria.remoteOk}
- Salary Range: ${criteria.salaryRange ? `$${criteria.salaryRange.min}-${criteria.salaryRange.max}` : 'Not specified'}

Identify 3-5 potential risks or concerns. Return as JSON array of strings.`
          }
        ],
        temperature: 0.4,
        maxTokens: 800,
      }, undefined, 'analysis');

      const risks = JSON.parse(response.content);
      return Array.isArray(risks) ? risks : [];
    } catch (error) {
      console.error('Error assessing risk factors:', error);
      return ['Manual risk assessment recommended'];
    }
  }
}
