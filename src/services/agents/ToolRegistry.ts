/**
 * Tool Registry - Manages available tools and their handlers
 */

import { supabase } from '@/integrations/supabase/client';
import { AgentTool, ToolHandler, ToolCallRequest, ToolCallResponse, ToolExecutionContext } from './types';

export class ToolRegistry {
  private tools: Map<string, AgentTool> = new Map();
  private handlers: Map<string, ToolHandler> = new Map();
  private initialized = false;

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load tools from database
      const { data: toolsData, error } = await supabase
        .from('agent_tools')
        .select('*')
        .eq('is_active', true);

      if (error) {
        console.error('Failed to load agent tools:', error);
        return;
      }

      // Register tools
      for (const toolData of toolsData || []) {
        const tool: AgentTool = {
          id: toolData.id,
          name: toolData.name,
          description: toolData.description,
          functionSchema: toolData.function_schema,
          securityLevel: toolData.security_level,
          category: toolData.category,
          requiresApproval: toolData.requires_approval,
          rateLimitPerHour: toolData.rate_limit_per_hour,
          isActive: toolData.is_active,
        };

        this.tools.set(tool.name, tool);
      }

      // Register built-in tool handlers
      await this.registerBuiltInHandlers();

      this.initialized = true;
      console.log(`Initialized ${this.tools.size} agent tools`);
    } catch (error) {
      console.error('Failed to initialize Tool Registry:', error);
    }
  }

  private async registerBuiltInHandlers(): Promise<void> {
    // Register candidate info handler
    this.registerHandler({
      name: 'get_candidate_info',
      async execute(request: ToolCallRequest, context: ToolExecutionContext): Promise<ToolCallResponse> {
        const startTime = Date.now();
        
        try {
          const { candidate_id } = request.arguments;
          
          if (!candidate_id) {
            throw new Error('candidate_id is required');
          }

          const { data, error } = await supabase
            .from('candidates')
            .select('*')
            .eq('id', candidate_id)
            .eq('user_id', context.userId)
            .single();

          if (error) {
            throw new Error(`Failed to fetch candidate: ${error.message}`);
          }

          return {
            requestId: request.requestId,
            success: true,
            result: data,
            executionTimeMs: Date.now() - startTime,
          };
        } catch (error) {
          return {
            requestId: request.requestId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            executionTimeMs: Date.now() - startTime,
          };
        }
      },
      validate(args: Record<string, any>): boolean {
        return typeof args.candidate_id === 'string' && args.candidate_id.length > 0;
      },
      getRequiredPermissions(): string[] {
        return ['read:candidates'];
      },
    });

    // Register candidate search handler
    this.registerHandler({
      name: 'search_candidates',
      async execute(request: ToolCallRequest, context: ToolExecutionContext): Promise<ToolCallResponse> {
        const startTime = Date.now();
        
        try {
          const { query, skills, experience_min, limit = 10 } = request.arguments;
          
          if (!query) {
            throw new Error('query is required');
          }

          let queryBuilder = supabase
            .from('candidates')
            .select('id, name, email, role, experience, skills:candidate_skills(skill_id, skills(name))')
            .eq('user_id', context.userId)
            .limit(Math.min(limit, 50)); // Cap at 50 results

          // Add text search
          if (query) {
            queryBuilder = queryBuilder.textSearch('search_vector', query);
          }

          const { data, error } = await queryBuilder;

          if (error) {
            throw new Error(`Failed to search candidates: ${error.message}`);
          }

          return {
            requestId: request.requestId,
            success: true,
            result: data,
            executionTimeMs: Date.now() - startTime,
          };
        } catch (error) {
          return {
            requestId: request.requestId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            executionTimeMs: Date.now() - startTime,
          };
        }
      },
      validate(args: Record<string, any>): boolean {
        return typeof args.query === 'string' && args.query.length > 0;
      },
      getRequiredPermissions(): string[] {
        return ['read:candidates'];
      },
    });

    // Register candidate analysis handler
    this.registerHandler({
      name: 'analyze_candidate_fit',
      async execute(request: ToolCallRequest, context: ToolExecutionContext): Promise<ToolCallResponse> {
        const startTime = Date.now();
        
        try {
          const { candidate_id, job_id } = request.arguments;
          
          if (!candidate_id || !job_id) {
            throw new Error('candidate_id and job_id are required');
          }

          // Fetch candidate and job data
          const [candidateResult, jobResult] = await Promise.all([
            supabase
              .from('candidates')
              .select('*, candidate_skills(skill_id, skills(name))')
              .eq('id', candidate_id)
              .eq('user_id', context.userId)
              .single(),
            supabase
              .from('jobs')
              .select('*, job_skills(skill_id, skills(name))')
              .eq('id', job_id)
              .eq('user_id', context.userId)
              .single()
          ]);

          if (candidateResult.error || jobResult.error) {
            throw new Error('Failed to fetch candidate or job data');
          }

          // Simple fit analysis (in a real implementation, this would use ML)
          const candidate = candidateResult.data;
          const job = jobResult.data;
          
          const analysis = {
            candidateId: candidate_id,
            jobId: job_id,
            fitScore: Math.floor(Math.random() * 40) + 60, // Mock score 60-100
            strengths: ['Relevant experience', 'Strong technical skills'],
            concerns: ['Location mismatch'],
            recommendation: 'Proceed with interview',
            confidence: 0.85,
          };

          return {
            requestId: request.requestId,
            success: true,
            result: analysis,
            executionTimeMs: Date.now() - startTime,
          };
        } catch (error) {
          return {
            requestId: request.requestId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            executionTimeMs: Date.now() - startTime,
          };
        }
      },
      validate(args: Record<string, any>): boolean {
        return typeof args.candidate_id === 'string' && 
               typeof args.job_id === 'string' &&
               args.candidate_id.length > 0 && 
               args.job_id.length > 0;
      },
      getRequiredPermissions(): string[] {
        return ['read:candidates', 'read:jobs'];
      },
    });
  }

  registerHandler(handler: ToolHandler): void {
    this.handlers.set(handler.name, handler);
  }

  getTool(name: string): AgentTool | undefined {
    return this.tools.get(name);
  }

  getHandler(name: string): ToolHandler | undefined {
    return this.handlers.get(name);
  }

  getAvailableTools(securityLevel: 'safe' | 'restricted' | 'dangerous' = 'safe'): AgentTool[] {
    const allowedLevels = securityLevel === 'dangerous' 
      ? ['safe', 'restricted', 'dangerous']
      : securityLevel === 'restricted'
      ? ['safe', 'restricted']
      : ['safe'];

    return Array.from(this.tools.values())
      .filter(tool => tool.isActive && allowedLevels.includes(tool.securityLevel));
  }

  getToolsByCategory(category: string): AgentTool[] {
    return Array.from(this.tools.values())
      .filter(tool => tool.isActive && tool.category === category);
  }

  async refreshTools(): Promise<void> {
    this.initialized = false;
    this.tools.clear();
    await this.initialize();
  }
}

// Singleton instance
export const toolRegistry = new ToolRegistry();
