/**
 * Autonomous Agent - Combines LLM and tool calling for autonomous actions
 */

import { llmManager } from '@/services/llm/LLMManager';
import { toolRegistry } from './ToolRegistry';
import { toolExecutor } from './ToolExecutor';
import { agent<PERSON>ogger } from './AgentLogger';
import { 
  LLMToolRequest, 
  LLMTool, 
  LLMMessage 
} from '@/services/llm/types';
import { 
  ToolExecutionContext, 
  AgentTool 
} from './types';

export interface AgentTask {
  id: string;
  type: 'candidate_screening' | 'interview_scheduling' | 'candidate_communication' | 'analysis';
  description: string;
  context: Record<string, any>;
  priority: 'low' | 'medium' | 'high';
  maxExecutionTime: number; // milliseconds
  requiredTools?: string[];
}

export interface AgentResult {
  taskId: string;
  success: boolean;
  result?: any;
  error?: string;
  executionTimeMs: number;
  toolsUsed: string[];
  llmInteractions: number;
  tokensUsed: number;
  confidenceScore: number;
  reasoning: string;
}

export class AutonomousAgent {
  private userId: string;
  private workflowId?: string;
  private executionId?: string;
  private securityLevel: 'safe' | 'restricted' | 'dangerous';
  private permissions: string[];

  constructor(
    userId: string,
    options: {
      workflowId?: string;
      executionId?: string;
      securityLevel?: 'safe' | 'restricted' | 'dangerous';
      permissions?: string[];
    } = {}
  ) {
    this.userId = userId;
    this.workflowId = options.workflowId;
    this.executionId = options.executionId;
    this.securityLevel = options.securityLevel || 'safe';
    this.permissions = options.permissions || ['read:candidates', 'read:jobs'];
  }

  async executeTask(task: AgentTask): Promise<AgentResult> {
    const startTime = Date.now();
    let toolsUsed: string[] = [];
    let llmInteractions = 0;
    let tokensUsed = 0;

    try {
      await agentLogger.log({
        workflowId: this.workflowId,
        executionId: this.executionId,
        agentAction: `task_start:${task.type}`,
        inputData: { taskId: task.id, description: task.description, context: task.context },
        executionTimeMs: 0,
        success: true,
        createdBy: this.userId,
      });

      // Initialize registries
      await toolRegistry.initialize();
      await llmManager.initialize();

      // Get available tools
      const availableTools = toolRegistry.getAvailableTools(this.securityLevel);
      const llmTools = this.convertToLLMTools(availableTools);

      // Create system prompt based on task type
      const systemPrompt = this.getSystemPrompt(task.type);

      // Create initial message
      const messages: LLMMessage[] = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: this.createTaskPrompt(task) }
      ];

      let result: any = null;
      let reasoning = '';
      let confidenceScore = 0.5;
      let maxIterations = 5;
      let iteration = 0;

      while (iteration < maxIterations) {
        iteration++;

        // Generate response with tools
        const llmRequest: LLMToolRequest = {
          messages,
          tools: llmTools,
          toolChoice: 'auto',
          temperature: 0.3, // Lower temperature for more consistent results
          maxTokens: 2000,
        };

        const llmResponse = await llmManager.generateWithTools(llmRequest);
        llmInteractions++;
        tokensUsed += llmResponse.usage.totalTokens;

        // Log LLM interaction
        await agentLogger.logLLMInteraction(
          'auto', // Provider will be determined by LLM manager
          'auto', // Model will be determined by LLM manager
          { messages: messages.slice(-1) }, // Last message only for privacy
          { content: llmResponse.content, toolCalls: llmResponse.toolCalls },
          llmResponse.executionTimeMs,
          llmResponse.usage.totalTokens,
          0, // Cost estimation would be calculated here
          true,
          this.userId,
          { workflowId: this.workflowId, executionId: this.executionId }
        );

        // Add assistant response to conversation
        messages.push({
          role: 'assistant',
          content: llmResponse.content,
          toolCalls: llmResponse.toolCalls,
        });

        // Execute tool calls if any
        if (llmResponse.toolCalls && llmResponse.toolCalls.length > 0) {
          for (const toolCall of llmResponse.toolCalls) {
            const toolName = toolCall.function.name;
            const toolArgs = JSON.parse(toolCall.function.arguments);

            toolsUsed.push(toolName);

            // Execute tool
            const toolResponse = await toolExecutor.executeToolCall(
              {
                toolName,
                arguments: toolArgs,
                requestId: toolCall.id,
                workflowId: this.workflowId,
                userId: this.userId,
                context: task.context,
              },
              {
                userId: this.userId,
                workflowId: this.workflowId,
                executionId: this.executionId,
                permissions: this.permissions,
                rateLimits: new Map(),
                securityLevel: this.securityLevel,
              }
            );

            // Add tool response to conversation
            messages.push({
              role: 'tool',
              content: JSON.stringify(toolResponse.result || { error: toolResponse.error }),
              toolCallId: toolCall.id,
            });
          }
        } else {
          // No more tool calls, extract final result
          result = this.extractResult(llmResponse.content, task.type);
          reasoning = llmResponse.content;
          confidenceScore = this.calculateConfidenceScore(llmResponse.content, toolsUsed.length);
          break;
        }

        // Check timeout
        if (Date.now() - startTime > task.maxExecutionTime) {
          throw new Error('Task execution timeout');
        }
      }

      const executionTimeMs = Date.now() - startTime;

      // Log successful completion
      await agentLogger.logSuccess(
        `task_complete:${task.type}`,
        result,
        executionTimeMs,
        this.userId,
        {
          workflowId: this.workflowId,
          executionId: this.executionId,
          confidenceScore,
          metadata: { toolsUsed, llmInteractions, tokensUsed },
        }
      );

      return {
        taskId: task.id,
        success: true,
        result,
        executionTimeMs,
        toolsUsed,
        llmInteractions,
        tokensUsed,
        confidenceScore,
        reasoning,
      };
    } catch (error) {
      const executionTimeMs = Date.now() - startTime;

      // Log error
      await agentLogger.logError(
        `task_error:${task.type}`,
        error as Error,
        executionTimeMs,
        this.userId,
        {
          workflowId: this.workflowId,
          executionId: this.executionId,
          metadata: { toolsUsed, llmInteractions, tokensUsed },
        }
      );

      return {
        taskId: task.id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTimeMs,
        toolsUsed,
        llmInteractions,
        tokensUsed,
        confidenceScore: 0,
        reasoning: 'Task failed due to error',
      };
    }
  }

  private convertToLLMTools(agentTools: AgentTool[]): LLMTool[] {
    return agentTools.map(tool => ({
      type: 'function' as const,
      function: tool.functionSchema,
    }));
  }

  private getSystemPrompt(taskType: string): string {
    const basePrompt = `You are an AI recruiting assistant with access to various tools. 
You should use the available tools to gather information and complete tasks efficiently.
Always provide clear reasoning for your decisions and actions.`;

    const taskSpecificPrompts = {
      candidate_screening: `${basePrompt}
Your task is to screen candidates for job positions. Use tools to:
1. Get candidate information
2. Analyze candidate-job fit
3. Provide scoring and recommendations
Be objective and focus on relevant qualifications.`,

      interview_scheduling: `${basePrompt}
Your task is to help with interview scheduling. Use tools to:
1. Check candidate and interviewer availability
2. Find optimal meeting times
3. Send scheduling communications
Be efficient and respectful of everyone's time.`,

      candidate_communication: `${basePrompt}
Your task is to communicate with candidates. Use tools to:
1. Get candidate information for personalization
2. Draft appropriate messages
3. Send communications when approved
Be professional, friendly, and helpful.`,

      analysis: `${basePrompt}
Your task is to analyze recruitment data. Use tools to:
1. Gather relevant data
2. Identify patterns and insights
3. Provide actionable recommendations
Base your analysis on data and provide specific suggestions.`,
    };

    return taskSpecificPrompts[taskType as keyof typeof taskSpecificPrompts] || basePrompt;
  }

  private createTaskPrompt(task: AgentTask): string {
    return `Please complete the following task:

Task Type: ${task.type}
Description: ${task.description}
Priority: ${task.priority}

Context:
${JSON.stringify(task.context, null, 2)}

Please use the available tools to gather information and complete this task. 
Provide your reasoning and final recommendations.`;
  }

  private extractResult(content: string, taskType: string): any {
    // Simple result extraction - in a real implementation, this would be more sophisticated
    try {
      // Look for JSON in the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      // Fallback to structured text parsing
    }

    // Fallback to basic structured result
    return {
      taskType,
      summary: content.substring(0, 200),
      recommendation: this.extractRecommendation(content),
      confidence: this.extractConfidence(content),
    };
  }

  private extractRecommendation(content: string): string {
    const recommendationMatch = content.match(/recommendation[:\s]+(.*?)(?:\n|$)/i);
    return recommendationMatch ? recommendationMatch[1].trim() : 'No specific recommendation provided';
  }

  private extractConfidence(content: string): number {
    const confidenceMatch = content.match(/confidence[:\s]+(\d+(?:\.\d+)?)/i);
    return confidenceMatch ? parseFloat(confidenceMatch[1]) : 0.5;
  }

  private calculateConfidenceScore(content: string, toolsUsedCount: number): number {
    let score = 0.5; // Base confidence

    // Increase confidence based on tools used
    score += Math.min(toolsUsedCount * 0.1, 0.3);

    // Increase confidence if specific metrics or data are mentioned
    if (content.includes('score') || content.includes('rating') || content.includes('percentage')) {
      score += 0.1;
    }

    // Increase confidence if reasoning is provided
    if (content.includes('because') || content.includes('due to') || content.includes('analysis')) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }
}
