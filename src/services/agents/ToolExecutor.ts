/**
 * Tool Executor - <PERSON>les secure execution of agent tools with rate limiting and approval
 */

import { supabase } from '@/integrations/supabase/client';
import { toolRegistry } from './ToolRegistry';
import {
  ToolCallRequest,
  ToolCallResponse,
  ToolExecutionContext,
  RateLimitStatus,
  ToolApprovalRequest
} from './types';

export class ToolExecutor {
  private rateLimits: Map<string, Map<string, RateLimitStatus>> = new Map(); // userId -> toolName -> status
  private pendingApprovals: Map<string, ToolApprovalRequest> = new Map();

  async executeToolCall(request: ToolCallRequest, context: ToolExecutionContext): Promise<ToolCallResponse> {
    const startTime = Date.now();

    try {
      // Initialize tool registry if needed
      await toolRegistry.initialize();

      // Get tool and handler
      const tool = toolRegistry.getTool(request.toolName);
      const handler = toolRegistry.getHandler(request.toolName);

      if (!tool || !handler) {
        throw new Error(`Tool '${request.toolName}' not found or not available`);
      }

      // Security checks
      await this.performSecurityChecks(request, context, tool);

      // Rate limiting check
      await this.checkRateLimit(request.toolName, context.userId, tool.rateLimitPerHour);

      // Validate arguments
      if (!handler.validate(request.arguments)) {
        throw new Error(`Invalid arguments for tool '${request.toolName}'`);
      }

      // Check if approval is required
      if (tool.requiresApproval && !context.permissions.includes('bypass_approval')) {
        return await this.requestApproval(request, context, tool);
      }

      // Execute the tool
      const response = await handler.execute(request, context);

      // Update rate limit
      this.updateRateLimit(request.toolName, context.userId);

      // Log execution
      await this.logExecution(request, context, response, Date.now() - startTime);

      return response;
    } catch (error) {
      const errorResponse: ToolCallResponse = {
        requestId: request.requestId,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTimeMs: Date.now() - startTime,
      };

      // Log failed execution
      await this.logExecution(request, context, errorResponse, Date.now() - startTime);

      return errorResponse;
    }
  }

  private async performSecurityChecks(
    request: ToolCallRequest, 
    context: ToolExecutionContext, 
    tool: any
  ): Promise<void> {
    // Check security level
    const allowedLevels = context.securityLevel === 'dangerous' 
      ? ['safe', 'restricted', 'dangerous']
      : context.securityLevel === 'restricted'
      ? ['safe', 'restricted']
      : ['safe'];

    if (!allowedLevels.includes(tool.securityLevel)) {
      throw new Error(`Tool '${request.toolName}' requires higher security clearance`);
    }

    // Check permissions
    const handler = toolRegistry.getHandler(request.toolName);
    if (handler) {
      const requiredPermissions = handler.getRequiredPermissions();
      const hasAllPermissions = requiredPermissions.every(perm => 
        context.permissions.includes(perm)
      );

      if (!hasAllPermissions) {
        throw new Error(`Insufficient permissions for tool '${request.toolName}'`);
      }
    }
  }

  private async checkRateLimit(toolName: string, userId: string, maxRequestsPerHour: number): Promise<void> {
    if (!this.rateLimits.has(userId)) {
      this.rateLimits.set(userId, new Map());
    }

    const userLimits = this.rateLimits.get(userId)!;
    const now = new Date();
    const windowStart = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour window

    let rateLimitStatus = userLimits.get(toolName);

    if (!rateLimitStatus) {
      rateLimitStatus = {
        toolName,
        requestCount: 0,
        windowStart,
        windowDurationMs: 60 * 60 * 1000, // 1 hour
        maxRequests: maxRequestsPerHour,
      };
      userLimits.set(toolName, rateLimitStatus);
    }

    // Reset window if expired
    if (now.getTime() - rateLimitStatus.windowStart.getTime() > rateLimitStatus.windowDurationMs) {
      rateLimitStatus.requestCount = 0;
      rateLimitStatus.windowStart = now;
    }

    // Check limit
    if (rateLimitStatus.requestCount >= rateLimitStatus.maxRequests) {
      const resetTime = new Date(rateLimitStatus.windowStart.getTime() + rateLimitStatus.windowDurationMs);
      const waitMinutes = Math.ceil((resetTime.getTime() - now.getTime()) / (60 * 1000));
      throw new Error(`Rate limit exceeded for tool '${toolName}'. Try again in ${waitMinutes} minutes.`);
    }
  }

  private updateRateLimit(toolName: string, userId: string): void {
    const userLimits = this.rateLimits.get(userId);
    if (userLimits) {
      const rateLimitStatus = userLimits.get(toolName);
      if (rateLimitStatus) {
        rateLimitStatus.requestCount++;
      }
    }
  }

  private async requestApproval(
    request: ToolCallRequest,
    context: ToolExecutionContext,
    _tool: any
  ): Promise<ToolCallResponse> {
    const approvalRequest: ToolApprovalRequest = {
      id: `approval_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      toolName: request.toolName,
      arguments: request.arguments,
      requestedBy: context.userId,
      workflowId: context.workflowId,
      reason: `Tool '${request.toolName}' requires human approval due to security policy`,
      status: 'pending',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      createdAt: new Date(),
    };

    this.pendingApprovals.set(approvalRequest.id, approvalRequest);

    // In a real implementation, this would notify administrators
    console.log(`Approval requested for tool '${request.toolName}' by user ${context.userId}`);

    return {
      requestId: request.requestId,
      success: false,
      error: 'Human approval required',
      executionTimeMs: 0,
      requiresHumanApproval: true,
      approvalRequestId: approvalRequest.id,
    };
  }

  private async logExecution(
    request: ToolCallRequest,
    context: ToolExecutionContext,
    response: ToolCallResponse,
    executionTimeMs: number
  ): Promise<void> {
    try {
      const logEntry = {
        workflow_id: context.workflowId,
        execution_id: context.executionId,
        agent_action: `tool_call:${request.toolName}`,
        tool_name: request.toolName,
        input_data: request.arguments,
        output_data: response.result,
        execution_time_ms: executionTimeMs,
        success: response.success,
        error_message: response.error,
        human_override: false,
        created_by: context.userId,
      };

      const { error } = await supabase
        .from('agent_execution_logs')
        .insert(logEntry);

      if (error) {
        console.error('Failed to log tool execution:', error);
      }
    } catch (error) {
      console.error('Error logging tool execution:', error);
    }
  }

  async approveToolCall(approvalId: string, approvedBy: string): Promise<boolean> {
    const approval = this.pendingApprovals.get(approvalId);
    if (!approval) {
      return false;
    }

    if (approval.status !== 'pending' || new Date() > approval.expiresAt) {
      return false;
    }

    approval.status = 'approved';
    approval.approvedBy = approvedBy;
    approval.approvedAt = new Date();

    // Execute the approved tool call
    // This would typically be handled by a background job
    console.log(`Tool call approved: ${approval.toolName} by ${approvedBy}`);

    return true;
  }

  async rejectToolCall(approvalId: string, rejectedBy: string): Promise<boolean> {
    const approval = this.pendingApprovals.get(approvalId);
    if (!approval) {
      return false;
    }

    if (approval.status !== 'pending') {
      return false;
    }

    approval.status = 'rejected';
    approval.approvedBy = rejectedBy;
    approval.approvedAt = new Date();

    console.log(`Tool call rejected: ${approval.toolName} by ${rejectedBy}`);

    return true;
  }

  getPendingApprovals(userId?: string): ToolApprovalRequest[] {
    const approvals = Array.from(this.pendingApprovals.values())
      .filter(approval => approval.status === 'pending' && new Date() <= approval.expiresAt);

    if (userId) {
      return approvals.filter(approval => approval.requestedBy === userId);
    }

    return approvals;
  }

  getRateLimitStatus(userId: string, toolName?: string): RateLimitStatus[] {
    const userLimits = this.rateLimits.get(userId);
    if (!userLimits) {
      return [];
    }

    if (toolName) {
      const status = userLimits.get(toolName);
      return status ? [status] : [];
    }

    return Array.from(userLimits.values());
  }
}

// Singleton instance
export const toolExecutor = new ToolExecutor();
