/**
 * Continuous Learning System - Implements machine learning capabilities for agents to improve performance
 * based on human feedback, approval patterns, and workflow outcomes
 */

import { supabase } from '@/integrations/supabase/client';
import { AdvancedAuditTrail } from '@/services/audit/AdvancedAuditTrail';

export interface LearningData {
  agentType: string;
  taskType: string;
  inputData: Record<string, any>;
  aiDecision: any;
  humanFeedback?: {
    approved: boolean;
    comments?: string;
    corrections?: Record<string, any>;
    rating?: number; // 1-5 scale
  };
  outcome: {
    success: boolean;
    metrics?: Record<string, number>;
    timeToComplete?: number;
    errorMessage?: string;
  };
  timestamp: Date;
  contextMetadata: Record<string, any>;
}

export interface LearningPattern {
  id: string;
  agentType: string;
  taskType: string;
  pattern: string;
  confidence: number;
  successRate: number;
  sampleSize: number;
  lastUpdated: Date;
  recommendations: string[];
}

export interface PerformanceMetrics {
  agentType: string;
  taskType: string;
  totalTasks: number;
  successRate: number;
  averageConfidence: number;
  humanApprovalRate: number;
  averageExecutionTime: number;
  improvementTrend: 'improving' | 'stable' | 'declining';
  keyInsights: string[];
  recommendedAdjustments: string[];
}

export class ContinuousLearningSystem {
  private static instance: ContinuousLearningSystem;
  private auditTrail: AdvancedAuditTrail;

  private constructor() {
    this.auditTrail = AdvancedAuditTrail.getInstance();
  }

  public static getInstance(): ContinuousLearningSystem {
    if (!ContinuousLearningSystem.instance) {
      ContinuousLearningSystem.instance = new ContinuousLearningSystem();
    }
    return ContinuousLearningSystem.instance;
  }

  /**
   * Record learning data from agent execution and human feedback
   */
  async recordLearningData(data: LearningData): Promise<void> {
    try {
      // Store in agent_learning table (adapted to existing schema)
      const { error } = await supabase
        .from('agent_learning')
        .insert({
          workflow_id: null, // No specific workflow ID for general learning
          learning_type: `${data.agentType}_${data.taskType}`,
          observation: {
            agentType: data.agentType,
            taskType: data.taskType,
            inputData: data.inputData,
            aiDecision: data.aiDecision,
            humanFeedback: data.humanFeedback || null,
            outcome: data.outcome,
            contextMetadata: data.contextMetadata,
          },
          applied_at: data.timestamp.toISOString(),
          impact_score: data.outcome.success ? 1.0 : 0.0,
          created_at: data.timestamp.toISOString(),
        });

      if (error) {
        throw new Error(`Failed to record learning data: ${error.message}`);
      }

      // Trigger pattern analysis if we have enough new data
      await this.triggerPatternAnalysis(data.agentType, data.taskType);

    } catch (error) {
      console.error('Error recording learning data:', error);
      throw error;
    }
  }

  /**
   * Analyze patterns from approval responses and outcomes
   */
  async analyzeApprovalPatterns(agentType: string, taskType?: string): Promise<LearningPattern[]> {
    try {
      // Query approval data with outcomes
      let query = supabase
        .from('workflow_approvals')
        .select(`
          *,
          audit_events!inner(
            decision_point,
            context,
            metadata
          )
        `)
        .eq('status', 'approved')
        .gte('created_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()); // Last 90 days

      if (taskType) {
        query = query.eq('context_data->>taskType', taskType);
      }

      const { data: approvals, error } = await query;

      if (error) {
        throw new Error(`Failed to query approval patterns: ${error.message}`);
      }

      // Analyze patterns in the data
      const patterns = this.extractPatternsFromApprovals(approvals || [], agentType, taskType);
      
      // Store patterns in database
      await this.storeLearningPatterns(patterns);

      return patterns;

    } catch (error) {
      console.error('Error analyzing approval patterns:', error);
      return [];
    }
  }

  /**
   * Get performance metrics for an agent
   */
  async getPerformanceMetrics(agentType: string, taskType?: string): Promise<PerformanceMetrics> {
    try {
      // Query learning data for the agent (adapted to existing schema)
      let query = supabase
        .from('agent_learning')
        .select('*')
        .like('learning_type', `${agentType}%`)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

      if (taskType) {
        query = query.like('learning_type', `${agentType}_${taskType}%`);
      }

      const { data: learningData, error } = await query;

      if (error) {
        throw new Error(`Failed to query performance metrics: ${error.message}`);
      }

      return this.calculatePerformanceMetrics(learningData || [], agentType, taskType);

    } catch (error) {
      console.error('Error getting performance metrics:', error);
      return this.getDefaultMetrics(agentType, taskType);
    }
  }

  /**
   * Get learning recommendations for improving agent performance
   */
  async getLearningRecommendations(agentType: string, taskType?: string): Promise<string[]> {
    try {
      const metrics = await this.getPerformanceMetrics(agentType, taskType);
      const patterns = await this.getStoredPatterns(agentType, taskType);

      const recommendations = [];

      // Analyze success rate
      if (metrics.successRate < 0.8) {
        recommendations.push('Consider adjusting confidence thresholds to reduce false positives');
        recommendations.push('Review failed cases for common patterns');
      }

      // Analyze approval rate
      if (metrics.humanApprovalRate > 0.3) {
        recommendations.push('High human intervention rate - consider refining decision criteria');
        recommendations.push('Analyze approved cases to identify automation opportunities');
      }

      // Analyze execution time
      if (metrics.averageExecutionTime > 5000) { // 5 seconds
        recommendations.push('Optimize processing time by caching common queries');
        recommendations.push('Consider parallel processing for complex analyses');
      }

      // Pattern-based recommendations
      for (const pattern of patterns) {
        if (pattern.confidence > 0.8 && pattern.successRate > 0.9) {
          recommendations.push(`High-confidence pattern identified: ${pattern.pattern}`);
        }
      }

      return recommendations.length > 0 ? recommendations : ['Performance is within acceptable ranges'];

    } catch (error) {
      console.error('Error getting learning recommendations:', error);
      return ['Unable to generate recommendations due to data access issues'];
    }
  }

  /**
   * Update agent configuration based on learning insights
   */
  async updateAgentConfiguration(
    agentType: string,
    taskType: string,
    adjustments: Record<string, any>
  ): Promise<void> {
    try {
      // Store configuration updates in agent_memory table
      const { error } = await supabase
        .from('agent_memory')
        .insert({
          agent_type: agentType,
          memory_type: 'configuration_update',
          content: {
            taskType,
            adjustments,
            timestamp: new Date().toISOString(),
            reason: 'continuous_learning_optimization',
          },
          importance: 0.8,
          created_at: new Date().toISOString(),
        });

      if (error) {
        throw new Error(`Failed to update agent configuration: ${error.message}`);
      }

      // Log the configuration update to audit trail
      await this.auditTrail.logEvent({
        eventType: 'execution',
        entityType: 'workflow',
        entityId: agentType,
        userId: 'system',
        context: {
          action: 'configuration_update',
          agentType,
          taskType,
          adjustments,
        },
        metadata: {
          timestamp: new Date(),
          success: true,
        },
        compliance: {
          sensitivityLevel: 'medium',
          gdprRelevant: false,
          retentionPeriod: 365,
        },
      });

    } catch (error) {
      console.error('Error updating agent configuration:', error);
      throw error;
    }
  }

  /**
   * Generate learning insights from feedback data
   */
  async generateLearningInsights(agentType: string, days: number = 30): Promise<{
    insights: string[];
    trends: Record<string, number>;
    recommendations: string[];
  }> {
    try {
      const metrics = await this.getPerformanceMetrics(agentType);
      const patterns = await this.getStoredPatterns(agentType);

      const insights = [];
      const trends: Record<string, number> = {};
      const recommendations = [];

      // Generate insights based on metrics
      insights.push(`Agent ${agentType} processed ${metrics.totalTasks} tasks with ${(metrics.successRate * 100).toFixed(1)}% success rate`);
      insights.push(`Human approval required for ${(metrics.humanApprovalRate * 100).toFixed(1)}% of decisions`);
      insights.push(`Average execution time: ${metrics.averageExecutionTime.toFixed(0)}ms`);

      // Calculate trends
      trends.successRate = metrics.successRate;
      trends.approvalRate = metrics.humanApprovalRate;
      trends.executionTime = metrics.averageExecutionTime;
      trends.confidence = metrics.averageConfidence;

      // Generate recommendations
      recommendations.push(...await this.getLearningRecommendations(agentType));

      return { insights, trends, recommendations };

    } catch (error) {
      console.error('Error generating learning insights:', error);
      return {
        insights: ['Unable to generate insights due to data access issues'],
        trends: {},
        recommendations: ['Check system logs for detailed error information'],
      };
    }
  }

  // Private helper methods

  private async triggerPatternAnalysis(agentType: string, taskType: string): Promise<void> {
    // Check if we have enough data points to trigger analysis
    const { count } = await supabase
      .from('agent_learning')
      .select('*', { count: 'exact', head: true })
      .eq('agent_type', agentType)
      .eq('task_type', taskType)
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

    // Trigger analysis if we have at least 10 new data points in the last week
    if ((count || 0) >= 10) {
      await this.analyzeApprovalPatterns(agentType, taskType);
    }
  }

  private extractPatternsFromApprovals(approvals: any[], agentType: string, taskType?: string): LearningPattern[] {
    const patterns: LearningPattern[] = [];

    // Group approvals by common characteristics
    const groupedApprovals = this.groupApprovalsByCharacteristics(approvals);

    for (const [characteristic, group] of Object.entries(groupedApprovals)) {
      if (group.length >= 5) { // Minimum sample size
        const successRate = group.filter(a => a.audit_events?.[0]?.metadata?.success).length / group.length;
        
        patterns.push({
          id: `${agentType}_${taskType || 'all'}_${characteristic}`,
          agentType,
          taskType: taskType || 'all',
          pattern: characteristic,
          confidence: Math.min(0.9, group.length / 20), // Confidence increases with sample size
          successRate,
          sampleSize: group.length,
          lastUpdated: new Date(),
          recommendations: this.generatePatternRecommendations(characteristic, successRate),
        });
      }
    }

    return patterns;
  }

  private groupApprovalsByCharacteristics(approvals: any[]): Record<string, any[]> {
    const groups: Record<string, any[]> = {};

    for (const approval of approvals) {
      const characteristics = [];
      
      // Group by priority
      characteristics.push(`priority_${approval.priority}`);
      
      // Group by approval type
      characteristics.push(`approval_type_${approval.approval_type}`);
      
      // Group by context characteristics
      if (approval.context_data?.riskScore) {
        const riskLevel = approval.context_data.riskScore > 70 ? 'high_risk' : 
                         approval.context_data.riskScore > 40 ? 'medium_risk' : 'low_risk';
        characteristics.push(`risk_${riskLevel}`);
      }

      for (const char of characteristics) {
        if (!groups[char]) groups[char] = [];
        groups[char].push(approval);
      }
    }

    return groups;
  }

  private generatePatternRecommendations(pattern: string, successRate: number): string[] {
    const recommendations = [];

    if (successRate > 0.9) {
      recommendations.push(`High success rate for ${pattern} - consider increasing automation`);
    } else if (successRate < 0.7) {
      recommendations.push(`Low success rate for ${pattern} - review decision criteria`);
    }

    if (pattern.includes('high_risk') && successRate > 0.8) {
      recommendations.push('High-risk cases showing good outcomes - consider adjusting risk thresholds');
    }

    return recommendations;
  }

  private async storeLearningPatterns(patterns: LearningPattern[]): Promise<void> {
    for (const pattern of patterns) {
      const { error } = await supabase
        .from('agent_memory')
        .upsert({
          agent_type: pattern.agentType,
          memory_type: 'learning_pattern',
          content: pattern,
          importance: pattern.confidence,
          created_at: new Date().toISOString(),
        }, {
          onConflict: 'agent_type,memory_type,content->id',
        });

      if (error) {
        console.error('Error storing learning pattern:', error);
      }
    }
  }

  private async getStoredPatterns(agentType: string, taskType?: string): Promise<LearningPattern[]> {
    const { data, error } = await supabase
      .from('agent_memory')
      .select('content')
      .eq('agent_type', agentType)
      .eq('memory_type', 'learning_pattern')
      .gte('created_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString());

    if (error) {
      console.error('Error getting stored patterns:', error);
      return [];
    }

    return (data || [])
      .map(d => d.content as LearningPattern)
      .filter(p => !taskType || p.taskType === taskType || p.taskType === 'all');
  }

  private calculatePerformanceMetrics(learningData: any[], agentType: string, taskType?: string): PerformanceMetrics {
    if (learningData.length === 0) {
      return this.getDefaultMetrics(agentType, taskType);
    }

    const totalTasks = learningData.length;
    const successfulTasks = learningData.filter(d => d.observation?.outcome?.success).length;
    const tasksWithApproval = learningData.filter(d => d.observation?.humanFeedback?.approved !== undefined).length;
    const approvedTasks = learningData.filter(d => d.observation?.humanFeedback?.approved).length;
    const executionTimes = learningData
      .filter(d => d.observation?.outcome?.timeToComplete)
      .map(d => d.observation.outcome.timeToComplete);

    const successRate = successfulTasks / totalTasks;
    const humanApprovalRate = tasksWithApproval > 0 ? approvedTasks / tasksWithApproval : 0;
    const averageExecutionTime = executionTimes.length > 0 ?
      executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length : 0;

    // Calculate confidence from AI decisions
    const confidenceValues = learningData
      .filter(d => d.observation?.aiDecision?.confidence)
      .map(d => d.observation.aiDecision.confidence);
    const averageConfidence = confidenceValues.length > 0 ?
      confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length : 0.5;

    return {
      agentType,
      taskType: taskType || 'all',
      totalTasks,
      successRate,
      averageConfidence,
      humanApprovalRate,
      averageExecutionTime,
      improvementTrend: this.calculateTrend(learningData),
      keyInsights: this.generateKeyInsights(learningData),
      recommendedAdjustments: [],
    };
  }

  private calculateTrend(learningData: any[]): 'improving' | 'stable' | 'declining' {
    if (learningData.length < 10) return 'stable';

    // Split data into two halves and compare success rates
    const midPoint = Math.floor(learningData.length / 2);
    const firstHalf = learningData.slice(0, midPoint);
    const secondHalf = learningData.slice(midPoint);

    const firstHalfSuccess = firstHalf.filter(d => d.outcome?.success).length / firstHalf.length;
    const secondHalfSuccess = secondHalf.filter(d => d.outcome?.success).length / secondHalf.length;

    const improvement = secondHalfSuccess - firstHalfSuccess;

    if (improvement > 0.05) return 'improving';
    if (improvement < -0.05) return 'declining';
    return 'stable';
  }

  private generateKeyInsights(learningData: any[]): string[] {
    const insights = [];

    // Analyze common failure patterns
    const failures = learningData.filter(d => !d.outcome?.success);
    if (failures.length > 0) {
      const commonErrors = this.findCommonErrors(failures);
      if (commonErrors.length > 0) {
        insights.push(`Common failure patterns: ${commonErrors.join(', ')}`);
      }
    }

    // Analyze human feedback patterns
    const feedbackData = learningData.filter(d => d.human_feedback);
    if (feedbackData.length > 0) {
      const avgRating = feedbackData
        .filter(d => d.human_feedback?.rating)
        .reduce((sum, d) => sum + d.human_feedback.rating, 0) / feedbackData.length;
      
      if (avgRating > 0) {
        insights.push(`Average human feedback rating: ${avgRating.toFixed(1)}/5`);
      }
    }

    return insights;
  }

  private findCommonErrors(failures: any[]): string[] {
    const errorCounts: Record<string, number> = {};

    for (const failure of failures) {
      const error = failure.outcome?.errorMessage || 'Unknown error';
      errorCounts[error] = (errorCounts[error] || 0) + 1;
    }

    return Object.entries(errorCounts)
      .filter(([_, count]) => count >= 2)
      .sort(([_, a], [__, b]) => b - a)
      .slice(0, 3)
      .map(([error, _]) => error);
  }

  private getDefaultMetrics(agentType: string, taskType?: string): PerformanceMetrics {
    return {
      agentType,
      taskType: taskType || 'all',
      totalTasks: 0,
      successRate: 0,
      averageConfidence: 0.5,
      humanApprovalRate: 1.0, // Default to requiring approval
      averageExecutionTime: 0,
      improvementTrend: 'stable',
      keyInsights: ['Insufficient data for analysis'],
      recommendedAdjustments: ['Collect more performance data'],
    };
  }
}

export const continuousLearningSystem = ContinuousLearningSystem.getInstance();
