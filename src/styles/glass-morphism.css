/* Glass Morphism Utilities for HireLogix
 * Usage:
 * .glass-sm - Light blur for menus, tooltips, small panels
 * .glass-md - Medium blur for navbars, popovers, toasts  
 * .glass-lg - Heavy blur for modals, drawers, floating panels
 * .glass-strong - Stronger opacity for better readability
 * .glass-inset - Inner bevel effect for depth
 * 
 * Disable globally: Add data-glass="off" to root element
 */

:root {
  /* Blur intensity scales */
  --glass-blur-xs: 6px;
  --glass-blur-sm: 8px;
  --glass-blur-md: 12px;
  --glass-blur-lg: 20px;
  --glass-blur-xl: 24px;

  /* Opacity levels */
  --glass-opacity: 0.72;
  --glass-opacity-strong: 0.85;
  --glass-border-opacity: 0.15;

  /* Light mode glass tokens */
  --glass-bg: rgba(255, 255, 255, var(--glass-opacity));
  --glass-bg-strong: rgba(255, 255, 255, var(--glass-opacity-strong));
  --glass-border: rgba(255, 255, 255, var(--glass-border-opacity));
  --glass-fallback-bg: rgba(255, 255, 255, 0.95);
  
  /* Visual enhancement */
  --glass-saturate: 150%;
  --glass-contrast: 100%;
  --glass-brightness: 100%;
  --glass-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.04);
  --glass-shadow-md: 0 8px 24px rgba(0, 0, 0, 0.06);
  --glass-shadow-lg: 0 16px 48px rgba(0, 0, 0, 0.08);
}

/* Dark mode overrides */
.dark {
  --glass-opacity: 0.50;
  --glass-opacity-strong: 0.72;
  --glass-border-opacity: 0.10;
  
  --glass-bg: rgba(17, 24, 39, var(--glass-opacity));
  --glass-bg-strong: rgba(17, 24, 39, var(--glass-opacity-strong));
  --glass-border: rgba(255, 255, 255, var(--glass-border-opacity));
  --glass-fallback-bg: rgba(17, 24, 39, 0.92);
  
  --glass-saturate: 120%;
  --glass-contrast: 105%;
  --glass-brightness: 98%;
  --glass-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.20);
  --glass-shadow-md: 0 8px 24px rgba(0, 0, 0, 0.30);
  --glass-shadow-lg: 0 16px 48px rgba(0, 0, 0, 0.40);
}

/* Base glass styles */
.glass,
.glass-sm,
.glass-md,
.glass-lg,
.glass-xl {
  --current-blur: var(--glass-blur-md);
  
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(var(--current-blur)) 
                           saturate(var(--glass-saturate)) 
                           contrast(var(--glass-contrast))
                           brightness(var(--glass-brightness));
  backdrop-filter: blur(var(--current-blur)) 
                   saturate(var(--glass-saturate)) 
                   contrast(var(--glass-contrast))
                   brightness(var(--glass-brightness));
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow-sm), var(--glass-shadow-md);
  
  /* Performance optimizations */
  will-change: backdrop-filter;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* Size variants */
.glass-sm { 
  --current-blur: var(--glass-blur-sm);
  box-shadow: var(--glass-shadow-sm);
}

.glass-md { 
  --current-blur: var(--glass-blur-md);
  box-shadow: var(--glass-shadow-sm), var(--glass-shadow-md);
}

.glass-lg { 
  --current-blur: var(--glass-blur-lg);
  box-shadow: var(--glass-shadow-md), var(--glass-shadow-lg);
}

.glass-xl {
  --current-blur: var(--glass-blur-xl);
  box-shadow: var(--glass-shadow-md), var(--glass-shadow-lg);
}

/* Modifiers */
.glass-strong {
  background: var(--glass-bg-strong);
}

.glass-inset {
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05),
    var(--glass-shadow-sm),
    var(--glass-shadow-md);
}

/* Interactive states */
.glass-interactive:hover {
  background: var(--glass-bg-strong);
  transition: background-color 200ms ease;
}

.glass-interactive:active {
  transform: translateZ(0) scale(0.99);
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(1px)) {
  .glass,
  .glass-sm,
  .glass-md,
  .glass-lg,
  .glass-xl {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: var(--glass-fallback-bg);
  }
}

/* Global kill switch */
[data-glass="off"] .glass,
[data-glass="off"] .glass-sm,
[data-glass="off"] .glass-md,
[data-glass="off"] .glass-lg,
[data-glass="off"] .glass-xl {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  background: var(--glass-fallback-bg) !important;
}

/* Modal/drawer backdrop blur */
.glass-backdrop {
  background: rgba(0, 0, 0, 0.25);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.dark .glass-backdrop {
  background: rgba(0, 0, 0, 0.50);
}

/* Ensure focus states remain visible */
.glass :focus-visible,
.glass-sm :focus-visible,
.glass-md :focus-visible,
.glass-lg :focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Prevent text selection issues on glass */
.glass ::selection,
.glass-sm ::selection,
.glass-md ::selection,
.glass-lg ::selection {
  background: hsl(var(--primary) / 0.3);
}

/* Animation for glass elements entering view */
@keyframes glass-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(var(--current-blur));
    -webkit-backdrop-filter: blur(var(--current-blur));
  }
}

.glass-animate-in {
  animation: glass-fade-in 300ms ease-out;
}
