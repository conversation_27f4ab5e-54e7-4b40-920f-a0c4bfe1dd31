/* Enhanced Tab Indicator Styles */

/* 1. Glowing Underline - Modern and elegant */
.tab-indicator-underline {
  will-change: transform, width;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1), width 300ms cubic-bezier(0.4, 0, 0.2, 1);
  height: 3px !important;
  bottom: -1px !important;
  top: auto !important;
  background: linear-gradient(90deg, 
    transparent 0%,
    hsl(var(--primary) / 0.5) 10%,
    hsl(var(--primary)) 50%,
    hsl(var(--primary) / 0.5) 90%,
    transparent 100%
  );
  border-radius: 2px;
  z-index: 20;
  opacity: 1;
  box-shadow: 
    0 0 15px hsl(var(--primary) / 0.6),
    0 0 30px hsl(var(--primary) / 0.3),
    0 0 45px hsl(var(--primary) / 0.2);
}

/* Add a subtle glow animation */
@keyframes underline-glow {
  0%, 100% {
    box-shadow: 
      0 0 15px hsl(var(--primary) / 0.6),
      0 0 30px hsl(var(--primary) / 0.3),
      0 0 45px hsl(var(--primary) / 0.2);
  }
  50% {
    box-shadow: 
      0 0 20px hsl(var(--primary) / 0.8),
      0 0 40px hsl(var(--primary) / 0.4),
      0 0 60px hsl(var(--primary) / 0.3);
  }
}

.tab-indicator-underline {
  animation: underline-glow 2s ease-in-out infinite;
}

/* 2. Floating Pill - Stands out above the tab */
.tab-indicator-pill {
  will-change: transform, width, height;
  transition: 
    transform 420ms var(--ease-spring), 
    width 420ms var(--ease-spring),
    height 320ms var(--ease-spring);
  background: hsl(var(--primary));
  box-shadow: 
    0 4px 20px hsl(var(--primary) / 0.4),
    0 2px 10px hsl(var(--primary) / 0.2),
    inset 0 1px 0 hsl(var(--primary-foreground) / 0.1);
  transform: scale(1.05);
  z-index: -1;
  border-radius: 12px;
}

/* 3. Neon Outline - Cyberpunk style */
.tab-indicator-neon {
  will-change: transform, width;
  transition: transform 420ms var(--ease-spring), width 420ms var(--ease-spring);
  background: transparent;
  border: 2px solid hsl(var(--primary));
  box-shadow: 
    0 0 10px hsl(var(--primary) / 0.8),
    0 0 20px hsl(var(--primary) / 0.6),
    0 0 30px hsl(var(--primary) / 0.4),
    inset 0 0 10px hsl(var(--primary) / 0.2);
  border-radius: 12px;
  animation: neon-pulse 2s ease-in-out infinite;
}

@keyframes neon-pulse {
  0%, 100% {
    box-shadow: 
      0 0 10px hsl(var(--primary) / 0.8),
      0 0 20px hsl(var(--primary) / 0.6),
      0 0 30px hsl(var(--primary) / 0.4),
      inset 0 0 10px hsl(var(--primary) / 0.2);
  }
  50% {
    box-shadow: 
      0 0 15px hsl(var(--primary) / 1),
      0 0 30px hsl(var(--primary) / 0.8),
      0 0 45px hsl(var(--primary) / 0.6),
      inset 0 0 15px hsl(var(--primary) / 0.3);
  }
}

/* 4. Morphing Blob - Organic and playful */
.tab-indicator-blob {
  will-change: transform, width;
  transition: 
    transform 520ms var(--ease-spring), 
    width 520ms var(--ease-spring);
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.9),
    hsl(var(--primary) / 0.7)
  );
  border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
  animation: blob-morph 8s ease-in-out infinite;
  filter: blur(0.5px);
  opacity: 0.15;
}

@keyframes blob-morph {
  0%, 100% {
    border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
    transform: rotate(0deg) scale(1);
  }
  33% {
    border-radius: 70% 30% 50% 50% / 30% 30% 70% 70%;
    transform: rotate(120deg) scale(1.05);
  }
  66% {
    border-radius: 30% 70% 30% 70% / 50% 60% 40% 60%;
    transform: rotate(240deg) scale(0.95);
  }
}

/* 5. Gradient Wave - Smooth and modern */
.tab-indicator-wave {
  will-change: transform, width;
  transition: transform 420ms var(--ease-spring), width 420ms var(--ease-spring);
  background: linear-gradient(90deg,
    hsl(var(--primary) / 0.1),
    hsl(var(--primary) / 0.3),
    hsl(var(--primary) / 0.5),
    hsl(var(--primary) / 0.3),
    hsl(var(--primary) / 0.1)
  );
  background-size: 200% 100%;
  animation: wave-flow 3s ease-in-out infinite;
  border-radius: 12px;
  backdrop-filter: blur(8px);
}

@keyframes wave-flow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 6. Magnetic Border - Follows with elastic effect */
.tab-indicator-magnetic {
  will-change: transform, width, height;
  transition: 
    transform 420ms cubic-bezier(0.68, -0.55, 0.265, 1.55), 
    width 420ms cubic-bezier(0.68, -0.55, 0.265, 1.55),
    height 420ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  background: transparent;
  border: 2px solid hsl(var(--primary) / 0.6);
  border-radius: 12px;
}

.tab-indicator-magnetic::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, 
    hsl(var(--primary)),
    hsl(var(--primary) / 0.5),
    transparent,
    hsl(var(--primary) / 0.5),
    hsl(var(--primary))
  );
  border-radius: 12px;
  opacity: 0;
  animation: magnetic-pulse 2s linear infinite;
}

@keyframes magnetic-pulse {
  0%, 100% {
    opacity: 0;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}

/* 7. Laser Focus - Sharp and precise */
.tab-indicator-laser {
  will-change: transform, width;
  transition: transform 320ms var(--ease-snap), width 320ms var(--ease-snap);
  height: 2px !important;
  bottom: -1px;
  top: auto !important;
  background: linear-gradient(90deg,
    transparent,
    hsl(var(--primary) / 0.4),
    hsl(var(--primary)),
    hsl(var(--primary)),
    hsl(var(--primary) / 0.4),
    transparent
  );
  box-shadow: 
    0 0 10px hsl(var(--primary)),
    0 0 20px hsl(var(--primary) / 0.5);
}

.tab-indicator-laser::before,
.tab-indicator-laser::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background: hsl(var(--primary));
  border-radius: 50%;
  top: -1px;
  box-shadow: 0 0 10px hsl(var(--primary));
  animation: laser-dot-pulse 1s ease-in-out infinite;
}

.tab-indicator-laser::before {
  left: -2px;
}

.tab-indicator-laser::after {
  right: -2px;
  animation-delay: 0.5s;
}

@keyframes laser-dot-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
}

/* 8. Gradient Orb - Sophisticated with depth */
.tab-indicator-orb {
  will-change: transform, width;
  transition: transform 420ms var(--ease-spring), width 420ms var(--ease-spring);
  background: radial-gradient(
    ellipse at center,
    hsl(var(--primary) / 0.3),
    hsl(var(--primary) / 0.15),
    transparent
  );
  border-radius: 50%;
  filter: blur(2px);
  transform: scale(1.2);
  animation: orb-breathe 3s ease-in-out infinite;
}

@keyframes orb-breathe {
  0%, 100% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.4);
    opacity: 1;
  }
}

/* 9. Circuit Board - Tech/Digital theme */
.tab-indicator-circuit {
  will-change: transform, width;
  transition: transform 420ms var(--ease-spring), width 420ms var(--ease-spring);
  background: 
    repeating-linear-gradient(
      90deg,
      hsl(var(--primary) / 0.1),
      hsl(var(--primary) / 0.1) 2px,
      transparent 2px,
      transparent 4px
    ),
    repeating-linear-gradient(
      0deg,
      hsl(var(--primary) / 0.1),
      hsl(var(--primary) / 0.1) 2px,
      transparent 2px,
      transparent 4px
    );
  border: 1px solid hsl(var(--primary) / 0.3);
  border-radius: 8px;
  position: relative;
}

.tab-indicator-circuit::before {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: hsl(var(--primary));
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px hsl(var(--primary));
  animation: circuit-pulse 1.5s ease-in-out infinite;
}

@keyframes circuit-pulse {
  0%, 100% {
    box-shadow: 0 0 10px hsl(var(--primary));
  }
  50% {
    box-shadow: 0 0 20px hsl(var(--primary)), 0 0 30px hsl(var(--primary) / 0.5);
  }
}

/* Utility classes for easy switching */
.tabs-indicator-style-underline .tab-indicator { @apply tab-indicator-underline; }
.tabs-indicator-style-pill .tab-indicator { @apply tab-indicator-pill; }
.tabs-indicator-style-neon .tab-indicator { @apply tab-indicator-neon; }
.tabs-indicator-style-blob .tab-indicator { @apply tab-indicator-blob; }
.tabs-indicator-style-wave .tab-indicator { @apply tab-indicator-wave; }
.tabs-indicator-style-magnetic .tab-indicator { @apply tab-indicator-magnetic; }
.tabs-indicator-style-laser .tab-indicator { @apply tab-indicator-laser; }
.tabs-indicator-style-orb .tab-indicator { @apply tab-indicator-orb; }
.tabs-indicator-style-circuit .tab-indicator { @apply tab-indicator-circuit; }
