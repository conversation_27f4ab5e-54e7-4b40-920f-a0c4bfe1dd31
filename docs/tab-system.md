# RMS-Refresh Tab System

## Overview

The RMS-Refresh application uses a standardized tab system built around the `EnhancedTabs` component and `useTabNavigation` hook. This system provides consistent tab behavior, URL persistence, keyboard navigation, and performance optimizations across all pages.

## ✅ Migration Complete

All major pages have been successfully migrated to the unified tab system:

### Migrated Pages

1. **Settings.tsx** - Profile, Account, Notifications, Security, Preferences
2. **Candidates.tsx** - All Candidates, Active Pipeline, New Applications, Archived  
3. **Jobs.tsx** - All Jobs, Active, Draft, Closed
4. **Communications.tsx** - Inbox, Compose, Templates, Contacts
5. **AIWorkflows.tsx** - Canvas, Manage, Templates, Analytics, Integrations, etc.
6. **Analytics.tsx** - Overview, Applications, Candidates, Performance
7. **Integrations.tsx** - Connected, Available, Settings, Logs
8. **Search.tsx** - Candidates, Jobs, All Results
9. **Tasks.tsx** - All, Pending, In Progress, Completed

## Core Components

### EnhancedTabs Component

The main tab component that provides:
- Consistent styling with glass morphism effects
- Multiple variants (navigation, pills, underline)
- Responsive design with mobile optimization
- Accessibility features (ARIA labels, keyboard navigation)
- Integration with routing and state management

### useTabNavigation Hook

Manages tab state and navigation:
- URL-based persistence (e.g., `/candidates/active`)
- localStorage sync for user preferences
- Automatic route handling
- Tab validation and fallback logic

## Key Features

### 1. Performance Optimizations

#### Lazy Loading
- **AIWorkflows**: 15 tab components lazy load on-demand
- **Bundle Reduction**: Main bundle reduced from 3.15 MB to 2.92 MB
- **Component Chunks**: Each workflow component in separate 6-42 KB chunks
- **Preloading**: `usePreloadTab` hook for hover-based preloading

#### Components:
- `LazyTabPanel` - Wrapper for lazy loading tab content
- `withLazyLoading` - HOC for wrapping components

### 2. Keyboard Navigation

#### Shortcuts:
- **Ctrl/Cmd + Tab**: Navigate to next tab
- **Ctrl/Cmd + Shift + Tab**: Navigate to previous tab  
- **Alt + [1-9]**: Jump directly to tab by index
- **Arrow Keys**: Navigate when tab list is focused
- **Home/End**: Jump to first/last tab

#### Implementation:
- `useTabKeyboardNavigation` hook manages all interactions
- Platform-aware (Mac vs Windows key combinations)
- Automatically integrated into `EnhancedTabs`

### 3. Loading States & Skeletons

#### Skeleton Components:
- `TabListSkeleton` - For table/list layouts
- `TabFormSkeleton` - For form/settings content
- `TabGridSkeleton` - For card grid layouts
- `TabChartSkeleton` - For analytics/dashboard content
- `TabDetailSkeleton` - For profile/detail views
- `TabEmptyState` - For empty tab content

### 4. Enhanced UX Features

#### Visual Enhancements:
- Glass morphism effects with backdrop blur
- Smooth animations and transitions
- Contextual loading states
- Badge support for counts and notifications
- Responsive breakpoint handling

#### State Management:
- URL synchronization with React Router
- localStorage persistence for user preferences
- Real-time state updates
- Error boundary protection

## Usage Examples

### Basic Tab Implementation

```tsx
import { EnhancedTabs, TabPanel } from "@/components/ui/enhanced-tabs";
import { useTabNavigation } from "@/hooks/useTabNavigation";

const MyPage = () => {
  const { activeTab, handleTabChange } = useTabNavigation({
    basePath: "/my-page",
    defaultTab: "overview",
    validTabs: ["overview", "details", "settings"],
    persistToLocalStorage: true,
    storageKey: "my-page-tab",
  });

  const tabs = [
    { value: "overview", label: "Overview", icon: Home },
    { value: "details", label: "Details", icon: Info },
    { value: "settings", label: "Settings", icon: Settings },
  ];

  return (
    <EnhancedTabs
      tabs={tabs}
      value={activeTab}
      onValueChange={handleTabChange}
      variant="navigation"
      indicatorStyle="underline"
    >
      <TabPanel value="overview">
        <OverviewContent />
      </TabPanel>
      <TabPanel value="details">
        <DetailsContent />
      </TabPanel>
      <TabPanel value="settings">
        <SettingsContent />
      </TabPanel>
    </EnhancedTabs>
  );
};
```

### Lazy Loading Implementation

```tsx
import { LazyTabPanel } from "@/components/ui/enhanced-tabs";

const LazyComponent = lazy(() => import("./HeavyComponent"));

<LazyTabPanel value="heavy-tab" fallback={<TabChartSkeleton />}>
  <LazyComponent />
</LazyTabPanel>
```

## Technical Architecture

### File Structure
```
src/components/ui/enhanced-tabs/
├── index.ts                    # Main exports
├── EnhancedTabs.tsx           # Core tab component
├── TabPanel.tsx               # Tab content wrapper
├── LazyTabPanel.tsx           # Lazy loading wrapper
├── skeletons/                 # Loading state components
└── hooks/
    ├── useTabNavigation.ts    # Navigation logic
    ├── useTabKeyboard.ts      # Keyboard handling
    └── usePreloadTab.ts       # Preloading logic
```

### Integration Points
- **React Router**: URL-based tab persistence
- **localStorage**: User preference storage
- **Supabase**: Real-time data integration
- **shadcn/ui**: Base component styling
- **Tailwind CSS**: Responsive design utilities

## Best Practices

### 1. Tab Organization
- Use descriptive, consistent tab labels
- Limit tabs to 7±2 for optimal UX
- Group related functionality logically
- Provide clear visual hierarchy

### 2. Performance
- Implement lazy loading for heavy components
- Use appropriate skeleton components
- Preload commonly accessed tabs
- Optimize bundle splitting

### 3. Accessibility
- Provide proper ARIA labels
- Support keyboard navigation
- Maintain focus management
- Use semantic HTML structure

### 4. State Management
- Persist important tab states to URL
- Use localStorage for user preferences
- Handle edge cases and invalid states
- Provide sensible defaults

## Migration Benefits

1. **Consistency**: Unified tab behavior across all pages
2. **Performance**: Reduced bundle size and lazy loading
3. **UX**: Enhanced keyboard navigation and loading states
4. **Maintainability**: Centralized tab logic and styling
5. **Accessibility**: Improved screen reader and keyboard support
6. **Developer Experience**: Simplified tab implementation

## Future Enhancements

- Tab reordering and customization
- Advanced preloading strategies
- Enhanced mobile gestures
- Tab grouping and nesting
- Analytics integration for tab usage

---

*This documentation reflects the current state of the RMS-Refresh tab system. All migrations are complete and the system is production-ready.*
